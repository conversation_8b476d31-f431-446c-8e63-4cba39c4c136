Note: you need yarn for this repo.
Done! created a new project under ./fishtouch-store

visit your project:
  cd fishtouch-store


  To start the Next.js development server, run:
    yarn web
    
  To start Expo Go for mobile development, run:
    yarn native

  You can also create Expo development builds by doing:
  
    cd apps/expo 
    then:
    yarn ios 
    or...
    yarn android

  Be sure to replace yourprojectsname in app.json with the uid you'd like for your app.


        ████████████          
      ██            ██        
    ██            ██  ██████  
  ██        ██              ██
  ██                  ████████
  ██                        ██
  ██                    ████  
██                    ██      
██                    ██      