"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_packages_app_features_inventory_database_web_ts"],{

/***/ "(app-pages-browser)/../../packages/app/features/inventory/database.web.ts":
/*!*************************************************************!*\
  !*** ../../packages/app/features/inventory/database.web.ts ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearDatabase: function() { return /* binding */ clearDatabase; },\n/* harmony export */   closeDatabase: function() { return /* binding */ closeDatabase; },\n/* harmony export */   executeQuery: function() { return /* binding */ executeQuery; },\n/* harmony export */   executeQueryFirst: function() { return /* binding */ executeQueryFirst; },\n/* harmony export */   executeTransaction: function() { return /* binding */ executeTransaction; },\n/* harmony export */   executeUpdate: function() { return /* binding */ executeUpdate; },\n/* harmony export */   getDatabase: function() { return /* binding */ getDatabase; },\n/* harmony export */   initializeDatabase: function() { return /* binding */ initializeDatabase; },\n/* harmony export */   seedDatabase: function() { return /* binding */ seedDatabase; }\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/../../packages/app/features/inventory/types.ts\");\n// Web-compatible database implementation using localStorage for demo purposes\n// In a real app, you'd use IndexedDB or connect to a backend API\n\n// Simple in-memory database for web demo\nclass WebDatabase {\n    async execAsync(sql) {\n        // Handle CREATE TABLE statements\n        if (sql.includes(\"CREATE TABLE\")) {\n            const tableMatch = sql.match(/CREATE TABLE (?:IF NOT EXISTS )?(\\w+)/);\n            if (tableMatch) {\n                const tableName = tableMatch[1];\n                if (!this.data[tableName]) {\n                    this.data[tableName] = [];\n                    this.nextId[tableName] = 1;\n                }\n            }\n        }\n    // Handle other SQL statements as needed\n    }\n    async getAllAsync(sql) {\n        let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n        // Simple query parsing for demo\n        if (sql.includes(\"SELECT * FROM products\")) {\n            return this.data.products || [];\n        }\n        if (sql.includes(\"SELECT * FROM inventory\")) {\n            return this.data.inventory || [];\n        }\n        if (sql.includes(\"SELECT * FROM transactions\")) {\n            return this.data.transactions || [];\n        }\n        if (sql.includes(\"SELECT * FROM stock_alerts\")) {\n            return this.data.stock_alerts || [];\n        }\n        if (sql.includes(\"SELECT * FROM packages\")) {\n            return this.data.packages || [];\n        }\n        if (sql.includes(\"SELECT * FROM batches\")) {\n            return this.data.batches || [];\n        }\n        if (sql.includes(\"SELECT * FROM shipments\")) {\n            return this.data.shipments || [];\n        }\n        // Handle COUNT queries\n        if (sql.includes(\"COUNT(*)\")) {\n            if (sql.includes(\"FROM products\")) {\n                return [\n                    {\n                        count: (this.data.products || []).length\n                    }\n                ];\n            }\n        }\n        return [];\n    }\n    async getFirstAsync(sql) {\n        let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n        const results = await this.getAllAsync(sql, params);\n        return results.length > 0 ? results[0] : null;\n    }\n    async runAsync(sql) {\n        let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n        // Handle INSERT statements\n        if (sql.includes(\"INSERT INTO\")) {\n            const tableMatch = sql.match(/INSERT INTO (\\w+)/);\n            if (tableMatch) {\n                const tableName = tableMatch[1];\n                if (!this.data[tableName]) {\n                    this.data[tableName] = [];\n                    this.nextId[tableName] = 1;\n                }\n                const id = this.nextId[tableName]++;\n                const newRecord = {\n                    id,\n                    ...this.parseInsertValues(sql, params)\n                };\n                this.data[tableName].push(newRecord);\n                return {\n                    lastInsertRowId: id,\n                    changes: 1\n                };\n            }\n        }\n        // Handle UPDATE statements\n        if (sql.includes(\"UPDATE\")) {\n            return {\n                changes: 1\n            };\n        }\n        // Handle DELETE statements\n        if (sql.includes(\"DELETE\")) {\n            return {\n                changes: 1\n            };\n        }\n        return {\n            changes: 0\n        };\n    }\n    async closeAsync() {\n    // Nothing to close for in-memory database\n    }\n    parseInsertValues(sql, params) {\n        // Simple parsing for demo - in real implementation you'd parse SQL properly\n        const record = {};\n        // For demo purposes, create some sample data based on table\n        if (sql.includes(\"products\")) {\n            record.name = params[0] || \"Sample Product\";\n            record.sku = params[1] || \"SKU001\";\n            record.description = params[2] || \"Sample description\";\n            record.unit = params[3] || \"piece\";\n            record.min_stock_level = params[4] || 10;\n            record.created_at = new Date().toISOString();\n        }\n        return record;\n    }\n    constructor(){\n        this.data = {};\n        this.nextId = {};\n    }\n}\nlet db = null;\nconst initializeDatabase = async ()=>{\n    if (db) {\n        return db;\n    }\n    try {\n        db = new WebDatabase();\n        // Create tables\n        await db.execAsync(_types__WEBPACK_IMPORTED_MODULE_0__.CREATE_TABLES_SQL);\n        console.log(\"Web database initialized successfully\");\n        return db;\n    } catch (error) {\n        console.error(\"Failed to initialize web database:\", error);\n        throw error;\n    }\n};\nconst getDatabase = async ()=>{\n    if (!db) {\n        return await initializeDatabase();\n    }\n    return db;\n};\nconst closeDatabase = async ()=>{\n    if (db) {\n        await db.closeAsync();\n        db = null;\n    }\n};\n// Utility function to execute queries with error handling\nconst executeQuery = async function(query) {\n    let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n    try {\n        const database = await getDatabase();\n        const result = await database.getAllAsync(query, params);\n        return result;\n    } catch (error) {\n        console.error(\"Query execution failed:\", error);\n        console.error(\"Query:\", query);\n        console.error(\"Params:\", params);\n        throw error;\n    }\n};\n// Utility function to execute single row queries\nconst executeQueryFirst = async function(query) {\n    let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n    try {\n        const database = await getDatabase();\n        const result = await database.getFirstAsync(query, params);\n        return result;\n    } catch (error) {\n        console.error(\"Query execution failed:\", error);\n        console.error(\"Query:\", query);\n        console.error(\"Params:\", params);\n        throw error;\n    }\n};\n// Utility function to execute insert/update/delete queries\nconst executeUpdate = async function(query) {\n    let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n    try {\n        const database = await getDatabase();\n        const result = await database.runAsync(query, params);\n        return result;\n    } catch (error) {\n        console.error(\"Update execution failed:\", error);\n        console.error(\"Query:\", query);\n        console.error(\"Params:\", params);\n        throw error;\n    }\n};\n// Transaction wrapper for multiple operations\nconst executeTransaction = async (operations)=>{\n    const database = await getDatabase();\n    try {\n        const result = await operations(database);\n        return result;\n    } catch (error) {\n        console.error(\"Transaction failed:\", error);\n        throw error;\n    }\n};\n// Database seeding function for development/testing\nconst seedDatabase = async ()=>{\n    try {\n        console.log(\"Seeding web database with sample data...\");\n        // Create sample data directly in the web database\n        const database = await getDatabase();\n        // Sample products\n        const sampleProducts = [\n            {\n                id: 1,\n                name: \"Fish Food Premium\",\n                sku: \"FF001\",\n                unit: \"kg\",\n                min_stock_level: 10,\n                created_at: new Date().toISOString()\n            },\n            {\n                id: 2,\n                name: \"Aquarium Filter\",\n                sku: \"AF001\",\n                unit: \"piece\",\n                min_stock_level: 5,\n                created_at: new Date().toISOString()\n            },\n            {\n                id: 3,\n                name: \"Water Conditioner\",\n                sku: \"WC001\",\n                unit: \"liter\",\n                min_stock_level: 20,\n                created_at: new Date().toISOString()\n            },\n            {\n                id: 4,\n                name: \"LED Light Strip\",\n                sku: \"LS001\",\n                unit: \"piece\",\n                min_stock_level: 3,\n                created_at: new Date().toISOString()\n            },\n            {\n                id: 5,\n                name: \"Gravel Substrate\",\n                sku: \"GS001\",\n                unit: \"kg\",\n                min_stock_level: 50,\n                created_at: new Date().toISOString()\n            }\n        ];\n        // Directly populate the data for web demo\n        database.data.products = sampleProducts;\n        database.nextId.products = 6;\n        console.log(\"Web database seeded successfully\");\n    } catch (error) {\n        console.error(\"Failed to seed web database:\", error);\n        throw error;\n    }\n};\n// Clear all data (for testing purposes)\nconst clearDatabase = async ()=>{\n    try {\n        const database = await getDatabase();\n        database.data = {};\n        database.nextId = {};\n        console.log(\"Web database cleared successfully\");\n    } catch (error) {\n        console.error(\"Failed to clear web database:\", error);\n        throw error;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/app/features/inventory/database.web.ts\n"));

/***/ })

}]);