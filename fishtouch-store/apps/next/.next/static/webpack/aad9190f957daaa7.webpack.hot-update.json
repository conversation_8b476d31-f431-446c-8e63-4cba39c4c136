{"c": ["webpack"], "r": ["pages/pages-example"], "m": ["../../node_modules/@react-native/assets-registry/registry.js", "../../node_modules/@tamagui/button/dist/esm/Button.mjs", "../../node_modules/@tamagui/create-context/dist/esm/create-context.mjs", "../../node_modules/@tamagui/font-size/dist/esm/getFontSize.mjs", "../../node_modules/@tamagui/get-button-sized/dist/esm/index.mjs", "../../node_modules/@tamagui/get-token/dist/esm/index.mjs", "../../node_modules/@tamagui/helpers-icon/dist/esm/themed.mjs", "../../node_modules/@tamagui/helpers-tamagui/dist/esm/index.mjs", "../../node_modules/@tamagui/helpers-tamagui/dist/esm/prevent.mjs", "../../node_modules/@tamagui/helpers-tamagui/dist/esm/useCurrentColor.mjs", "../../node_modules/@tamagui/helpers-tamagui/dist/esm/useGetThemedIcon.mjs", "../../node_modules/@tamagui/lucide-icons/dist/esm/icons/ChevronDown.mjs", "../../node_modules/@tamagui/lucide-icons/dist/esm/icons/ChevronUp.mjs", "../../node_modules/@tamagui/next-theme/dist/esm/useTheme.js", "../../node_modules/@tamagui/remove-scroll/dist/esm/RemoveScroll.mjs", "../../node_modules/@tamagui/remove-scroll/dist/esm/index.mjs", "../../node_modules/@tamagui/scroll-view/dist/esm/ScrollView.mjs", "../../node_modules/@tamagui/separator/dist/esm/Separator.mjs", "../../node_modules/@tamagui/sheet/dist/esm/Sheet.mjs", "../../node_modules/@tamagui/sheet/dist/esm/SheetContext.mjs", "../../node_modules/@tamagui/sheet/dist/esm/SheetImplementationCustom.mjs", "../../node_modules/@tamagui/sheet/dist/esm/SheetScrollView.mjs", "../../node_modules/@tamagui/sheet/dist/esm/constants.mjs", "../../node_modules/@tamagui/sheet/dist/esm/contexts.mjs", "../../node_modules/@tamagui/sheet/dist/esm/createSheet.mjs", "../../node_modules/@tamagui/sheet/dist/esm/helpers.mjs", "../../node_modules/@tamagui/sheet/dist/esm/useSheetController.mjs", "../../node_modules/@tamagui/sheet/dist/esm/useSheetOffscreenSize.mjs", "../../node_modules/@tamagui/sheet/dist/esm/useSheetOpenState.mjs", "../../node_modules/@tamagui/sheet/dist/esm/useSheetProviderProps.mjs", "../../node_modules/@tamagui/stacks/dist/esm/NestingContext.mjs", "../../node_modules/@tamagui/text/dist/esm/Headings.mjs", "../../node_modules/@tamagui/text/dist/esm/Paragraph.mjs", "../../node_modules/@tamagui/text/dist/esm/wrapChildrenInText.mjs", "../../node_modules/@tamagui/use-did-finish-ssr/dist/esm/ClientOnly.mjs", "../../node_modules/@tamagui/use-did-finish-ssr/dist/esm/index.mjs", "../../node_modules/base64-js/index.js", "../../node_modules/buffer/index.js", "../../node_modules/get-nonce/dist/es2015/index.js", "../../node_modules/ieee754/index.js", "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fapps%2Fnext%2Fpages%2Fpages-example%2Findex.tsx&page=%2Fpages-example!", "../../node_modules/react-native-svg/lib/module/ReactNativeSVG.web.js", "../../node_modules/react-native-svg/lib/module/deprecated.js", "../../node_modules/react-native-svg/lib/module/elements.web.js", "../../node_modules/react-native-svg/lib/module/index.js", "../../node_modules/react-native-svg/lib/module/lib/Matrix2D.js", "../../node_modules/react-native-svg/lib/module/lib/SvgTouchableMixin.js", "../../node_modules/react-native-svg/lib/module/lib/extract/extractTransform.js", "../../node_modules/react-native-svg/lib/module/lib/extract/transform.js", "../../node_modules/react-native-svg/lib/module/lib/extract/transformToRn.js", "../../node_modules/react-native-svg/lib/module/lib/extract/types.js", "../../node_modules/react-native-svg/lib/module/lib/resolve.js", "../../node_modules/react-native-svg/lib/module/lib/resolveAssetUri.js", "../../node_modules/react-native-svg/lib/module/utils/fetchData.js", "../../node_modules/react-native-svg/lib/module/web/WebShape.js", "../../node_modules/react-native-svg/lib/module/web/utils/convertInt32Color.js", "../../node_modules/react-native-svg/lib/module/web/utils/index.js", "../../node_modules/react-native-svg/lib/module/web/utils/prepare.js", "../../node_modules/react-native-svg/lib/module/xml.js", "../../node_modules/react-native-svg/lib/module/xmlTags.js", "../../node_modules/react-native-web/dist/exports/Keyboard/index.js", "../../node_modules/react-native-web/dist/exports/Linking/index.js", "../../node_modules/react-native-web/dist/exports/Touchable/index.js", "../../node_modules/react-remove-scroll-bar/dist/es2015/component.js", "../../node_modules/react-remove-scroll-bar/dist/es2015/constants.js", "../../node_modules/react-remove-scroll-bar/dist/es2015/index.js", "../../node_modules/react-remove-scroll-bar/dist/es2015/utils.js", "../../node_modules/react-remove-scroll/dist/es2015/Combination.js", "../../node_modules/react-remove-scroll/dist/es2015/SideEffect.js", "../../node_modules/react-remove-scroll/dist/es2015/UI.js", "../../node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js", "../../node_modules/react-remove-scroll/dist/es2015/handleScroll.js", "../../node_modules/react-remove-scroll/dist/es2015/medium.js", "../../node_modules/react-remove-scroll/dist/es2015/sidecar.js", "../../node_modules/react-style-singleton/dist/es2015/component.js", "../../node_modules/react-style-singleton/dist/es2015/hook.js", "../../node_modules/react-style-singleton/dist/es2015/index.js", "../../node_modules/react-style-singleton/dist/es2015/singleton.js", "../../node_modules/solito/build/app/navigation/use-link.js", "../../node_modules/solito/build/app/navigation/use-router.js", "../../node_modules/solito/build/router/parse-next-path.js", "../../node_modules/solito/build/router/replace-helpers.web.js", "../../node_modules/solito/build/router/use-link-to.web.js", "../../node_modules/solito/build/router/use-navigation.web.js", "../../node_modules/tamagui/dist/esm/views/Anchor.mjs", "../../node_modules/tslib/tslib.es6.mjs", "../../node_modules/use-callback-ref/dist/es2015/assignRef.js", "../../node_modules/use-callback-ref/dist/es2015/useMergeRef.js", "../../node_modules/use-callback-ref/dist/es2015/useRef.js", "../../node_modules/use-sidecar/dist/es2015/exports.js", "../../node_modules/use-sidecar/dist/es2015/medium.js", "../../packages/app/features/home/<USER>", "../../packages/ui/src/SwitchRouterButton.tsx", "../../packages/ui/src/SwitchThemeButton.tsx", "./pages/pages-example/index.tsx"]}