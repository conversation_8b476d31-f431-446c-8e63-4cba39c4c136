{"c": ["app/inventory/page", "webpack"], "r": ["pages/pages-example-user/[id]"], "m": ["../../node_modules/@tamagui/lucide-icons/dist/esm/icons/ChevronLeft.mjs", "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fapps%2Fnext%2Fpages%2Fpages-example-user%2F%5Bid%5D.tsx&page=%2Fpages-example-user%2F%5Bid%5D!", "../../node_modules/next/router.js", "../../node_modules/solito/build/params/index.js", "../../node_modules/solito/build/params/use-route.web.js", "../../node_modules/solito/build/params/use-router.web.js", "../../packages/app/features/user/detail-screen.tsx", "./pages/pages-example-user/[id].tsx"]}