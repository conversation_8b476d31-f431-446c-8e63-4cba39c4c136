"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/../../packages/app/features/inventory/database.ts":
/*!*********************************************************!*\
  !*** ../../packages/app/features/inventory/database.ts ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearDatabase: function() { return /* binding */ clearDatabase; },\n/* harmony export */   closeDatabase: function() { return /* binding */ closeDatabase; },\n/* harmony export */   executeQuery: function() { return /* binding */ executeQuery; },\n/* harmony export */   executeQueryFirst: function() { return /* binding */ executeQueryFirst; },\n/* harmony export */   executeTransaction: function() { return /* binding */ executeTransaction; },\n/* harmony export */   executeUpdate: function() { return /* binding */ executeUpdate; },\n/* harmony export */   getDatabase: function() { return /* binding */ getDatabase; },\n/* harmony export */   initializeDatabase: function() { return /* binding */ initializeDatabase; },\n/* harmony export */   seedDatabase: function() { return /* binding */ seedDatabase; }\n/* harmony export */ });\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-native */ \"(app-pages-browser)/../../node_modules/react-native-web/dist/exports/Platform/index.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/../../packages/app/features/inventory/types.ts\");\n\n\nlet db = null;\nconst initializeDatabase = async ()=>{\n    if (db) {\n        return db;\n    }\n    try {\n        if (react_native__WEBPACK_IMPORTED_MODULE_0__[\"default\"].OS === \"web\") {\n            // Use web-compatible database\n            const { initializeDatabase: initWebDb } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_packages_app_features_inventory_database_web_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./database.web */ \"(app-pages-browser)/../../packages/app/features/inventory/database.web.ts\"));\n            db = await initWebDb();\n        } else {\n            // Use expo-sqlite for native\n            const SQLite = __webpack_require__(Object(function webpackMissingModule() { var e = new Error(\"Cannot find module 'expo-sqlite'\"); e.code = 'MODULE_NOT_FOUND'; throw e; }()));\n            const nativeDb = await SQLite.openDatabaseAsync(\"inventory.db\");\n            // Enable foreign keys\n            await nativeDb.execAsync(\"PRAGMA foreign_keys = ON;\");\n            // Create tables\n            await nativeDb.execAsync(_types__WEBPACK_IMPORTED_MODULE_1__.CREATE_TABLES_SQL);\n            db = nativeDb;\n        }\n        console.log(\"Database initialized successfully\");\n        return db;\n    } catch (error) {\n        console.error(\"Failed to initialize database:\", error);\n        throw error;\n    }\n};\nconst getDatabase = async ()=>{\n    if (!db) {\n        return await initializeDatabase();\n    }\n    return db;\n};\nconst closeDatabase = async ()=>{\n    if (db) {\n        await db.closeAsync();\n        db = null;\n    }\n};\n// Utility function to execute queries with error handling\nconst executeQuery = async function(query) {\n    let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n    try {\n        const database = await getDatabase();\n        const result = await database.getAllAsync(query, params);\n        return result;\n    } catch (error) {\n        console.error(\"Query execution failed:\", error);\n        console.error(\"Query:\", query);\n        console.error(\"Params:\", params);\n        throw error;\n    }\n};\n// Utility function to execute single row queries\nconst executeQueryFirst = async function(query) {\n    let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n    try {\n        const database = await getDatabase();\n        const result = await database.getFirstAsync(query, params);\n        return result;\n    } catch (error) {\n        console.error(\"Query execution failed:\", error);\n        console.error(\"Query:\", query);\n        console.error(\"Params:\", params);\n        throw error;\n    }\n};\n// Utility function to execute insert/update/delete queries\nconst executeUpdate = async function(query) {\n    let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n    try {\n        const database = await getDatabase();\n        const result = await database.runAsync(query, params);\n        return result;\n    } catch (error) {\n        console.error(\"Update execution failed:\", error);\n        console.error(\"Query:\", query);\n        console.error(\"Params:\", params);\n        throw error;\n    }\n};\n// Transaction wrapper for multiple operations\nconst executeTransaction = async (operations)=>{\n    const database = await getDatabase();\n    try {\n        await database.execAsync(\"BEGIN TRANSACTION;\");\n        const result = await operations(database);\n        await database.execAsync(\"COMMIT;\");\n        return result;\n    } catch (error) {\n        await database.execAsync(\"ROLLBACK;\");\n        console.error(\"Transaction failed:\", error);\n        throw error;\n    }\n};\n// Database seeding function for development/testing\nconst seedDatabase = async ()=>{\n    try {\n        console.log(\"Seeding database with sample data...\");\n        // Sample products\n        const sampleProducts = [\n            {\n                name: \"Fish Food Premium\",\n                sku: \"FF001\",\n                unit: \"kg\",\n                min_stock_level: 10\n            },\n            {\n                name: \"Aquarium Filter\",\n                sku: \"AF001\",\n                unit: \"piece\",\n                min_stock_level: 5\n            },\n            {\n                name: \"Water Conditioner\",\n                sku: \"WC001\",\n                unit: \"liter\",\n                min_stock_level: 20\n            },\n            {\n                name: \"LED Light Strip\",\n                sku: \"LS001\",\n                unit: \"piece\",\n                min_stock_level: 3\n            },\n            {\n                name: \"Gravel Substrate\",\n                sku: \"GS001\",\n                unit: \"kg\",\n                min_stock_level: 50\n            }\n        ];\n        for (const product of sampleProducts){\n            await executeUpdate(\"INSERT OR IGNORE INTO products (name, sku, unit, min_stock_level) \\n         VALUES (?, ?, ?, ?)\", [\n                product.name,\n                product.sku,\n                product.unit,\n                product.min_stock_level\n            ]);\n        }\n        // Sample packages\n        const starterPackage = await executeUpdate(\"INSERT OR IGNORE INTO packages (name, description) \\n       VALUES (?, ?)\", [\n            \"Aquarium Starter Kit\",\n            \"Complete kit for new aquarium setup\"\n        ]);\n        if (starterPackage.lastInsertRowId) {\n            // Add items to the starter package\n            const packageItems = [\n                {\n                    product_id: 1,\n                    quantity: 2\n                },\n                {\n                    product_id: 2,\n                    quantity: 1\n                },\n                {\n                    product_id: 3,\n                    quantity: 1\n                },\n                {\n                    product_id: 4,\n                    quantity: 1\n                },\n                {\n                    product_id: 5,\n                    quantity: 5\n                } // Gravel\n            ];\n            for (const item of packageItems){\n                await executeUpdate(\"INSERT OR IGNORE INTO package_items (package_id, product_id, quantity) \\n           VALUES (?, ?, ?)\", [\n                    starterPackage.lastInsertRowId,\n                    item.product_id,\n                    item.quantity\n                ]);\n            }\n        }\n        // Sample batches and inventory\n        const sampleBatches = [\n            {\n                product_id: 1,\n                batch_number: \"FF001-2024-01\",\n                supplier: \"AquaSupply Co\",\n                cost_per_unit: 25.50,\n                quantity: 100\n            },\n            {\n                product_id: 2,\n                batch_number: \"AF001-2024-01\",\n                supplier: \"FilterTech Ltd\",\n                cost_per_unit: 45.00,\n                quantity: 20\n            },\n            {\n                product_id: 3,\n                batch_number: \"WC001-2024-01\",\n                supplier: \"ChemAqua Inc\",\n                cost_per_unit: 12.75,\n                quantity: 50\n            },\n            {\n                product_id: 4,\n                batch_number: \"LS001-2024-01\",\n                supplier: \"LightCorp\",\n                cost_per_unit: 89.99,\n                quantity: 15\n            },\n            {\n                product_id: 5,\n                batch_number: \"GS001-2024-01\",\n                supplier: \"SubstratePro\",\n                cost_per_unit: 8.50,\n                quantity: 200\n            }\n        ];\n        for (const batch of sampleBatches){\n            const batchResult = await executeUpdate(\"INSERT OR IGNORE INTO batches (product_id, batch_number, supplier, cost_per_unit) \\n         VALUES (?, ?, ?, ?)\", [\n                batch.product_id,\n                batch.batch_number,\n                batch.supplier,\n                batch.cost_per_unit\n            ]);\n            if (batchResult.lastInsertRowId) {\n                // Add initial inventory\n                await executeUpdate(\"INSERT OR IGNORE INTO inventory (product_id, batch_id, quantity, location) \\n           VALUES (?, ?, ?, ?)\", [\n                    batch.product_id,\n                    batchResult.lastInsertRowId,\n                    batch.quantity,\n                    \"Main Warehouse\"\n                ]);\n                // Add initial inbound transaction\n                await executeUpdate(\"INSERT INTO transactions (type, product_id, batch_id, quantity, reference_number, notes) \\n           VALUES (?, ?, ?, ?, ?, ?)\", [\n                    \"INBOUND\",\n                    batch.product_id,\n                    batchResult.lastInsertRowId,\n                    batch.quantity,\n                    \"PO-2024-\".concat(batch.product_id.toString().padStart(3, \"0\")),\n                    \"Initial stock\"\n                ]);\n            }\n        }\n        // Sample stock alerts\n        const stockAlerts = [\n            {\n                product_id: 1,\n                alert_type: \"LOW_STOCK\",\n                threshold: 10\n            },\n            {\n                product_id: 2,\n                alert_type: \"LOW_STOCK\",\n                threshold: 5\n            },\n            {\n                product_id: 3,\n                alert_type: \"LOW_STOCK\",\n                threshold: 20\n            },\n            {\n                product_id: 4,\n                alert_type: \"LOW_STOCK\",\n                threshold: 3\n            },\n            {\n                product_id: 5,\n                alert_type: \"LOW_STOCK\",\n                threshold: 50\n            }\n        ];\n        for (const alert of stockAlerts){\n            await executeUpdate(\"INSERT OR IGNORE INTO stock_alerts (product_id, alert_type, threshold) \\n         VALUES (?, ?, ?)\", [\n                alert.product_id,\n                alert.alert_type,\n                alert.threshold\n            ]);\n        }\n        console.log(\"Database seeded successfully\");\n    } catch (error) {\n        console.error(\"Failed to seed database:\", error);\n        throw error;\n    }\n};\n// Clear all data (for testing purposes)\nconst clearDatabase = async ()=>{\n    try {\n        const tables = [\n            \"transactions\",\n            \"stock_alerts\",\n            \"inventory\",\n            \"batches\",\n            \"package_items\",\n            \"packages\",\n            \"products\",\n            \"shipments\"\n        ];\n        for (const table of tables){\n            await executeUpdate(\"DELETE FROM \".concat(table));\n        }\n        console.log(\"Database cleared successfully\");\n    } catch (error) {\n        console.error(\"Failed to clear database:\", error);\n        throw error;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/app/features/inventory/database.ts\n"));

/***/ })

});