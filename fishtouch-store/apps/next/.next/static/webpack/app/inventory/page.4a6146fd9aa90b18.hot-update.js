"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/../../packages/app/features/inventory/database.ts":
/*!*********************************************************!*\
  !*** ../../packages/app/features/inventory/database.ts ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearDatabase: function() { return /* binding */ clearDatabase; },\n/* harmony export */   closeDatabase: function() { return /* binding */ closeDatabase; },\n/* harmony export */   executeQuery: function() { return /* binding */ executeQuery; },\n/* harmony export */   executeQueryFirst: function() { return /* binding */ executeQueryFirst; },\n/* harmony export */   executeTransaction: function() { return /* binding */ executeTransaction; },\n/* harmony export */   executeUpdate: function() { return /* binding */ executeUpdate; },\n/* harmony export */   getDatabase: function() { return /* binding */ getDatabase; },\n/* harmony export */   initializeDatabase: function() { return /* binding */ initializeDatabase; },\n/* harmony export */   seedDatabase: function() { return /* binding */ seedDatabase; }\n/* harmony export */ });\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-native */ \"(app-pages-browser)/../../node_modules/react-native-web/dist/exports/Platform/index.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/../../packages/app/features/inventory/types.ts\");\n\n\nlet db = null;\nconst initializeDatabase = async ()=>{\n    if (db) {\n        return db;\n    }\n    try {\n        if (react_native__WEBPACK_IMPORTED_MODULE_0__[\"default\"].OS === \"web\") {\n            // Use web-compatible database\n            const { initializeDatabase: initWebDb } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_packages_app_features_inventory_database_web_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./database.web */ \"(app-pages-browser)/../../packages/app/features/inventory/database.web.ts\"));\n            db = await initWebDb();\n        } else {\n            // Use expo-sqlite for native\n            const SQLite = __webpack_require__(Object(function webpackMissingModule() { var e = new Error(\"Cannot find module 'expo-sqlite'\"); e.code = 'MODULE_NOT_FOUND'; throw e; }()));\n            const nativeDb = await SQLite.openDatabaseAsync(\"inventory.db\");\n            // Enable foreign keys\n            await nativeDb.execAsync(\"PRAGMA foreign_keys = ON;\");\n            // Create tables\n            await nativeDb.execAsync(_types__WEBPACK_IMPORTED_MODULE_1__.CREATE_TABLES_SQL);\n            db = nativeDb;\n        }\n        console.log(\"Database initialized successfully\");\n        return db;\n    } catch (error) {\n        console.error(\"Failed to initialize database:\", error);\n        throw error;\n    }\n};\nconst getDatabase = async ()=>{\n    if (!db) {\n        return await initializeDatabase();\n    }\n    return db;\n};\nconst closeDatabase = async ()=>{\n    if (db) {\n        await db.closeAsync();\n        db = null;\n    }\n};\n// Utility function to execute queries with error handling\nconst executeQuery = async function(query) {\n    let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n    try {\n        const database = await getDatabase();\n        const result = await database.getAllAsync(query, params);\n        return result;\n    } catch (error) {\n        console.error(\"Query execution failed:\", error);\n        console.error(\"Query:\", query);\n        console.error(\"Params:\", params);\n        throw error;\n    }\n};\n// Utility function to execute single row queries\nconst executeQueryFirst = async function(query) {\n    let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n    try {\n        const database = await getDatabase();\n        const result = await database.getFirstAsync(query, params);\n        return result;\n    } catch (error) {\n        console.error(\"Query execution failed:\", error);\n        console.error(\"Query:\", query);\n        console.error(\"Params:\", params);\n        throw error;\n    }\n};\n// Utility function to execute insert/update/delete queries\nconst executeUpdate = async function(query) {\n    let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n    try {\n        const database = await getDatabase();\n        const result = await database.runAsync(query, params);\n        return result;\n    } catch (error) {\n        console.error(\"Update execution failed:\", error);\n        console.error(\"Query:\", query);\n        console.error(\"Params:\", params);\n        throw error;\n    }\n};\n// Transaction wrapper for multiple operations\nconst executeTransaction = async (operations)=>{\n    const database = await getDatabase();\n    try {\n        await database.execAsync(\"BEGIN TRANSACTION;\");\n        const result = await operations(database);\n        await database.execAsync(\"COMMIT;\");\n        return result;\n    } catch (error) {\n        await database.execAsync(\"ROLLBACK;\");\n        console.error(\"Transaction failed:\", error);\n        throw error;\n    }\n};\n// Database seeding function for development/testing\nconst seedDatabase = async ()=>{\n    try {\n        console.log(\"Seeding database with sample data...\");\n        // Sample products\n        const sampleProducts = [\n            {\n                name: \"Fish Food Premium\",\n                sku: \"FF001\",\n                unit: \"kg\",\n                min_stock_level: 10\n            },\n            {\n                name: \"Aquarium Filter\",\n                sku: \"AF001\",\n                unit: \"piece\",\n                min_stock_level: 5\n            },\n            {\n                name: \"Water Conditioner\",\n                sku: \"WC001\",\n                unit: \"liter\",\n                min_stock_level: 20\n            },\n            {\n                name: \"LED Light Strip\",\n                sku: \"LS001\",\n                unit: \"piece\",\n                min_stock_level: 3\n            },\n            {\n                name: \"Gravel Substrate\",\n                sku: \"GS001\",\n                unit: \"kg\",\n                min_stock_level: 50\n            }\n        ];\n        for (const product of sampleProducts){\n            await executeUpdate(\"INSERT OR IGNORE INTO products (name, sku, unit, min_stock_level) \\n         VALUES (?, ?, ?, ?)\", [\n                product.name,\n                product.sku,\n                product.unit,\n                product.min_stock_level\n            ]);\n        }\n        // Sample packages\n        const starterPackage = await executeUpdate(\"INSERT OR IGNORE INTO packages (name, description) \\n       VALUES (?, ?)\", [\n            \"Aquarium Starter Kit\",\n            \"Complete kit for new aquarium setup\"\n        ]);\n        if (starterPackage.lastInsertRowId) {\n            // Add items to the starter package\n            const packageItems = [\n                {\n                    product_id: 1,\n                    quantity: 2\n                },\n                {\n                    product_id: 2,\n                    quantity: 1\n                },\n                {\n                    product_id: 3,\n                    quantity: 1\n                },\n                {\n                    product_id: 4,\n                    quantity: 1\n                },\n                {\n                    product_id: 5,\n                    quantity: 5\n                } // Gravel\n            ];\n            for (const item of packageItems){\n                await executeUpdate(\"INSERT OR IGNORE INTO package_items (package_id, product_id, quantity) \\n           VALUES (?, ?, ?)\", [\n                    starterPackage.lastInsertRowId,\n                    item.product_id,\n                    item.quantity\n                ]);\n            }\n        }\n        // Sample batches and inventory\n        const sampleBatches = [\n            {\n                product_id: 1,\n                batch_number: \"FF001-2024-01\",\n                supplier: \"AquaSupply Co\",\n                cost_per_unit: 25.50,\n                quantity: 100\n            },\n            {\n                product_id: 2,\n                batch_number: \"AF001-2024-01\",\n                supplier: \"FilterTech Ltd\",\n                cost_per_unit: 45.00,\n                quantity: 20\n            },\n            {\n                product_id: 3,\n                batch_number: \"WC001-2024-01\",\n                supplier: \"ChemAqua Inc\",\n                cost_per_unit: 12.75,\n                quantity: 50\n            },\n            {\n                product_id: 4,\n                batch_number: \"LS001-2024-01\",\n                supplier: \"LightCorp\",\n                cost_per_unit: 89.99,\n                quantity: 15\n            },\n            {\n                product_id: 5,\n                batch_number: \"GS001-2024-01\",\n                supplier: \"SubstratePro\",\n                cost_per_unit: 8.50,\n                quantity: 200\n            }\n        ];\n        for (const batch of sampleBatches){\n            const batchResult = await executeUpdate(\"INSERT OR IGNORE INTO batches (product_id, batch_number, supplier, cost_per_unit) \\n         VALUES (?, ?, ?, ?)\", [\n                batch.product_id,\n                batch.batch_number,\n                batch.supplier,\n                batch.cost_per_unit\n            ]);\n            if (batchResult.lastInsertRowId) {\n                // Add initial inventory\n                await executeUpdate(\"INSERT OR IGNORE INTO inventory (product_id, batch_id, quantity, location) \\n           VALUES (?, ?, ?, ?)\", [\n                    batch.product_id,\n                    batchResult.lastInsertRowId,\n                    batch.quantity,\n                    \"Main Warehouse\"\n                ]);\n                // Add initial inbound transaction\n                await executeUpdate(\"INSERT INTO transactions (type, product_id, batch_id, quantity, reference_number, notes) \\n           VALUES (?, ?, ?, ?, ?, ?)\", [\n                    \"INBOUND\",\n                    batch.product_id,\n                    batchResult.lastInsertRowId,\n                    batch.quantity,\n                    \"PO-2024-\".concat(batch.product_id.toString().padStart(3, \"0\")),\n                    \"Initial stock\"\n                ]);\n            }\n        }\n        // Sample stock alerts\n        const stockAlerts = [\n            {\n                product_id: 1,\n                alert_type: \"LOW_STOCK\",\n                threshold: 10\n            },\n            {\n                product_id: 2,\n                alert_type: \"LOW_STOCK\",\n                threshold: 5\n            },\n            {\n                product_id: 3,\n                alert_type: \"LOW_STOCK\",\n                threshold: 20\n            },\n            {\n                product_id: 4,\n                alert_type: \"LOW_STOCK\",\n                threshold: 3\n            },\n            {\n                product_id: 5,\n                alert_type: \"LOW_STOCK\",\n                threshold: 50\n            }\n        ];\n        for (const alert of stockAlerts){\n            await executeUpdate(\"INSERT OR IGNORE INTO stock_alerts (product_id, alert_type, threshold) \\n         VALUES (?, ?, ?)\", [\n                alert.product_id,\n                alert.alert_type,\n                alert.threshold\n            ]);\n        }\n        console.log(\"Database seeded successfully\");\n    } catch (error) {\n        console.error(\"Failed to seed database:\", error);\n        throw error;\n    }\n};\n// Clear all data (for testing purposes)\nconst clearDatabase = async ()=>{\n    try {\n        const tables = [\n            \"transactions\",\n            \"stock_alerts\",\n            \"inventory\",\n            \"batches\",\n            \"package_items\",\n            \"packages\",\n            \"products\",\n            \"shipments\"\n        ];\n        for (const table of tables){\n            await executeUpdate(\"DELETE FROM \".concat(table));\n        }\n        console.log(\"Database cleared successfully\");\n    } catch (error) {\n        console.error(\"Failed to clear database:\", error);\n        throw error;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/app/features/inventory/database.ts\n"));

/***/ })

});