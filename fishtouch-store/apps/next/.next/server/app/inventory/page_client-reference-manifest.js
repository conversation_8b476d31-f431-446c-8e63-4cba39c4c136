globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/inventory/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../packages/app/provider/NextTamaguiProvider.tsx":{"*":{"id":"(ssr)/../../packages/app/provider/NextTamaguiProvider.tsx","name":"*","chunks":[],"async":true}},"(app-pages-browser)/../../node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/inventory/page.tsx":{"*":{"id":"(ssr)/./app/inventory/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/user/[id]/page.tsx":{"*":{"id":"(ssr)/./app/user/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/app/page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/NextTamaguiProvider.tsx":{"id":"(app-pages-browser)/../../packages/app/provider/NextTamaguiProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":true},"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/app/inventory/page.tsx":{"id":"(app-pages-browser)/./app/inventory/page.tsx","name":"*","chunks":["app/inventory/page","static/chunks/app/inventory/page.js"],"async":false},"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/app/user/[id]/page.tsx":{"id":"(app-pages-browser)/./app/user/[id]/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/":[],"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/app/page":[],"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/app/layout":["static/css/app/layout.css"],"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/app/inventory/page":[]}}