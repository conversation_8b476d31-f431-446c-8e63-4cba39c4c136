/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/user/[id]/page";
exports.ids = ["app/user/[id]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fuser%2F%5Bid%5D%2Fpage&page=%2Fuser%2F%5Bid%5D%2Fpage&appPaths=%2Fuser%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fuser%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fapps%2Fnext%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fapps%2Fnext&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fuser%2F%5Bid%5D%2Fpage&page=%2Fuser%2F%5Bid%5D%2Fpage&appPaths=%2Fuser%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fuser%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fapps%2Fnext%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fapps%2Fnext&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?ac0d\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'user',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/user/[id]/page.tsx */ \"(rsc)/./app/user/[id]/page.tsx\")), \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/app/user/[id]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/app/user/[id]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/user/[id]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/user/[id]/page\",\n        pathname: \"/user/[id]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fuser%2F%5Bid%5D%2Fpage&page=%2Fuser%2F%5Bid%5D%2Fpage&appPaths=%2Fuser%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fuser%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fapps%2Fnext%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fapps%2Fnext&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fapps%2Fnext%2Fapp%2Fuser%2F%5Bid%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fapps%2Fnext%2Fapp%2Fuser%2F%5Bid%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/user/[id]/page.tsx */ \"(ssr)/./app/user/[id]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnpoZSUyRkRldmVsb3BlciUyRkZ1bGxTdGFja1Byb2plY3RzJTJGRmlzaFRvdWNoU3RvcmUlMkZmaXNodG91Y2gtc3RvcmUlMkZhcHBzJTJGbmV4dCUyRmFwcCUyRnVzZXIlMkYlNUJpZCU1RCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0SkFBMkkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8/MGE1MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy96aGUvRGV2ZWxvcGVyL0Z1bGxTdGFja1Byb2plY3RzL0Zpc2hUb3VjaFN0b3JlL2Zpc2h0b3VjaC1zdG9yZS9hcHBzL25leHQvYXBwL3VzZXIvW2lkXS9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fapps%2Fnext%2Fapp%2Fuser%2F%5Bid%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fpackages%2Fapp%2Fprovider%2FNextTamaguiProvider.tsx%22%2C%22ids%22%3A%5B%22NextTamaguiProvider%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fpackages%2Fapp%2Fprovider%2FNextTamaguiProvider.tsx%22%2C%22ids%22%3A%5B%22NextTamaguiProvider%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../packages/app/provider/NextTamaguiProvider.tsx */ \"(ssr)/../../packages/app/provider/NextTamaguiProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnpoZSUyRkRldmVsb3BlciUyRkZ1bGxTdGFja1Byb2plY3RzJTJGRmlzaFRvdWNoU3RvcmUlMkZmaXNodG91Y2gtc3RvcmUlMkZwYWNrYWdlcyUyRmFwcCUyRnByb3ZpZGVyJTJGTmV4dFRhbWFndWlQcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJOZXh0VGFtYWd1aVByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrTkFBaU0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8/MGNlZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIk5leHRUYW1hZ3VpUHJvdmlkZXJcIl0gKi8gXCIvVXNlcnMvemhlL0RldmVsb3Blci9GdWxsU3RhY2tQcm9qZWN0cy9GaXNoVG91Y2hTdG9yZS9maXNodG91Y2gtc3RvcmUvcGFja2FnZXMvYXBwL3Byb3ZpZGVyL05leHRUYW1hZ3VpUHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fpackages%2Fapp%2Fprovider%2FNextTamaguiProvider.tsx%22%2C%22ids%22%3A%5B%22NextTamaguiProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/user/[id]/page.tsx":
/*!********************************!*\
  !*** ./app/user/[id]/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_features_user_detail_screen__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! app/features/user/detail-screen */ \"(ssr)/../../packages/app/features/user/detail-screen.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! solito/navigation */ \"(ssr)/../../node_modules/solito/build/app/navigation/use-params.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Page() {\n    const { id } = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_features_user_detail_screen__WEBPACK_IMPORTED_MODULE_2__.UserDetailScreen, {\n        id: id\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/app/user/[id]/page.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvdXNlci9baWRdL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVrRTtBQUNyQjtBQUU5QixTQUFTRTtJQUN0QixNQUFNLEVBQUVDLEVBQUUsRUFBRSxHQUFHRiw0REFBU0E7SUFDeEIscUJBQU8sOERBQUNELDZFQUFnQkE7UUFBQ0csSUFBSUE7Ozs7OztBQUMvQiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4vYXBwL3VzZXIvW2lkXS9wYWdlLnRzeD9iODIzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBVc2VyRGV0YWlsU2NyZWVuIH0gZnJvbSAnYXBwL2ZlYXR1cmVzL3VzZXIvZGV0YWlsLXNjcmVlbidcbmltcG9ydCB7IHVzZVBhcmFtcyB9IGZyb20gJ3NvbGl0by9uYXZpZ2F0aW9uJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQYWdlKCkge1xuICBjb25zdCB7IGlkIH0gPSB1c2VQYXJhbXMoKVxuICByZXR1cm4gPFVzZXJEZXRhaWxTY3JlZW4gaWQ9e2lkIGFzIHN0cmluZ30gLz5cbn1cbiJdLCJuYW1lcyI6WyJVc2VyRGV0YWlsU2NyZWVuIiwidXNlUGFyYW1zIiwiUGFnZSIsImlkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/user/[id]/page.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/app/features/user/detail-screen.tsx":
/*!**********************************************************!*\
  !*** ../../packages/app/features/user/detail-screen.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserDetailScreen: () => (/* binding */ UserDetailScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @my/ui */ \"(ssr)/../../node_modules/@tamagui/stacks/dist/esm/Stacks.mjs\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @my/ui */ \"(ssr)/../../node_modules/@tamagui/text/dist/esm/Paragraph.mjs\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @my/ui */ \"(ssr)/../../node_modules/@tamagui/button/dist/esm/Button.mjs\");\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"(ssr)/../../node_modules/@tamagui/lucide-icons/dist/esm/icons/ChevronLeft.mjs\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! solito/navigation */ \"(ssr)/../../node_modules/solito/build/app/navigation/use-router.js\");\n\n\n\n\nfunction UserDetailScreen({ id }) {\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    if (!id) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_2__.YStack, {\n        \"data-at\": \"detail-screen.tsx:11\",\n        \"data-in\": \"UserDetailScreen\",\n        \"data-is\": \"YStack\",\n        flex: 1,\n        justify: \"center\",\n        items: \"center\",\n        gap: \"$4\",\n        bg: \"$background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.Paragraph, {\n                \"data-at\": \"detail-screen.tsx:12\",\n                \"data-in\": \"UserDetailScreen\",\n                \"data-is\": \"Paragraph\",\n                text: \"center\",\n                fontWeight: \"700\",\n                color: \"$blue10\",\n                children: `User ID: ${id}`\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/user/detail-screen.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                \"data-at\": \"detail-screen.tsx:13\",\n                \"data-in\": \"UserDetailScreen\",\n                \"data-is\": \"Button\",\n                icon: _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_5__.ChevronLeft,\n                onPress: ()=>router.back(),\n                children: \"Go Home\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/user/detail-screen.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/user/detail-screen.tsx\",\n        lineNumber: 13,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/app/features/user/detail-screen.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/app/provider/NextTamaguiProvider.tsx":
/*!***********************************************************!*\
  !*** ../../packages/app/provider/NextTamaguiProvider.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NextTamaguiProvider: () => (/* binding */ NextTamaguiProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tamagui_core_reset_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tamagui/core/reset.css */ \"(ssr)/../../node_modules/@tamagui/core/reset.css\");\n/* harmony import */ var _tamagui_font_inter_css_400_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tamagui/font-inter/css/400.css */ \"(ssr)/../../node_modules/@tamagui/font-inter/css/400.css\");\n/* harmony import */ var _tamagui_font_inter_css_700_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tamagui/font-inter/css/700.css */ \"(ssr)/../../node_modules/@tamagui/font-inter/css/700.css\");\n/* harmony import */ var _tamagui_polyfill_dev__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tamagui/polyfill-dev */ \"(ssr)/../../node_modules/@tamagui/polyfill-dev/index.js\");\n/* harmony import */ var _tamagui_polyfill_dev__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tamagui_polyfill_dev__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tamagui_next_theme__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tamagui/next-theme */ \"(ssr)/../../node_modules/@tamagui/next-theme/dist/esm/useRootTheme.js\");\n/* harmony import */ var _tamagui_next_theme__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tamagui/next-theme */ \"(ssr)/../../node_modules/@tamagui/next-theme/dist/esm/NextThemeProvider.js\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @my/ui */ \"(ssr)/../../packages/config/src/tamagui.config.ts\");\n/* harmony import */ var app_provider__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! app/provider */ \"(ssr)/../../packages/app/provider/index.tsx\");\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-native */ \"(ssr)/../../node_modules/react-native-web/dist/exports/StyleSheet/index.js\");\n/* __next_internal_client_entry_do_not_use__ NextTamaguiProvider auto */ \n\n\n\n\n\n\n\n\n\nconst NextTamaguiProvider = ({ children })=>{\n    const [theme, setTheme] = (0,_tamagui_next_theme__WEBPACK_IMPORTED_MODULE_6__.useRootTheme)();\n    (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useServerInsertedHTML)(()=>{\n        // @ts-ignore\n        const rnwStyle = react_native__WEBPACK_IMPORTED_MODULE_7__[\"default\"].getSheet();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"stylesheet\",\n                    href: \"/tamagui.css\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/NextTamaguiProvider.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                    dangerouslySetInnerHTML: {\n                        __html: rnwStyle.textContent\n                    },\n                    id: rnwStyle.id\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/NextTamaguiProvider.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                    dangerouslySetInnerHTML: {\n                        // the first time this runs you'll get the full CSS including all themes\n                        // after that, it will only return CSS generated since the last call\n                        __html: _my_ui__WEBPACK_IMPORTED_MODULE_8__.config.getNewCSS()\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/NextTamaguiProvider.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                    dangerouslySetInnerHTML: {\n                        __html: _my_ui__WEBPACK_IMPORTED_MODULE_8__.config.getCSS({\n                            exclude:  false ? 0 : null\n                        })\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/NextTamaguiProvider.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    dangerouslySetInnerHTML: {\n                        // avoid flash of animated things on enter:\n                        __html: `document.documentElement.classList.add('t_unmounted')`\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/NextTamaguiProvider.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_next_theme__WEBPACK_IMPORTED_MODULE_9__.NextThemeProvider, {\n        skipNextHead: true,\n        defaultTheme: \"light\",\n        onChangeTheme: (next)=>{\n            setTheme(next);\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_provider__WEBPACK_IMPORTED_MODULE_10__.Provider, {\n            disableRootThemeClass: true,\n            defaultTheme: theme || \"light\",\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/NextTamaguiProvider.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/NextTamaguiProvider.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/app/provider/NextTamaguiProvider.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/app/provider/ToastViewport.web.tsx":
/*!*********************************************************!*\
  !*** ../../packages/app/provider/ToastViewport.web.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @my/ui */ \"(ssr)/../../node_modules/@tamagui/toast/dist/esm/ToastViewport.mjs\");\n\n\nconst ToastViewport = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_1__.ToastViewport, {\n        left: 0,\n        right: 0,\n        top: 10\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/ToastViewport.web.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvYXBwL3Byb3ZpZGVyL1RvYXN0Vmlld3BvcnQud2ViLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF5RDtBQUVsRCxNQUFNQSxnQkFBZ0I7SUFDM0IscUJBQ0UsOERBQUNDLGlEQUFlQTtRQUNkQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsS0FBSzs7Ozs7O0FBR1gsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL3BhY2thZ2VzL2FwcC9wcm92aWRlci9Ub2FzdFZpZXdwb3J0LndlYi50c3g/ZWM1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBUb2FzdFZpZXdwb3J0IGFzIFRvYXN0Vmlld3BvcnRPZyB9IGZyb20gJ0BteS91aSdcblxuZXhwb3J0IGNvbnN0IFRvYXN0Vmlld3BvcnQgPSAoKSA9PiB7XG4gIHJldHVybiAoXG4gICAgPFRvYXN0Vmlld3BvcnRPZ1xuICAgICAgbGVmdD17MH1cbiAgICAgIHJpZ2h0PXswfVxuICAgICAgdG9wPXsxMH1cbiAgICAvPlxuICApXG59XG4iXSwibmFtZXMiOlsiVG9hc3RWaWV3cG9ydCIsIlRvYXN0Vmlld3BvcnRPZyIsImxlZnQiLCJyaWdodCIsInRvcCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../packages/app/provider/ToastViewport.web.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/app/provider/index.tsx":
/*!*********************************************!*\
  !*** ../../packages/app/provider/index.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Provider: () => (/* binding */ Provider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-native */ \"(ssr)/../../node_modules/react-native-web/dist/exports/useColorScheme/index.js\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @my/ui */ \"(ssr)/../../node_modules/tamagui/dist/esm/views/TamaguiProvider.mjs\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @my/ui */ \"(ssr)/../../packages/config/src/tamagui.config.ts\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @my/ui */ \"(ssr)/../../node_modules/@tamagui/toast/dist/esm/ToastProvider.mjs\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @my/ui */ \"(ssr)/../../node_modules/@tamagui/core/dist/cjs/index.cjs\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_my_ui__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @my/ui */ \"(ssr)/../../packages/ui/src/CustomToast.tsx\");\n/* harmony import */ var _ToastViewport__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ToastViewport */ \"(ssr)/../../packages/app/provider/ToastViewport.web.tsx\");\n\n\n\n\nfunction Provider({ children, defaultTheme = \"light\", ...rest }) {\n    const colorScheme = (0,react_native__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    const theme = defaultTheme || (colorScheme === \"dark\" ? \"dark\" : \"light\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_2__.TamaguiProvider, {\n        config: _my_ui__WEBPACK_IMPORTED_MODULE_3__.config,\n        defaultTheme: theme,\n        ...rest,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_4__.ToastProvider, {\n            swipeDirection: \"horizontal\",\n            duration: 6000,\n            native: _my_ui__WEBPACK_IMPORTED_MODULE_5__.isWeb ? [] : [\n                \"mobile\"\n            ],\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.CustomToast, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/index.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ToastViewport__WEBPACK_IMPORTED_MODULE_7__.ToastViewport, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/index.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/index.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/index.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/app/provider/index.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui/src/CustomToast.tsx":
/*!*********************************************!*\
  !*** ../../packages/ui/src/CustomToast.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomToast: () => (/* binding */ CustomToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var expo_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! expo-constants */ \"(ssr)/../../node_modules/expo-constants/build/Constants.js\");\n/* harmony import */ var expo_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! expo-constants */ \"(ssr)/../../node_modules/expo-constants/build/Constants.types.js\");\n/* harmony import */ var _NativeToast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./NativeToast */ \"(ssr)/../../packages/ui/src/NativeToast.tsx\");\n\n\n\nconst isExpo = expo_constants__WEBPACK_IMPORTED_MODULE_1__[\"default\"].executionEnvironment === expo_constants__WEBPACK_IMPORTED_MODULE_2__.ExecutionEnvironment.StoreClient;\nconst CustomToast = ()=>{\n    if (isExpo) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NativeToast__WEBPACK_IMPORTED_MODULE_3__.NativeToast, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/ui/src/CustomToast.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWkvc3JjL0N1c3RvbVRvYXN0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQWdFO0FBQ1o7QUFFcEQsTUFBTUksU0FBU0osc0RBQVNBLENBQUNLLG9CQUFvQixLQUFLSixnRUFBb0JBLENBQUNLLFdBQVc7QUFFM0UsTUFBTUMsY0FBYztJQUN6QixJQUFJSCxRQUFRO1FBQ1YsT0FBTztJQUNUO0lBQ0EscUJBQU8sOERBQUNELHFEQUFLQTs7Ozs7QUFDZixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vcGFja2FnZXMvdWkvc3JjL0N1c3RvbVRvYXN0LnRzeD84YzBkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBDb25zdGFudHMsIHsgRXhlY3V0aW9uRW52aXJvbm1lbnQgfSBmcm9tICdleHBvLWNvbnN0YW50cydcbmltcG9ydCB7IE5hdGl2ZVRvYXN0IGFzIFRvYXN0IH0gZnJvbSAnLi9OYXRpdmVUb2FzdCdcblxuY29uc3QgaXNFeHBvID0gQ29uc3RhbnRzLmV4ZWN1dGlvbkVudmlyb25tZW50ID09PSBFeGVjdXRpb25FbnZpcm9ubWVudC5TdG9yZUNsaWVudFxuXG5leHBvcnQgY29uc3QgQ3VzdG9tVG9hc3QgPSAoKSA9PiB7XG4gIGlmIChpc0V4cG8pIHtcbiAgICByZXR1cm4gbnVsbFxuICB9XG4gIHJldHVybiA8VG9hc3QgLz5cbn1cbiJdLCJuYW1lcyI6WyJDb25zdGFudHMiLCJFeGVjdXRpb25FbnZpcm9ubWVudCIsIk5hdGl2ZVRvYXN0IiwiVG9hc3QiLCJpc0V4cG8iLCJleGVjdXRpb25FbnZpcm9ubWVudCIsIlN0b3JlQ2xpZW50IiwiQ3VzdG9tVG9hc3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui/src/CustomToast.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui/src/NativeToast.tsx":
/*!*********************************************!*\
  !*** ../../packages/ui/src/NativeToast.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NativeToast: () => (/* binding */ NativeToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tamagui_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tamagui/toast */ \"(ssr)/../../node_modules/@tamagui/toast/dist/esm/ToastImperative.mjs\");\n/* harmony import */ var _tamagui_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tamagui/toast */ \"(ssr)/../../node_modules/@tamagui/toast/dist/esm/Toast.mjs\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tamagui */ \"(ssr)/../../node_modules/@tamagui/stacks/dist/esm/Stacks.mjs\");\n\n\n\nconst NativeToast = ()=>{\n    const currentToast = (0,_tamagui_toast__WEBPACK_IMPORTED_MODULE_1__.useToastState)();\n    if (!currentToast || currentToast.isHandledNatively) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_toast__WEBPACK_IMPORTED_MODULE_2__.Toast, {\n        duration: currentToast.duration,\n        viewportName: currentToast.viewportName,\n        enterStyle: {\n            opacity: 0,\n            scale: 0.5,\n            y: -25\n        },\n        exitStyle: {\n            opacity: 0,\n            scale: 1,\n            y: -20\n        },\n        y: 0,\n        opacity: 1,\n        scale: 1,\n        animation: \"quick\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.YStack, {\n            \"data-at\": \"NativeToast.tsx:23\",\n            \"data-in\": \"NativeToast\",\n            \"data-is\": \"YStack\",\n            py: \"$1.5\",\n            px: \"$2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_toast__WEBPACK_IMPORTED_MODULE_2__.Toast.Title, {\n                    lineHeight: \"$1\",\n                    children: currentToast.title\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/ui/src/NativeToast.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, undefined),\n                !!currentToast.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_toast__WEBPACK_IMPORTED_MODULE_2__.Toast.Description, {\n                    children: currentToast.message\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/ui/src/NativeToast.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 36\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/ui/src/NativeToast.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, undefined)\n    }, currentToast.id, false, {\n        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/ui/src/NativeToast.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui/src/NativeToast.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/config/src/animations.ts":
/*!***********************************************!*\
  !*** ../../packages/config/src/animations.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animations: () => (/* binding */ animations)\n/* harmony export */ });\n/* harmony import */ var _tamagui_animations_react_native__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tamagui/animations-react-native */ \"(ssr)/../../node_modules/@tamagui/animations-react-native/dist/esm/createAnimations.mjs\");\n\nconst animations = (0,_tamagui_animations_react_native__WEBPACK_IMPORTED_MODULE_0__.createAnimations)({\n    \"100ms\": {\n        type: \"timing\",\n        duration: 100\n    },\n    bouncy: {\n        damping: 9,\n        mass: 0.9,\n        stiffness: 150\n    },\n    lazy: {\n        damping: 18,\n        stiffness: 50\n    },\n    medium: {\n        damping: 15,\n        stiffness: 120,\n        mass: 1\n    },\n    slow: {\n        damping: 15,\n        stiffness: 40\n    },\n    quick: {\n        damping: 20,\n        mass: 1.2,\n        stiffness: 250\n    },\n    tooltip: {\n        damping: 10,\n        mass: 0.9,\n        stiffness: 100\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvY29uZmlnL3NyYy9hbmltYXRpb25zLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW1FO0FBRTVELE1BQU1DLGFBQWFELGtGQUFnQkEsQ0FBQztJQUN6QyxTQUFTO1FBQ1BFLE1BQU07UUFDTkMsVUFBVTtJQUNaO0lBQ0FDLFFBQVE7UUFDTkMsU0FBUztRQUNUQyxNQUFNO1FBQ05DLFdBQVc7SUFDYjtJQUNBQyxNQUFNO1FBQ0pILFNBQVM7UUFDVEUsV0FBVztJQUNiO0lBQ0FFLFFBQVE7UUFDTkosU0FBUztRQUNURSxXQUFXO1FBQ1hELE1BQU07SUFDUjtJQUNBSSxNQUFNO1FBQ0pMLFNBQVM7UUFDVEUsV0FBVztJQUNiO0lBQ0FJLE9BQU87UUFDTE4sU0FBUztRQUNUQyxNQUFNO1FBQ05DLFdBQVc7SUFDYjtJQUNBSyxTQUFTO1FBQ1BQLFNBQVM7UUFDVEMsTUFBTTtRQUNOQyxXQUFXO0lBQ2I7QUFDRixHQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vcGFja2FnZXMvY29uZmlnL3NyYy9hbmltYXRpb25zLnRzPzRlZGUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQW5pbWF0aW9ucyB9IGZyb20gJ0B0YW1hZ3VpL2FuaW1hdGlvbnMtcmVhY3QtbmF0aXZlJ1xuXG5leHBvcnQgY29uc3QgYW5pbWF0aW9ucyA9IGNyZWF0ZUFuaW1hdGlvbnMoe1xuICAnMTAwbXMnOiB7XG4gICAgdHlwZTogJ3RpbWluZycsXG4gICAgZHVyYXRpb246IDEwMCxcbiAgfSxcbiAgYm91bmN5OiB7XG4gICAgZGFtcGluZzogOSxcbiAgICBtYXNzOiAwLjksXG4gICAgc3RpZmZuZXNzOiAxNTAsXG4gIH0sXG4gIGxhenk6IHtcbiAgICBkYW1waW5nOiAxOCxcbiAgICBzdGlmZm5lc3M6IDUwLFxuICB9LFxuICBtZWRpdW06IHtcbiAgICBkYW1waW5nOiAxNSxcbiAgICBzdGlmZm5lc3M6IDEyMCxcbiAgICBtYXNzOiAxLFxuICB9LFxuICBzbG93OiB7XG4gICAgZGFtcGluZzogMTUsXG4gICAgc3RpZmZuZXNzOiA0MCxcbiAgfSxcbiAgcXVpY2s6IHtcbiAgICBkYW1waW5nOiAyMCxcbiAgICBtYXNzOiAxLjIsXG4gICAgc3RpZmZuZXNzOiAyNTAsXG4gIH0sXG4gIHRvb2x0aXA6IHtcbiAgICBkYW1waW5nOiAxMCxcbiAgICBtYXNzOiAwLjksXG4gICAgc3RpZmZuZXNzOiAxMDAsXG4gIH0sXG59KVxuIl0sIm5hbWVzIjpbImNyZWF0ZUFuaW1hdGlvbnMiLCJhbmltYXRpb25zIiwidHlwZSIsImR1cmF0aW9uIiwiYm91bmN5IiwiZGFtcGluZyIsIm1hc3MiLCJzdGlmZm5lc3MiLCJsYXp5IiwibWVkaXVtIiwic2xvdyIsInF1aWNrIiwidG9vbHRpcCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../packages/config/src/animations.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/config/src/fonts.ts":
/*!******************************************!*\
  !*** ../../packages/config/src/fonts.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bodyFont: () => (/* binding */ bodyFont),\n/* harmony export */   headingFont: () => (/* binding */ headingFont)\n/* harmony export */ });\n/* harmony import */ var _tamagui_font_inter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tamagui/font-inter */ \"(ssr)/../../node_modules/@tamagui/font-inter/dist/esm/index.mjs\");\n\nconst headingFont = (0,_tamagui_font_inter__WEBPACK_IMPORTED_MODULE_0__.createInterFont)({\n    size: {\n        6: 15\n    },\n    transform: {\n        6: \"uppercase\",\n        7: \"none\"\n    },\n    weight: {\n        6: \"400\",\n        7: \"700\"\n    },\n    color: {\n        6: \"$colorFocus\",\n        7: \"$color\"\n    },\n    letterSpacing: {\n        5: 2,\n        6: 1,\n        7: 0,\n        8: -1,\n        9: -2,\n        10: -3,\n        12: -4,\n        14: -5,\n        15: -6\n    },\n    face: {\n        700: {\n            normal: \"InterBold\"\n        }\n    }\n});\nconst bodyFont = (0,_tamagui_font_inter__WEBPACK_IMPORTED_MODULE_0__.createInterFont)({\n    face: {\n        700: {\n            normal: \"InterBold\"\n        }\n    }\n}, {\n    sizeSize: (size)=>Math.round(size * 1.1),\n    sizeLineHeight: (size)=>Math.round(size * 1.1 + (size > 20 ? 10 : 10))\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/config/src/fonts.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/config/src/tamagui.config.ts":
/*!***************************************************!*\
  !*** ../../packages/config/src/tamagui.config.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config)\n/* harmony export */ });\n/* harmony import */ var _tamagui_config_v4__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tamagui/config/v4 */ \"(ssr)/../../node_modules/@tamagui/config/dist/esm/v4.mjs\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tamagui */ \"(ssr)/../../node_modules/tamagui/dist/esm/createTamagui.mjs\");\n/* harmony import */ var _fonts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./fonts */ \"(ssr)/../../packages/config/src/fonts.ts\");\n/* harmony import */ var _animations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./animations */ \"(ssr)/../../packages/config/src/animations.ts\");\n\n\n\n\nconst config = (0,tamagui__WEBPACK_IMPORTED_MODULE_0__.createTamagui)({\n    ..._tamagui_config_v4__WEBPACK_IMPORTED_MODULE_1__.defaultConfig,\n    animations: _animations__WEBPACK_IMPORTED_MODULE_2__.animations,\n    fonts: {\n        body: _fonts__WEBPACK_IMPORTED_MODULE_3__.bodyFont,\n        heading: _fonts__WEBPACK_IMPORTED_MODULE_3__.headingFont\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvY29uZmlnL3NyYy90YW1hZ3VpLmNvbmZpZy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFrRDtBQUNYO0FBQ1E7QUFDTjtBQUVsQyxNQUFNSyxTQUFTSixzREFBYUEsQ0FBQztJQUNsQyxHQUFHRCw2REFBYTtJQUNoQkksVUFBVUEscURBQUFBO0lBQ1ZFLE9BQU87UUFDTEMsTUFBTUwsNENBQVFBO1FBQ2RNLFNBQVNMLCtDQUFXQTtJQUN0QjtBQUNGLEdBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9wYWNrYWdlcy9jb25maWcvc3JjL3RhbWFndWkuY29uZmlnLnRzPzJkNjAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGVmYXVsdENvbmZpZyB9IGZyb20gJ0B0YW1hZ3VpL2NvbmZpZy92NCdcbmltcG9ydCB7IGNyZWF0ZVRhbWFndWkgfSBmcm9tICd0YW1hZ3VpJ1xuaW1wb3J0IHsgYm9keUZvbnQsIGhlYWRpbmdGb250IH0gZnJvbSAnLi9mb250cydcbmltcG9ydCB7IGFuaW1hdGlvbnMgfSBmcm9tICcuL2FuaW1hdGlvbnMnXG5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBjcmVhdGVUYW1hZ3VpKHtcbiAgLi4uZGVmYXVsdENvbmZpZyxcbiAgYW5pbWF0aW9ucyxcbiAgZm9udHM6IHtcbiAgICBib2R5OiBib2R5Rm9udCxcbiAgICBoZWFkaW5nOiBoZWFkaW5nRm9udCxcbiAgfSxcbn0pXG4iXSwibmFtZXMiOlsiZGVmYXVsdENvbmZpZyIsImNyZWF0ZVRhbWFndWkiLCJib2R5Rm9udCIsImhlYWRpbmdGb250IiwiYW5pbWF0aW9ucyIsImNvbmZpZyIsImZvbnRzIiwiYm9keSIsImhlYWRpbmciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/config/src/tamagui.config.ts\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_provider_NextTamaguiProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/provider/NextTamaguiProvider */ \"(rsc)/../../packages/app/provider/NextTamaguiProvider.tsx\");\n\n\nconst metadata = {\n    title: \"Tamagui • App Router\",\n    description: \"Tamagui, Solito, Expo & Next.js\",\n    icons: \"/favicon.ico\"\n};\nfunction RootLayout({ children }) {\n    return(// You can use `suppressHydrationWarning` to avoid the warning about mismatched content during hydration in dev mode\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_provider_NextTamaguiProvider__WEBPACK_IMPORTED_MODULE_1__.NextTamaguiProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/app/layout.tsx\",\n                lineNumber: 15,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/app/layout.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/app/layout.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDc0U7QUFFL0QsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxPQUFPO0FBQ1QsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFBRUMsUUFBUSxFQUFpQztJQUM1RSxPQUNFLG9IQUFvSDtrQkFDcEgsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0Msd0JBQXdCO2tCQUN0Qyw0RUFBQ0M7c0JBQ0MsNEVBQUNWLGlGQUFtQkE7MEJBQUVNOzs7Ozs7Ozs7Ozs7Ozs7O0FBSTlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgTmV4dFRhbWFndWlQcm92aWRlciB9IGZyb20gJ2FwcC9wcm92aWRlci9OZXh0VGFtYWd1aVByb3ZpZGVyJ1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1RhbWFndWkg4oCiIEFwcCBSb3V0ZXInLFxuICBkZXNjcmlwdGlvbjogJ1RhbWFndWksIFNvbGl0bywgRXhwbyAmIE5leHQuanMnLFxuICBpY29uczogJy9mYXZpY29uLmljbycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICByZXR1cm4gKFxuICAgIC8vIFlvdSBjYW4gdXNlIGBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmdgIHRvIGF2b2lkIHRoZSB3YXJuaW5nIGFib3V0IG1pc21hdGNoZWQgY29udGVudCBkdXJpbmcgaHlkcmF0aW9uIGluIGRldiBtb2RlXG4gICAgPGh0bWwgbGFuZz1cImVuXCIgc3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nPlxuICAgICAgPGJvZHk+XG4gICAgICAgIDxOZXh0VGFtYWd1aVByb3ZpZGVyPntjaGlsZHJlbn08L05leHRUYW1hZ3VpUHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiTmV4dFRhbWFndWlQcm92aWRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImljb25zIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmciLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/user/[id]/page.tsx":
/*!********************************!*\
  !*** ./app/user/[id]/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/app/user/[id]/page.tsx#default`));


/***/ }),

/***/ "(rsc)/../../packages/app/provider/NextTamaguiProvider.tsx":
/*!***********************************************************!*\
  !*** ../../packages/app/provider/NextTamaguiProvider.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NextTamaguiProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/NextTamaguiProvider.tsx#NextTamaguiProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@tamagui","vendor-chunks/react-native-web","vendor-chunks/next","vendor-chunks/@babel","vendor-chunks/tamagui","vendor-chunks/expo-constants","vendor-chunks/expo-modules-core","vendor-chunks/react-native-svg","vendor-chunks/solito","vendor-chunks/color2k","vendor-chunks/@react-native","vendor-chunks/postcss-value-parser","vendor-chunks/inline-style-prefixer","vendor-chunks/css-in-js-utils","vendor-chunks/styleq","vendor-chunks/fbjs","vendor-chunks/memoize-one","vendor-chunks/hyphenate-style-name","vendor-chunks/nullthrows"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fuser%2F%5Bid%5D%2Fpage&page=%2Fuser%2F%5Bid%5D%2Fpage&appPaths=%2Fuser%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fuser%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fapps%2Fnext%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzhe%2FDeveloper%2FFullStackProjects%2FFishTouchStore%2Ffishtouch-store%2Fapps%2Fnext&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();