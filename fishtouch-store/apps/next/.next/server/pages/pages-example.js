"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/pages-example";
exports.ids = ["pages/pages-example"];
exports.modules = {

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fpages-example&preferredRegion=&absolutePagePath=.%2Fpages%2Fpages-example%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fpages-example&preferredRegion=&absolutePagePath=.%2Fpages%2Fpages-example%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"../../node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_pages_example_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/pages-example/index.tsx */ \"./pages/pages-example/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__, private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_pages_example_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__, private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_pages_example_index_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pages_example_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pages_example_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pages_example_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pages_example_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pages_example_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pages_example_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pages_example_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pages_example_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pages_example_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pages_example_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pages_example_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/pages-example\",\n        pathname: \"/pages-example\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_pages_example_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fpages-example&preferredRegion=&absolutePagePath=.%2Fpages%2Fpages-example%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tamagui_core_reset_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tamagui/core/reset.css */ \"../../node_modules/@tamagui/core/reset.css\");\n/* harmony import */ var _tamagui_core_reset_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_tamagui_core_reset_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tamagui_font_inter_css_400_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tamagui/font-inter/css/400.css */ \"../../node_modules/@tamagui/font-inter/css/400.css\");\n/* harmony import */ var _tamagui_font_inter_css_400_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_tamagui_font_inter_css_400_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _tamagui_font_inter_css_700_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tamagui/font-inter/css/700.css */ \"../../node_modules/@tamagui/font-inter/css/700.css\");\n/* harmony import */ var _tamagui_font_inter_css_700_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_tamagui_font_inter_css_700_css__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var raf_polyfill__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! raf/polyfill */ \"raf/polyfill\");\n/* harmony import */ var raf_polyfill__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(raf_polyfill__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var app_provider_NextTamaguiProvider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! app/provider/NextTamaguiProvider */ \"../../packages/app/provider/NextTamaguiProvider.tsx\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @my/ui */ \"../../packages/config/src/tamagui.config.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_my_ui__WEBPACK_IMPORTED_MODULE_6__, app_provider_NextTamaguiProvider__WEBPACK_IMPORTED_MODULE_7__]);\n([_my_ui__WEBPACK_IMPORTED_MODULE_6__, app_provider_NextTamaguiProvider__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nif (false) {}\nfunction MyApp({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_5___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Tamagui • Pages Router\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/pages/_app.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Tamagui, Solito, Expo & Next.js\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/pages/_app.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/pages/_app.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                        dangerouslySetInnerHTML: {\n                            // the first time this runs you'll get the full CSS including all themes\n                            // after that, it will only return CSS generated since the last call\n                            __html: _my_ui__WEBPACK_IMPORTED_MODULE_6__.config.getNewCSS()\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/pages/_app.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                        dangerouslySetInnerHTML: {\n                            __html: _my_ui__WEBPACK_IMPORTED_MODULE_6__.config.getCSS({\n                                // if you are using \"outputCSS\" option, you should use this \"exclude\"\n                                // if not, then you can leave the option out\n                                exclude:  false ? 0 : null\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/pages/_app.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            // avoid flash of animated things on enter:\n                            __html: `document.documentElement.classList.add('t_unmounted')`\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/pages/_app.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/pages/_app.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_provider_NextTamaguiProvider__WEBPACK_IMPORTED_MODULE_7__.NextTamaguiProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                    ...pageProps\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/pages/_app.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/pages/_app.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/document */ \"../../node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @my/ui */ \"../../packages/config/src/tamagui.config.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_my_ui__WEBPACK_IMPORTED_MODULE_5__]);\n_my_ui__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nclass Document extends (next_document__WEBPACK_IMPORTED_MODULE_3___default()) {\n    static async getInitialProps(ctx) {\n        react_native__WEBPACK_IMPORTED_MODULE_4__.AppRegistry.registerComponent(\"Main\", ()=>next_document__WEBPACK_IMPORTED_MODULE_3__.Main);\n        const page = await ctx.renderPage();\n        // @ts-ignore\n        const { getStyleElement } = react_native__WEBPACK_IMPORTED_MODULE_4__.AppRegistry.getApplication(\"Main\");\n        /**\n     * Note: be sure to keep tamagui styles after react-native-web styles like it is here!\n     * So Tamagui styles can override the react-native-web styles.\n     */ const styles = [\n            getStyleElement(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: _my_ui__WEBPACK_IMPORTED_MODULE_5__.config.getCSS({\n                        exclude:  true ? null : 0\n                    })\n                }\n            }, \"tamagui-css\", false, {\n                fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/pages/_document.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"3480a98ff4a8efc3\",\n                children: 'html{font-family:\"Inter\"}'\n            }, void 0, false, void 0, this)\n        ];\n        return {\n            ...page,\n            styles: react__WEBPACK_IMPORTED_MODULE_2__.Children.toArray(styles)\n        };\n    }\n    render() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_3__.Html, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_3__.Head, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-UA-Compatible\",\n                        content: \"IE=edge\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/pages/_document.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/pages/_document.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_3__.Main, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/pages/_document.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_3__.NextScript, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/pages/_document.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/pages/_document.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/pages/_document.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this);\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_document.tsx\n");

/***/ }),

/***/ "./pages/pages-example/index.tsx":
/*!***************************************!*\
  !*** ./pages/pages-example/index.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_features_home_screen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/features/home/<USER>/ \"../../packages/app/features/home/<USER>");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([app_features_home_screen__WEBPACK_IMPORTED_MODULE_1__]);\napp_features_home_screen__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_features_home_screen__WEBPACK_IMPORTED_MODULE_1__.HomeScreen, {\n        pagesMode: true\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/apps/next/pages/pages-example/index.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9wYWdlcy1leGFtcGxlL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFxRDtBQUV0QyxTQUFTQztJQUN0QixxQkFBTyw4REFBQ0QsZ0VBQVVBO1FBQUNFLFdBQVc7Ozs7OztBQUNoQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4vcGFnZXMvcGFnZXMtZXhhbXBsZS9pbmRleC50c3g/YzcxMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIb21lU2NyZWVuIH0gZnJvbSAnYXBwL2ZlYXR1cmVzL2hvbWUvc2NyZWVuJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQYWdlKCkge1xuICByZXR1cm4gPEhvbWVTY3JlZW4gcGFnZXNNb2RlPXt0cnVlfSAvPlxufVxuIl0sIm5hbWVzIjpbIkhvbWVTY3JlZW4iLCJQYWdlIiwicGFnZXNNb2RlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/pages-example/index.tsx\n");

/***/ }),

/***/ "../../packages/app/features/home/<USER>":
/*!***************************************************!*\
  !*** ../../packages/app/features/home/<USER>
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HomeScreen: () => (/* binding */ HomeScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @my/ui */ \"../../node_modules/@tamagui/stacks/dist/esm/Stacks.mjs\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/SwitchRouterButton.tsx\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/SwitchThemeButton.tsx\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @my/ui */ \"../../node_modules/@tamagui/text/dist/esm/Headings.mjs\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @my/ui */ \"../../node_modules/@tamagui/text/dist/esm/Paragraph.mjs\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @my/ui */ \"../../node_modules/@tamagui/separator/dist/esm/Separator.mjs\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @my/ui */ \"../../node_modules/@tamagui/button/dist/esm/Button.mjs\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @my/ui */ \"../../node_modules/@tamagui/toast/dist/esm/ToastImperative.mjs\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @my/ui */ \"../../node_modules/@tamagui/sheet/dist/esm/Sheet.mjs\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @my/ui */ \"../../node_modules/tamagui/dist/esm/views/Anchor.mjs\");\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/icons/Package.mjs\");\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/icons/ChevronDown.mjs\");\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/icons/ChevronUp.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/build/app/navigation/use-link.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_my_ui__WEBPACK_IMPORTED_MODULE_5__, _my_ui__WEBPACK_IMPORTED_MODULE_6__]);\n([_my_ui__WEBPACK_IMPORTED_MODULE_5__, _my_ui__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nfunction HomeScreen({ pagesMode = false }) {\n    const linkTarget = pagesMode ? \"/pages-example-user\" : \"/user\";\n    const linkProps = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_2__.useLink)({\n        href: `${linkTarget}/nate`\n    });\n    const inventoryLinkProps = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_2__.useLink)({\n        href: \"/inventory\"\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.YStack, {\n        \"data-at\": \"screen.tsx:30\",\n        \"data-in\": \"HomeScreen\",\n        \"data-is\": \"YStack\",\n        flex: 1,\n        justify: \"center\",\n        items: \"center\",\n        gap: \"$8\",\n        p: \"$4\",\n        bg: \"$background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                \"data-at\": \"screen.tsx:31-39\",\n                \"data-in\": \"HomeScreen\",\n                \"data-is\": \"XStack\",\n                position: \"absolute\",\n                width: \"100%\",\n                t: \"$6\",\n                gap: \"$6\",\n                justify: \"center\",\n                flexWrap: \"wrap\",\n                $sm: {\n                    position: \"relative\",\n                    t: 0\n                },\n                children: react_native__WEBPACK_IMPORTED_MODULE_4__.Platform.OS === \"web\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.SwitchRouterButton, {\n                            pagesMode: pagesMode\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                            lineNumber: 24,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.SwitchThemeButton, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                            lineNumber: 25,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.YStack, {\n                \"data-at\": \"screen.tsx:48\",\n                \"data-in\": \"HomeScreen\",\n                \"data-is\": \"YStack\",\n                gap: \"$4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.H1, {\n                        \"data-at\": \"screen.tsx:49\",\n                        \"data-in\": \"HomeScreen\",\n                        \"data-is\": \"H1\",\n                        text: \"center\",\n                        color: \"$color12\",\n                        children: \"Welcome to FishTouch Store\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.Paragraph, {\n                        \"data-at\": \"screen.tsx:52\",\n                        \"data-in\": \"HomeScreen\",\n                        \"data-is\": \"Paragraph\",\n                        color: \"$color10\",\n                        text: \"center\",\n                        children: \"Your comprehensive aquarium management solution.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.Separator, {\n                        \"data-at\": \"screen.tsx:55\",\n                        \"data-in\": \"HomeScreen\",\n                        \"data-is\": \"Separator\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.Paragraph, {\n                        \"data-at\": \"screen.tsx:56\",\n                        \"data-in\": \"HomeScreen\",\n                        \"data-is\": \"Paragraph\",\n                        text: \"center\",\n                        children: \"This app includes inventory management, stock tracking, and more.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.Separator, {\n                        \"data-at\": \"screen.tsx:59\",\n                        \"data-in\": \"HomeScreen\",\n                        \"data-is\": \"Separator\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.YStack, {\n                \"data-at\": \"screen.tsx:62\",\n                \"data-in\": \"HomeScreen\",\n                \"data-is\": \"YStack\",\n                gap: \"$3\",\n                width: \"100%\",\n                maxWidth: 300,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                        \"data-at\": \"screen.tsx:63\",\n                        \"data-in\": \"HomeScreen\",\n                        \"data-is\": \"Button\",\n                        ...inventoryLinkProps,\n                        icon: _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_11__.Package,\n                        size: \"$5\",\n                        children: \"Inventory Management\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                        \"data-at\": \"screen.tsx:66\",\n                        \"data-in\": \"HomeScreen\",\n                        \"data-is\": \"Button\",\n                        ...linkProps,\n                        variant: \"outlined\",\n                        children: \"User Profile\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SheetDemo, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n        lineNumber: 18,\n        columnNumber: 10\n    }, this);\n}\nfunction SheetDemo() {\n    const toast = (0,_my_ui__WEBPACK_IMPORTED_MODULE_12__.useToastController)();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                \"data-at\": \"screen.tsx:84-89\",\n                \"data-is\": \"Button\",\n                size: \"$6\",\n                icon: open ? _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_13__.ChevronDown : _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_14__.ChevronUp,\n                circular: true,\n                onPress: ()=>setOpen((x)=>!x)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.Sheet, {\n                modal: true,\n                animation: \"medium\",\n                open: open,\n                onOpenChange: setOpen,\n                snapPoints: [\n                    80\n                ],\n                position: position,\n                onPositionChange: setPosition,\n                dismissOnSnapToBottom: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.Sheet.Overlay, {\n                        bg: \"$shadow4\",\n                        animation: \"lazy\",\n                        enterStyle: {\n                            opacity: 0\n                        },\n                        exitStyle: {\n                            opacity: 0\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.Sheet.Handle, {\n                        bg: \"$color8\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.Sheet.Frame, {\n                        items: \"center\",\n                        justify: \"center\",\n                        gap: \"$10\",\n                        bg: \"$color2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                                \"data-at\": \"screen.tsx:108\",\n                                \"data-is\": \"XStack\",\n                                gap: \"$2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.Paragraph, {\n                                        \"data-at\": \"screen.tsx:109\",\n                                        \"data-is\": \"Paragraph\",\n                                        text: \"center\",\n                                        children: \"Made by\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_16__.Anchor, {\n                                        \"data-at\": \"screen.tsx:110\",\n                                        \"data-is\": \"Anchor\",\n                                        color: \"$blue10\",\n                                        href: \"https://twitter.com/natebirdman\",\n                                        target: \"_blank\",\n                                        children: \"@natebirdman,\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_16__.Anchor, {\n                                        \"data-at\": \"screen.tsx:113-118\",\n                                        \"data-is\": \"Anchor\",\n                                        color: \"$blue10\",\n                                        href: \"https://github.com/tamagui/tamagui\",\n                                        target: \"_blank\",\n                                        rel: \"noreferrer\",\n                                        children: \"give it a ⭐️\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                \"data-at\": \"screen.tsx:123-133\",\n                                \"data-is\": \"Button\",\n                                size: \"$6\",\n                                circular: true,\n                                icon: _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_13__.ChevronDown,\n                                onPress: ()=>{\n                                    setOpen(false);\n                                    toast.show(\"Sheet closed!\", {\n                                        message: \"Just showing how toast works...\"\n                                    });\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/features/home/<USER>",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXBwL2ZlYXR1cmVzL2hvbWUvc2NyZWVuLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFZZTtBQUN3RDtBQUN2QztBQUNPO0FBQ0k7QUFFcEMsU0FBU2lCLFdBQVcsRUFBRUMsWUFBWSxPQUFnQztJQUN2RSxNQUFNQyxhQUFhRCxZQUFZLHdCQUF3QjtJQUN2RCxNQUFNRSxZQUFZSiwwREFBT0EsQ0FBQztRQUN4QkssTUFBTSxHQUFHRixXQUFVO0lBQ3JCO0lBRUEsTUFBTUcscUJBQXFCTiwwREFBT0EsQ0FBQztRQUNqQ0ssTUFBTTtJQUNSO0lBRUEscUJBQ0UsOERBQUNYLDBDQUFNQTtRQUFBYSxXQUFBO1FBQUFDLFdBQUE7UUFBQUMsV0FBQTtRQUFDQyxNQUFNO1FBQUdDLFNBQVE7UUFBU0MsT0FBTTtRQUFTQyxLQUFJO1FBQUtDLEdBQUU7UUFBS0MsSUFBRzs7MEJBQ2xFLDhEQUFDdEIsMENBQU1BO2dCQUFBYyxXQUFBO2dCQUFBQyxXQUFBO2dCQUFBQyxXQUFBO2dCQUNMTyxVQUFTO2dCQUNUQyxPQUFNO2dCQUNOQyxHQUFFO2dCQUNGTCxLQUFJO2dCQUNKRixTQUFRO2dCQUNSUSxVQUFTO2dCQUNUQyxLQUFLO29CQUFFSixVQUFVO29CQUFZRSxHQUFHO2dCQUFFOzBCQUVqQ25CLGtEQUFRQSxDQUFDc0IsRUFBRSxLQUFLLHVCQUNmOztzQ0FDRSw4REFBQy9CLHNEQUFrQkE7NEJBQUNZLFdBQVdBOzs7Ozs7c0NBQy9CLDhEQUFDWCxxREFBaUJBOzs7Ozs7Ozs7Ozs7MEJBS3hCLDhEQUFDRywwQ0FBTUE7Z0JBQUFhLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUNJLEtBQUk7O2tDQUNWLDhEQUFDM0Isc0NBQUVBO3dCQUFBcUIsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQ2EsTUFBSzt3QkFBU0MsT0FBTTtrQ0FBVTs7Ozs7O2tDQUdsQyw4REFBQ3BDLDZDQUFTQTt3QkFBQW9CLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUNjLE9BQU07d0JBQVdELE1BQUs7a0NBQVE7Ozs7OztrQ0FHekMsOERBQUNsQyw2Q0FBU0E7d0JBQUFtQixXQUFBO3dCQUFBQyxXQUFBO3dCQUFBQyxXQUFBOzs7Ozs7a0NBQ1YsOERBQUN0Qiw2Q0FBU0E7d0JBQUFvQixXQUFBO3dCQUFBQyxXQUFBO3dCQUFBQyxXQUFBO3dCQUFDYSxNQUFLO2tDQUFROzs7Ozs7a0NBR3hCLDhEQUFDbEMsNkNBQVNBO3dCQUFBbUIsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQUMsV0FBQTs7Ozs7Ozs7Ozs7OzBCQUdaLDhEQUFDZiwwQ0FBTUE7Z0JBQUFhLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUNJLEtBQUk7Z0JBQUtJLE9BQU07Z0JBQU9PLFVBQVU7O2tDQUN0Qyw4REFBQ3ZDLDJDQUFNQTt3QkFBQXNCLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUMsR0FBSUgsa0JBQWtCO3dCQUFFbUIsTUFBTTVCLDJEQUFPQTt3QkFBRTZCLE1BQUs7a0NBQUk7Ozs7OztrQ0FHeEQsOERBQUN6QywyQ0FBTUE7d0JBQUFzQixXQUFBO3dCQUFBQyxXQUFBO3dCQUFBQyxXQUFBO3dCQUFDLEdBQUlMLFNBQVM7d0JBQUV1QixTQUFRO2tDQUFVOzs7Ozs7Ozs7Ozs7MEJBSzNDLDhEQUFDQzs7Ozs7Ozs7Ozs7QUFHUDtBQUVBLFNBQVNBO0lBQ1AsTUFBTUMsUUFBUXJDLDJEQUFrQkE7SUFFaEMsTUFBTSxDQUFDc0MsTUFBTUMsUUFBUSxHQUFHakMsK0NBQVFBLENBQUM7SUFDakMsTUFBTSxDQUFDa0IsVUFBVWdCLFlBQVksR0FBR2xDLCtDQUFRQSxDQUFDO0lBRXpDLHFCQUNFOzswQkFDRSw4REFBQ2IsMkNBQU1BO2dCQUFBc0IsV0FBQTtnQkFBQUUsV0FBQTtnQkFDTGlCLE1BQUs7Z0JBQ0xELE1BQU1LLE9BQU9uQywrREFBV0EsR0FBR0MsNkRBQVNBO2dCQUNwQ3FDLFFBQVE7Z0JBQ1JDLFNBQVMsSUFBTUgsUUFBU0ksQ0FBQUEsSUFBTSxDQUFDQTs7Ozs7OzBCQUVqQyw4REFBQzlDLDBDQUFLQTtnQkFDSitDLEtBQUs7Z0JBQ0xDLFdBQVU7Z0JBQ1ZQLE1BQU1BO2dCQUNOUSxjQUFjUDtnQkFDZFEsWUFBWTtvQkFBQztpQkFBRztnQkFDaEJ2QixVQUFVQTtnQkFDVndCLGtCQUFrQlI7Z0JBQ2xCUyxxQkFBcUI7O2tDQUVyQiw4REFBQ3BELDBDQUFLQSxDQUFDcUQsT0FBTzt3QkFDWjNCLElBQUc7d0JBQ0hzQixXQUFVO3dCQUNWTSxZQUFZOzRCQUFFQyxTQUFTO3dCQUFFO3dCQUN6QkMsV0FBVzs0QkFBRUQsU0FBUzt3QkFBRTs7Ozs7O2tDQUUxQiw4REFBQ3ZELDBDQUFLQSxDQUFDeUQsTUFBTTt3QkFBQy9CLElBQUc7Ozs7OztrQ0FDakIsOERBQUMxQiwwQ0FBS0EsQ0FBQzBELEtBQUs7d0JBQUNuQyxPQUFNO3dCQUFTRCxTQUFRO3dCQUFTRSxLQUFJO3dCQUFNRSxJQUFHOzswQ0FDeEQsOERBQUN0QiwwQ0FBTUE7Z0NBQUFjLFdBQUE7Z0NBQUFFLFdBQUE7Z0NBQUNJLEtBQUk7O2tEQUNWLDhEQUFDMUIsNkNBQVNBO3dDQUFBb0IsV0FBQTt3Q0FBQUUsV0FBQTt3Q0FBQ2EsTUFBSztrREFBUzs7Ozs7O2tEQUN6Qiw4REFBQ3RDLDJDQUFNQTt3Q0FBQXVCLFdBQUE7d0NBQUFFLFdBQUE7d0NBQUNjLE9BQU07d0NBQVVsQixNQUFLO3dDQUFrQzJDLFFBQU87a0RBQVE7Ozs7OztrREFHOUUsOERBQUNoRSwyQ0FBTUE7d0NBQUF1QixXQUFBO3dDQUFBRSxXQUFBO3dDQUNMYyxPQUFNO3dDQUNObEIsTUFBSzt3Q0FDTDJDLFFBQU87d0NBQ1BDLEtBQUk7a0RBQVk7Ozs7Ozs7Ozs7OzswQ0FNcEIsOERBQUNoRSwyQ0FBTUE7Z0NBQUFzQixXQUFBO2dDQUFBRSxXQUFBO2dDQUNMaUIsTUFBSztnQ0FDTE8sUUFBUTtnQ0FDUlIsTUFBTTlCLCtEQUFXQTtnQ0FDakJ1QyxTQUFTO29DQUNQSCxRQUFRO29DQUNSRixNQUFNcUIsSUFBSSxDQUFDLGlCQUFpQjt3Q0FDMUJDLFNBQVM7b0NBQ1g7Z0NBQ0Y7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTVoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9wYWNrYWdlcy9hcHAvZmVhdHVyZXMvaG9tZS9zY3JlZW4udHN4PzZkMmIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgQW5jaG9yLFxuICBCdXR0b24sXG4gIEgxLFxuICBQYXJhZ3JhcGgsXG4gIFNlcGFyYXRvcixcbiAgU2hlZXQsXG4gIFN3aXRjaFJvdXRlckJ1dHRvbixcbiAgU3dpdGNoVGhlbWVCdXR0b24sXG4gIHVzZVRvYXN0Q29udHJvbGxlcixcbiAgWFN0YWNrLFxuICBZU3RhY2tcbn0gZnJvbSAnQG15L3VpJ1xuaW1wb3J0IHsgQ2hldnJvbkRvd24sIENoZXZyb25VcCwgUGFja2FnZSB9IGZyb20gJ0B0YW1hZ3VpL2x1Y2lkZS1pY29ucydcbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBQbGF0Zm9ybSB9IGZyb20gJ3JlYWN0LW5hdGl2ZSdcbmltcG9ydCB7IHVzZUxpbmsgfSBmcm9tICdzb2xpdG8vbmF2aWdhdGlvbidcblxuZXhwb3J0IGZ1bmN0aW9uIEhvbWVTY3JlZW4oeyBwYWdlc01vZGUgPSBmYWxzZSB9OiB7IHBhZ2VzTW9kZT86IGJvb2xlYW4gfSkge1xuICBjb25zdCBsaW5rVGFyZ2V0ID0gcGFnZXNNb2RlID8gJy9wYWdlcy1leGFtcGxlLXVzZXInIDogJy91c2VyJ1xuICBjb25zdCBsaW5rUHJvcHMgPSB1c2VMaW5rKHtcbiAgICBocmVmOiBgJHtsaW5rVGFyZ2V0fS9uYXRlYCxcbiAgfSlcblxuICBjb25zdCBpbnZlbnRvcnlMaW5rUHJvcHMgPSB1c2VMaW5rKHtcbiAgICBocmVmOiAnL2ludmVudG9yeScsXG4gIH0pXG5cbiAgcmV0dXJuIChcbiAgICA8WVN0YWNrIGZsZXg9ezF9IGp1c3RpZnk9XCJjZW50ZXJcIiBpdGVtcz1cImNlbnRlclwiIGdhcD1cIiQ4XCIgcD1cIiQ0XCIgYmc9XCIkYmFja2dyb3VuZFwiPlxuICAgICAgPFhTdGFja1xuICAgICAgICBwb3NpdGlvbj1cImFic29sdXRlXCJcbiAgICAgICAgd2lkdGg9XCIxMDAlXCJcbiAgICAgICAgdD1cIiQ2XCJcbiAgICAgICAgZ2FwPVwiJDZcIlxuICAgICAgICBqdXN0aWZ5PVwiY2VudGVyXCJcbiAgICAgICAgZmxleFdyYXA9XCJ3cmFwXCJcbiAgICAgICAgJHNtPXt7IHBvc2l0aW9uOiAncmVsYXRpdmUnLCB0OiAwIH19XG4gICAgICA+XG4gICAgICAgIHtQbGF0Zm9ybS5PUyA9PT0gJ3dlYicgJiYgKFxuICAgICAgICAgIDw+XG4gICAgICAgICAgICA8U3dpdGNoUm91dGVyQnV0dG9uIHBhZ2VzTW9kZT17cGFnZXNNb2RlfSAvPlxuICAgICAgICAgICAgPFN3aXRjaFRoZW1lQnV0dG9uIC8+XG4gICAgICAgICAgPC8+XG4gICAgICAgICl9XG4gICAgICA8L1hTdGFjaz5cblxuICAgICAgPFlTdGFjayBnYXA9XCIkNFwiPlxuICAgICAgICA8SDEgdGV4dD1cImNlbnRlclwiIGNvbG9yPVwiJGNvbG9yMTJcIj5cbiAgICAgICAgICBXZWxjb21lIHRvIEZpc2hUb3VjaCBTdG9yZVxuICAgICAgICA8L0gxPlxuICAgICAgICA8UGFyYWdyYXBoIGNvbG9yPVwiJGNvbG9yMTBcIiB0ZXh0PVwiY2VudGVyXCI+XG4gICAgICAgICAgWW91ciBjb21wcmVoZW5zaXZlIGFxdWFyaXVtIG1hbmFnZW1lbnQgc29sdXRpb24uXG4gICAgICAgIDwvUGFyYWdyYXBoPlxuICAgICAgICA8U2VwYXJhdG9yIC8+XG4gICAgICAgIDxQYXJhZ3JhcGggdGV4dD1cImNlbnRlclwiPlxuICAgICAgICAgIFRoaXMgYXBwIGluY2x1ZGVzIGludmVudG9yeSBtYW5hZ2VtZW50LCBzdG9jayB0cmFja2luZywgYW5kIG1vcmUuXG4gICAgICAgIDwvUGFyYWdyYXBoPlxuICAgICAgICA8U2VwYXJhdG9yIC8+XG4gICAgICA8L1lTdGFjaz5cblxuICAgICAgPFlTdGFjayBnYXA9XCIkM1wiIHdpZHRoPVwiMTAwJVwiIG1heFdpZHRoPXszMDB9PlxuICAgICAgICA8QnV0dG9uIHsuLi5pbnZlbnRvcnlMaW5rUHJvcHN9IGljb249e1BhY2thZ2V9IHNpemU9XCIkNVwiPlxuICAgICAgICAgIEludmVudG9yeSBNYW5hZ2VtZW50XG4gICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8QnV0dG9uIHsuLi5saW5rUHJvcHN9IHZhcmlhbnQ9XCJvdXRsaW5lZFwiPlxuICAgICAgICAgIFVzZXIgUHJvZmlsZVxuICAgICAgICA8L0J1dHRvbj5cbiAgICAgIDwvWVN0YWNrPlxuXG4gICAgICA8U2hlZXREZW1vIC8+XG4gICAgPC9ZU3RhY2s+XG4gIClcbn1cblxuZnVuY3Rpb24gU2hlZXREZW1vKCkge1xuICBjb25zdCB0b2FzdCA9IHVzZVRvYXN0Q29udHJvbGxlcigpXG5cbiAgY29uc3QgW29wZW4sIHNldE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtwb3NpdGlvbiwgc2V0UG9zaXRpb25dID0gdXNlU3RhdGUoMClcblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICA8QnV0dG9uXG4gICAgICAgIHNpemU9XCIkNlwiXG4gICAgICAgIGljb249e29wZW4gPyBDaGV2cm9uRG93biA6IENoZXZyb25VcH1cbiAgICAgICAgY2lyY3VsYXJcbiAgICAgICAgb25QcmVzcz17KCkgPT4gc2V0T3BlbigoeCkgPT4gIXgpfVxuICAgICAgLz5cbiAgICAgIDxTaGVldFxuICAgICAgICBtb2RhbFxuICAgICAgICBhbmltYXRpb249XCJtZWRpdW1cIlxuICAgICAgICBvcGVuPXtvcGVufVxuICAgICAgICBvbk9wZW5DaGFuZ2U9e3NldE9wZW59XG4gICAgICAgIHNuYXBQb2ludHM9e1s4MF19XG4gICAgICAgIHBvc2l0aW9uPXtwb3NpdGlvbn1cbiAgICAgICAgb25Qb3NpdGlvbkNoYW5nZT17c2V0UG9zaXRpb259XG4gICAgICAgIGRpc21pc3NPblNuYXBUb0JvdHRvbVxuICAgICAgPlxuICAgICAgICA8U2hlZXQuT3ZlcmxheVxuICAgICAgICAgIGJnPVwiJHNoYWRvdzRcIlxuICAgICAgICAgIGFuaW1hdGlvbj1cImxhenlcIlxuICAgICAgICAgIGVudGVyU3R5bGU9e3sgb3BhY2l0eTogMCB9fVxuICAgICAgICAgIGV4aXRTdHlsZT17eyBvcGFjaXR5OiAwIH19XG4gICAgICAgIC8+XG4gICAgICAgIDxTaGVldC5IYW5kbGUgYmc9XCIkY29sb3I4XCIgLz5cbiAgICAgICAgPFNoZWV0LkZyYW1lIGl0ZW1zPVwiY2VudGVyXCIganVzdGlmeT1cImNlbnRlclwiIGdhcD1cIiQxMFwiIGJnPVwiJGNvbG9yMlwiPlxuICAgICAgICAgIDxYU3RhY2sgZ2FwPVwiJDJcIj5cbiAgICAgICAgICAgIDxQYXJhZ3JhcGggdGV4dD1cImNlbnRlclwiPk1hZGUgYnk8L1BhcmFncmFwaD5cbiAgICAgICAgICAgIDxBbmNob3IgY29sb3I9XCIkYmx1ZTEwXCIgaHJlZj1cImh0dHBzOi8vdHdpdHRlci5jb20vbmF0ZWJpcmRtYW5cIiB0YXJnZXQ9XCJfYmxhbmtcIj5cbiAgICAgICAgICAgICAgQG5hdGViaXJkbWFuLFxuICAgICAgICAgICAgPC9BbmNob3I+XG4gICAgICAgICAgICA8QW5jaG9yXG4gICAgICAgICAgICAgIGNvbG9yPVwiJGJsdWUxMFwiXG4gICAgICAgICAgICAgIGhyZWY9XCJodHRwczovL2dpdGh1Yi5jb20vdGFtYWd1aS90YW1hZ3VpXCJcbiAgICAgICAgICAgICAgdGFyZ2V0PVwiX2JsYW5rXCJcbiAgICAgICAgICAgICAgcmVsPVwibm9yZWZlcnJlclwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIGdpdmUgaXQgYSDirZDvuI9cbiAgICAgICAgICAgIDwvQW5jaG9yPlxuICAgICAgICAgIDwvWFN0YWNrPlxuXG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgc2l6ZT1cIiQ2XCJcbiAgICAgICAgICAgIGNpcmN1bGFyXG4gICAgICAgICAgICBpY29uPXtDaGV2cm9uRG93bn1cbiAgICAgICAgICAgIG9uUHJlc3M9eygpID0+IHtcbiAgICAgICAgICAgICAgc2V0T3BlbihmYWxzZSlcbiAgICAgICAgICAgICAgdG9hc3Quc2hvdygnU2hlZXQgY2xvc2VkIScsIHtcbiAgICAgICAgICAgICAgICBtZXNzYWdlOiAnSnVzdCBzaG93aW5nIGhvdyB0b2FzdCB3b3Jrcy4uLicsXG4gICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICB9fVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvU2hlZXQuRnJhbWU+XG4gICAgICA8L1NoZWV0PlxuICAgIDwvPlxuICApXG59XG4iXSwibmFtZXMiOlsiQW5jaG9yIiwiQnV0dG9uIiwiSDEiLCJQYXJhZ3JhcGgiLCJTZXBhcmF0b3IiLCJTaGVldCIsIlN3aXRjaFJvdXRlckJ1dHRvbiIsIlN3aXRjaFRoZW1lQnV0dG9uIiwidXNlVG9hc3RDb250cm9sbGVyIiwiWFN0YWNrIiwiWVN0YWNrIiwiQ2hldnJvbkRvd24iLCJDaGV2cm9uVXAiLCJQYWNrYWdlIiwidXNlU3RhdGUiLCJQbGF0Zm9ybSIsInVzZUxpbmsiLCJIb21lU2NyZWVuIiwicGFnZXNNb2RlIiwibGlua1RhcmdldCIsImxpbmtQcm9wcyIsImhyZWYiLCJpbnZlbnRvcnlMaW5rUHJvcHMiLCJkYXRhLWF0IiwiZGF0YS1pbiIsImRhdGEtaXMiLCJmbGV4IiwianVzdGlmeSIsIml0ZW1zIiwiZ2FwIiwicCIsImJnIiwicG9zaXRpb24iLCJ3aWR0aCIsInQiLCJmbGV4V3JhcCIsIiRzbSIsIk9TIiwidGV4dCIsImNvbG9yIiwibWF4V2lkdGgiLCJpY29uIiwic2l6ZSIsInZhcmlhbnQiLCJTaGVldERlbW8iLCJ0b2FzdCIsIm9wZW4iLCJzZXRPcGVuIiwic2V0UG9zaXRpb24iLCJjaXJjdWxhciIsIm9uUHJlc3MiLCJ4IiwibW9kYWwiLCJhbmltYXRpb24iLCJvbk9wZW5DaGFuZ2UiLCJzbmFwUG9pbnRzIiwib25Qb3NpdGlvbkNoYW5nZSIsImRpc21pc3NPblNuYXBUb0JvdHRvbSIsIk92ZXJsYXkiLCJlbnRlclN0eWxlIiwib3BhY2l0eSIsImV4aXRTdHlsZSIsIkhhbmRsZSIsIkZyYW1lIiwidGFyZ2V0IiwicmVsIiwic2hvdyIsIm1lc3NhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/app/features/home/<USER>");

/***/ }),

/***/ "../../packages/app/provider/NextTamaguiProvider.tsx":
/*!***********************************************************!*\
  !*** ../../packages/app/provider/NextTamaguiProvider.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NextTamaguiProvider: () => (/* binding */ NextTamaguiProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tamagui_core_reset_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tamagui/core/reset.css */ \"../../node_modules/@tamagui/core/reset.css\");\n/* harmony import */ var _tamagui_core_reset_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_tamagui_core_reset_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tamagui_font_inter_css_400_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tamagui/font-inter/css/400.css */ \"../../node_modules/@tamagui/font-inter/css/400.css\");\n/* harmony import */ var _tamagui_font_inter_css_400_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_tamagui_font_inter_css_400_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _tamagui_font_inter_css_700_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tamagui/font-inter/css/700.css */ \"../../node_modules/@tamagui/font-inter/css/700.css\");\n/* harmony import */ var _tamagui_font_inter_css_700_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_tamagui_font_inter_css_700_css__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _tamagui_polyfill_dev__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tamagui/polyfill-dev */ \"../../node_modules/@tamagui/polyfill-dev/index.js\");\n/* harmony import */ var _tamagui_polyfill_dev__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tamagui_polyfill_dev__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"../../node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _tamagui_next_theme__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tamagui/next-theme */ \"../../node_modules/@tamagui/next-theme/dist/cjs/index.js\");\n/* harmony import */ var _tamagui_next_theme__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_tamagui_next_theme__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @my/ui */ \"../../packages/config/src/tamagui.config.ts\");\n/* harmony import */ var app_provider__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! app/provider */ \"../../packages/app/provider/index.tsx\");\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_my_ui__WEBPACK_IMPORTED_MODULE_8__, app_provider__WEBPACK_IMPORTED_MODULE_9__]);\n([_my_ui__WEBPACK_IMPORTED_MODULE_8__, app_provider__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* __next_internal_client_entry_do_not_use__ NextTamaguiProvider auto */ \n\n\n\n\n\n\n\n\n\nconst NextTamaguiProvider = ({ children })=>{\n    const [theme, setTheme] = (0,_tamagui_next_theme__WEBPACK_IMPORTED_MODULE_6__.useRootTheme)();\n    (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useServerInsertedHTML)(()=>{\n        // @ts-ignore\n        const rnwStyle = react_native__WEBPACK_IMPORTED_MODULE_7__.StyleSheet.getSheet();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"stylesheet\",\n                    href: \"/tamagui.css\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/NextTamaguiProvider.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                    dangerouslySetInnerHTML: {\n                        __html: rnwStyle.textContent\n                    },\n                    id: rnwStyle.id\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/NextTamaguiProvider.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                    dangerouslySetInnerHTML: {\n                        // the first time this runs you'll get the full CSS including all themes\n                        // after that, it will only return CSS generated since the last call\n                        __html: _my_ui__WEBPACK_IMPORTED_MODULE_8__.config.getNewCSS()\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/NextTamaguiProvider.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                    dangerouslySetInnerHTML: {\n                        __html: _my_ui__WEBPACK_IMPORTED_MODULE_8__.config.getCSS({\n                            exclude:  false ? 0 : null\n                        })\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/NextTamaguiProvider.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    dangerouslySetInnerHTML: {\n                        // avoid flash of animated things on enter:\n                        __html: `document.documentElement.classList.add('t_unmounted')`\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/NextTamaguiProvider.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_next_theme__WEBPACK_IMPORTED_MODULE_6__.NextThemeProvider, {\n        skipNextHead: true,\n        defaultTheme: \"light\",\n        onChangeTheme: (next)=>{\n            setTheme(next);\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_provider__WEBPACK_IMPORTED_MODULE_9__.Provider, {\n            disableRootThemeClass: true,\n            defaultTheme: theme || \"light\",\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/NextTamaguiProvider.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/NextTamaguiProvider.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/provider/NextTamaguiProvider.tsx\n");

/***/ }),

/***/ "../../packages/app/provider/ToastViewport.web.tsx":
/*!*********************************************************!*\
  !*** ../../packages/app/provider/ToastViewport.web.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @my/ui */ \"../../node_modules/@tamagui/toast/dist/esm/ToastViewport.mjs\");\n\n\nconst ToastViewport = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_1__.ToastViewport, {\n        left: 0,\n        right: 0,\n        top: 10\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/ToastViewport.web.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXBwL3Byb3ZpZGVyL1RvYXN0Vmlld3BvcnQud2ViLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBeUQ7QUFFbEQsTUFBTUEsZ0JBQWdCO0lBQzNCLHFCQUNFLDhEQUFDQyxpREFBZUE7UUFDZEMsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLEtBQUs7Ozs7OztBQUdYLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9wYWNrYWdlcy9hcHAvcHJvdmlkZXIvVG9hc3RWaWV3cG9ydC53ZWIudHN4P2VjNWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVG9hc3RWaWV3cG9ydCBhcyBUb2FzdFZpZXdwb3J0T2cgfSBmcm9tICdAbXkvdWknXG5cbmV4cG9ydCBjb25zdCBUb2FzdFZpZXdwb3J0ID0gKCkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxUb2FzdFZpZXdwb3J0T2dcbiAgICAgIGxlZnQ9ezB9XG4gICAgICByaWdodD17MH1cbiAgICAgIHRvcD17MTB9XG4gICAgLz5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlRvYXN0Vmlld3BvcnQiLCJUb2FzdFZpZXdwb3J0T2ciLCJsZWZ0IiwicmlnaHQiLCJ0b3AiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/app/provider/ToastViewport.web.tsx\n");

/***/ }),

/***/ "../../packages/app/provider/index.tsx":
/*!*********************************************!*\
  !*** ../../packages/app/provider/index.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Provider: () => (/* binding */ Provider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @my/ui */ \"../../node_modules/tamagui/dist/esm/views/TamaguiProvider.mjs\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @my/ui */ \"../../packages/config/src/tamagui.config.ts\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @my/ui */ \"../../node_modules/@tamagui/toast/dist/esm/ToastProvider.mjs\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @my/ui */ \"../../node_modules/@tamagui/core/dist/cjs/index.cjs\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_my_ui__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/CustomToast.tsx\");\n/* harmony import */ var _ToastViewport__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ToastViewport */ \"../../packages/app/provider/ToastViewport.web.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_my_ui__WEBPACK_IMPORTED_MODULE_3__, _my_ui__WEBPACK_IMPORTED_MODULE_6__, _ToastViewport__WEBPACK_IMPORTED_MODULE_7__]);\n([_my_ui__WEBPACK_IMPORTED_MODULE_3__, _my_ui__WEBPACK_IMPORTED_MODULE_6__, _ToastViewport__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nfunction Provider({ children, defaultTheme = \"light\", ...rest }) {\n    const colorScheme = (0,react_native__WEBPACK_IMPORTED_MODULE_1__.useColorScheme)();\n    const theme = defaultTheme || (colorScheme === \"dark\" ? \"dark\" : \"light\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_2__.TamaguiProvider, {\n        config: _my_ui__WEBPACK_IMPORTED_MODULE_3__.config,\n        defaultTheme: theme,\n        ...rest,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_4__.ToastProvider, {\n            swipeDirection: \"horizontal\",\n            duration: 6000,\n            native: _my_ui__WEBPACK_IMPORTED_MODULE_5__.isWeb ? [] : [\n                \"mobile\"\n            ],\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.CustomToast, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/index.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ToastViewport__WEBPACK_IMPORTED_MODULE_7__.ToastViewport, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/index.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/index.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/app/provider/index.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/provider/index.tsx\n");

/***/ }),

/***/ "../../packages/ui/src/CustomToast.tsx":
/*!*********************************************!*\
  !*** ../../packages/ui/src/CustomToast.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomToast: () => (/* binding */ CustomToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var expo_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! expo-constants */ \"../../node_modules/expo-constants/build/Constants.js\");\n/* harmony import */ var expo_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! expo-constants */ \"../../node_modules/expo-constants/build/Constants.types.js\");\n/* harmony import */ var _NativeToast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./NativeToast */ \"../../packages/ui/src/NativeToast.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_NativeToast__WEBPACK_IMPORTED_MODULE_3__]);\n_NativeToast__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst isExpo = expo_constants__WEBPACK_IMPORTED_MODULE_1__[\"default\"].executionEnvironment === expo_constants__WEBPACK_IMPORTED_MODULE_2__.ExecutionEnvironment.StoreClient;\nconst CustomToast = ()=>{\n    if (isExpo) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NativeToast__WEBPACK_IMPORTED_MODULE_3__.NativeToast, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/ui/src/CustomToast.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvdWkvc3JjL0N1c3RvbVRvYXN0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQWdFO0FBQ1o7QUFFcEQsTUFBTUksU0FBU0osc0RBQVNBLENBQUNLLG9CQUFvQixLQUFLSixnRUFBb0JBLENBQUNLLFdBQVc7QUFFM0UsTUFBTUMsY0FBYztJQUN6QixJQUFJSCxRQUFRO1FBQ1YsT0FBTztJQUNUO0lBQ0EscUJBQU8sOERBQUNELHFEQUFLQTs7Ozs7QUFDZixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vcGFja2FnZXMvdWkvc3JjL0N1c3RvbVRvYXN0LnRzeD84YzBkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBDb25zdGFudHMsIHsgRXhlY3V0aW9uRW52aXJvbm1lbnQgfSBmcm9tICdleHBvLWNvbnN0YW50cydcbmltcG9ydCB7IE5hdGl2ZVRvYXN0IGFzIFRvYXN0IH0gZnJvbSAnLi9OYXRpdmVUb2FzdCdcblxuY29uc3QgaXNFeHBvID0gQ29uc3RhbnRzLmV4ZWN1dGlvbkVudmlyb25tZW50ID09PSBFeGVjdXRpb25FbnZpcm9ubWVudC5TdG9yZUNsaWVudFxuXG5leHBvcnQgY29uc3QgQ3VzdG9tVG9hc3QgPSAoKSA9PiB7XG4gIGlmIChpc0V4cG8pIHtcbiAgICByZXR1cm4gbnVsbFxuICB9XG4gIHJldHVybiA8VG9hc3QgLz5cbn1cbiJdLCJuYW1lcyI6WyJDb25zdGFudHMiLCJFeGVjdXRpb25FbnZpcm9ubWVudCIsIk5hdGl2ZVRvYXN0IiwiVG9hc3QiLCJpc0V4cG8iLCJleGVjdXRpb25FbnZpcm9ubWVudCIsIlN0b3JlQ2xpZW50IiwiQ3VzdG9tVG9hc3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/ui/src/CustomToast.tsx\n");

/***/ }),

/***/ "../../packages/ui/src/NativeToast.tsx":
/*!*********************************************!*\
  !*** ../../packages/ui/src/NativeToast.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NativeToast: () => (/* binding */ NativeToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tamagui_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tamagui/toast */ \"../../node_modules/@tamagui/toast/dist/esm/ToastImperative.mjs\");\n/* harmony import */ var _tamagui_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tamagui/toast */ \"../../node_modules/@tamagui/toast/dist/esm/Toast.mjs\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tamagui */ \"../../node_modules/@tamagui/stacks/dist/esm/Stacks.mjs\");\n\n\n\nconst NativeToast = ()=>{\n    const currentToast = (0,_tamagui_toast__WEBPACK_IMPORTED_MODULE_1__.useToastState)();\n    if (!currentToast || currentToast.isHandledNatively) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_toast__WEBPACK_IMPORTED_MODULE_2__.Toast, {\n        duration: currentToast.duration,\n        viewportName: currentToast.viewportName,\n        enterStyle: {\n            opacity: 0,\n            scale: 0.5,\n            y: -25\n        },\n        exitStyle: {\n            opacity: 0,\n            scale: 1,\n            y: -20\n        },\n        y: 0,\n        opacity: 1,\n        scale: 1,\n        animation: \"quick\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.YStack, {\n            \"data-at\": \"NativeToast.tsx:23\",\n            \"data-in\": \"NativeToast\",\n            \"data-is\": \"YStack\",\n            py: \"$1.5\",\n            px: \"$2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_toast__WEBPACK_IMPORTED_MODULE_2__.Toast.Title, {\n                    lineHeight: \"$1\",\n                    children: currentToast.title\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/ui/src/NativeToast.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, undefined),\n                !!currentToast.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_toast__WEBPACK_IMPORTED_MODULE_2__.Toast.Description, {\n                    children: currentToast.message\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/ui/src/NativeToast.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 36\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/ui/src/NativeToast.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, undefined)\n    }, currentToast.id, false, {\n        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/ui/src/NativeToast.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/ui/src/NativeToast.tsx\n");

/***/ }),

/***/ "../../packages/ui/src/SwitchRouterButton.tsx":
/*!****************************************************!*\
  !*** ../../packages/ui/src/SwitchRouterButton.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SwitchRouterButton: () => (/* binding */ SwitchRouterButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/views/Anchor.mjs\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tamagui */ \"../../node_modules/@tamagui/button/dist/esm/Button.mjs\");\n\n\nconst SwitchRouterButton = ({ pagesMode = false })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_1__.Anchor, {\n        \"data-at\": \"SwitchRouterButton.tsx:5\",\n        \"data-in\": \"SwitchRouterButton\",\n        \"data-is\": \"Anchor\",\n        text: \"center\",\n        color: \"$color12\",\n        href: pagesMode ? \"/\" : \"/pages-example\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n            \"data-at\": \"SwitchRouterButton.tsx:6\",\n            \"data-in\": \"SwitchRouterButton\",\n            \"data-is\": \"Button\",\n            children: [\n                \"Change router: \",\n                pagesMode ? \"pages\" : \"app\"\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/ui/src/SwitchRouterButton.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/ui/src/SwitchRouterButton.tsx\",\n        lineNumber: 7,\n        columnNumber: 10\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvdWkvc3JjL1N3aXRjaFJvdXRlckJ1dHRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF3QztBQUVqQyxNQUFNRSxxQkFBcUJBLENBQUMsRUFBRUMsWUFBWSxPQUFnQztJQUMvRSxxQkFDRSw4REFBQ0gsMkNBQU1BO1FBQUFJLFdBQUE7UUFBQUMsV0FBQTtRQUFBQyxXQUFBO1FBQUNDLE1BQUs7UUFBU0MsT0FBTTtRQUFXQyxNQUFNTixZQUFZLE1BQU07a0JBQzdELDRFQUFDRiwyQ0FBTUE7WUFBQUcsV0FBQTtZQUFBQyxXQUFBO1lBQUFDLFdBQUE7O2dCQUFDO2dCQUFnQkgsWUFBWSxVQUFVOzs7Ozs7Ozs7Ozs7QUFHcEQsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL3BhY2thZ2VzL3VpL3NyYy9Td2l0Y2hSb3V0ZXJCdXR0b24udHN4P2JjNWQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQW5jaG9yLCBCdXR0b24gfSBmcm9tICd0YW1hZ3VpJ1xuXG5leHBvcnQgY29uc3QgU3dpdGNoUm91dGVyQnV0dG9uID0gKHsgcGFnZXNNb2RlID0gZmFsc2UgfTogeyBwYWdlc01vZGU/OiBib29sZWFuIH0pID0+IHtcbiAgcmV0dXJuIChcbiAgICA8QW5jaG9yIHRleHQ9XCJjZW50ZXJcIiBjb2xvcj1cIiRjb2xvcjEyXCIgaHJlZj17cGFnZXNNb2RlID8gJy8nIDogJy9wYWdlcy1leGFtcGxlJ30+XG4gICAgICA8QnV0dG9uPkNoYW5nZSByb3V0ZXI6IHtwYWdlc01vZGUgPyAncGFnZXMnIDogJ2FwcCd9PC9CdXR0b24+XG4gICAgPC9BbmNob3I+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJBbmNob3IiLCJCdXR0b24iLCJTd2l0Y2hSb3V0ZXJCdXR0b24iLCJwYWdlc01vZGUiLCJkYXRhLWF0IiwiZGF0YS1pbiIsImRhdGEtaXMiLCJ0ZXh0IiwiY29sb3IiLCJocmVmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/ui/src/SwitchRouterButton.tsx\n");

/***/ }),

/***/ "../../packages/ui/src/SwitchThemeButton.tsx":
/*!***************************************************!*\
  !*** ../../packages/ui/src/SwitchThemeButton.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SwitchThemeButton: () => (/* binding */ SwitchThemeButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tamagui */ \"../../node_modules/@tamagui/core/dist/cjs/index.cjs\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(tamagui__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tamagui */ \"../../node_modules/@tamagui/button/dist/esm/Button.mjs\");\n/* harmony import */ var _tamagui_next_theme__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tamagui/next-theme */ \"../../node_modules/@tamagui/next-theme/dist/cjs/index.js\");\n/* harmony import */ var _tamagui_next_theme__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_tamagui_next_theme__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nconst SwitchThemeButton = ()=>{\n    const themeSetting = (0,_tamagui_next_theme__WEBPACK_IMPORTED_MODULE_2__.useThemeSetting)();\n    const [theme] = (0,_tamagui_next_theme__WEBPACK_IMPORTED_MODULE_2__.useRootTheme)();\n    const [clientTheme, setClientTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    (0,tamagui__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(()=>{\n        setClientTheme(themeSetting.forcedTheme || themeSetting.current || theme);\n    }, [\n        themeSetting.current,\n        themeSetting.resolvedTheme\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n        \"data-at\": \"SwitchThemeButton.tsx:15\",\n        \"data-in\": \"SwitchThemeButton\",\n        \"data-is\": \"Button\",\n        onPress: themeSetting.toggle,\n        children: [\n            \"Change theme: \",\n            clientTheme\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/ui/src/SwitchThemeButton.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvdWkvc3JjL1N3aXRjaFRoZW1lQnV0dG9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBZ0M7QUFDMkI7QUFDUTtBQUU1RCxNQUFNSyxvQkFBb0JBO0lBQy9CLE1BQU1DLGVBQWVILG9FQUFlQTtJQUNwQyxNQUFNLENBQUNJLE1BQU0sR0FBR0gsaUVBQVlBO0lBRTVCLE1BQU0sQ0FBQ0ksYUFBYUMsZUFBZSxHQUFHVCwrQ0FBUUEsQ0FBcUI7SUFFbkVFLGtFQUF5QkEsQ0FBQztRQUN4Qk8sZUFBZUgsYUFBYUksV0FBVyxJQUFJSixhQUFhSyxPQUFPLElBQUlKO0lBQ3JFLEdBQUc7UUFBQ0QsYUFBYUssT0FBTztRQUFFTCxhQUFhTSxhQUFhO0tBQUM7SUFFckQscUJBQU8sOERBQUNYLDJDQUFNQTtRQUFBWSxXQUFBO1FBQUFDLFdBQUE7UUFBQUMsV0FBQTtRQUFDQyxTQUFTVixhQUFhVyxNQUFNOztZQUFFO1lBQWVUOzs7Ozs7O0FBQzlELEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9wYWNrYWdlcy91aS9zcmMvU3dpdGNoVGhlbWVCdXR0b24udHN4P2EyYmEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IEJ1dHRvbiwgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCB9IGZyb20gJ3RhbWFndWknXG5pbXBvcnQgeyB1c2VUaGVtZVNldHRpbmcsIHVzZVJvb3RUaGVtZSB9IGZyb20gJ0B0YW1hZ3VpL25leHQtdGhlbWUnXG5cbmV4cG9ydCBjb25zdCBTd2l0Y2hUaGVtZUJ1dHRvbiA9ICgpID0+IHtcbiAgY29uc3QgdGhlbWVTZXR0aW5nID0gdXNlVGhlbWVTZXR0aW5nKClcbiAgY29uc3QgW3RoZW1lXSA9IHVzZVJvb3RUaGVtZSgpXG5cbiAgY29uc3QgW2NsaWVudFRoZW1lLCBzZXRDbGllbnRUaGVtZV0gPSB1c2VTdGF0ZTxzdHJpbmcgfCB1bmRlZmluZWQ+KCdsaWdodCcpXG5cbiAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgc2V0Q2xpZW50VGhlbWUodGhlbWVTZXR0aW5nLmZvcmNlZFRoZW1lIHx8IHRoZW1lU2V0dGluZy5jdXJyZW50IHx8IHRoZW1lKVxuICB9LCBbdGhlbWVTZXR0aW5nLmN1cnJlbnQsIHRoZW1lU2V0dGluZy5yZXNvbHZlZFRoZW1lXSlcblxuICByZXR1cm4gPEJ1dHRvbiBvblByZXNzPXt0aGVtZVNldHRpbmcudG9nZ2xlfT5DaGFuZ2UgdGhlbWU6IHtjbGllbnRUaGVtZX08L0J1dHRvbj5cbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsIkJ1dHRvbiIsInVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QiLCJ1c2VUaGVtZVNldHRpbmciLCJ1c2VSb290VGhlbWUiLCJTd2l0Y2hUaGVtZUJ1dHRvbiIsInRoZW1lU2V0dGluZyIsInRoZW1lIiwiY2xpZW50VGhlbWUiLCJzZXRDbGllbnRUaGVtZSIsImZvcmNlZFRoZW1lIiwiY3VycmVudCIsInJlc29sdmVkVGhlbWUiLCJkYXRhLWF0IiwiZGF0YS1pbiIsImRhdGEtaXMiLCJvblByZXNzIiwidG9nZ2xlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/ui/src/SwitchThemeButton.tsx\n");

/***/ }),

/***/ "../../packages/config/src/animations.ts":
/*!***********************************************!*\
  !*** ../../packages/config/src/animations.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animations: () => (/* binding */ animations)\n/* harmony export */ });\n/* harmony import */ var _tamagui_animations_react_native__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tamagui/animations-react-native */ \"../../node_modules/@tamagui/animations-react-native/dist/esm/createAnimations.mjs\");\n\nconst animations = (0,_tamagui_animations_react_native__WEBPACK_IMPORTED_MODULE_0__.createAnimations)({\n    \"100ms\": {\n        type: \"timing\",\n        duration: 100\n    },\n    bouncy: {\n        damping: 9,\n        mass: 0.9,\n        stiffness: 150\n    },\n    lazy: {\n        damping: 18,\n        stiffness: 50\n    },\n    medium: {\n        damping: 15,\n        stiffness: 120,\n        mass: 1\n    },\n    slow: {\n        damping: 15,\n        stiffness: 40\n    },\n    quick: {\n        damping: 20,\n        mass: 1.2,\n        stiffness: 250\n    },\n    tooltip: {\n        damping: 10,\n        mass: 0.9,\n        stiffness: 100\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvY29uZmlnL3NyYy9hbmltYXRpb25zLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW1FO0FBRTVELE1BQU1DLGFBQWFELGtGQUFnQkEsQ0FBQztJQUN6QyxTQUFTO1FBQ1BFLE1BQU07UUFDTkMsVUFBVTtJQUNaO0lBQ0FDLFFBQVE7UUFDTkMsU0FBUztRQUNUQyxNQUFNO1FBQ05DLFdBQVc7SUFDYjtJQUNBQyxNQUFNO1FBQ0pILFNBQVM7UUFDVEUsV0FBVztJQUNiO0lBQ0FFLFFBQVE7UUFDTkosU0FBUztRQUNURSxXQUFXO1FBQ1hELE1BQU07SUFDUjtJQUNBSSxNQUFNO1FBQ0pMLFNBQVM7UUFDVEUsV0FBVztJQUNiO0lBQ0FJLE9BQU87UUFDTE4sU0FBUztRQUNUQyxNQUFNO1FBQ05DLFdBQVc7SUFDYjtJQUNBSyxTQUFTO1FBQ1BQLFNBQVM7UUFDVEMsTUFBTTtRQUNOQyxXQUFXO0lBQ2I7QUFDRixHQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vcGFja2FnZXMvY29uZmlnL3NyYy9hbmltYXRpb25zLnRzPzRlZGUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQW5pbWF0aW9ucyB9IGZyb20gJ0B0YW1hZ3VpL2FuaW1hdGlvbnMtcmVhY3QtbmF0aXZlJ1xuXG5leHBvcnQgY29uc3QgYW5pbWF0aW9ucyA9IGNyZWF0ZUFuaW1hdGlvbnMoe1xuICAnMTAwbXMnOiB7XG4gICAgdHlwZTogJ3RpbWluZycsXG4gICAgZHVyYXRpb246IDEwMCxcbiAgfSxcbiAgYm91bmN5OiB7XG4gICAgZGFtcGluZzogOSxcbiAgICBtYXNzOiAwLjksXG4gICAgc3RpZmZuZXNzOiAxNTAsXG4gIH0sXG4gIGxhenk6IHtcbiAgICBkYW1waW5nOiAxOCxcbiAgICBzdGlmZm5lc3M6IDUwLFxuICB9LFxuICBtZWRpdW06IHtcbiAgICBkYW1waW5nOiAxNSxcbiAgICBzdGlmZm5lc3M6IDEyMCxcbiAgICBtYXNzOiAxLFxuICB9LFxuICBzbG93OiB7XG4gICAgZGFtcGluZzogMTUsXG4gICAgc3RpZmZuZXNzOiA0MCxcbiAgfSxcbiAgcXVpY2s6IHtcbiAgICBkYW1waW5nOiAyMCxcbiAgICBtYXNzOiAxLjIsXG4gICAgc3RpZmZuZXNzOiAyNTAsXG4gIH0sXG4gIHRvb2x0aXA6IHtcbiAgICBkYW1waW5nOiAxMCxcbiAgICBtYXNzOiAwLjksXG4gICAgc3RpZmZuZXNzOiAxMDAsXG4gIH0sXG59KVxuIl0sIm5hbWVzIjpbImNyZWF0ZUFuaW1hdGlvbnMiLCJhbmltYXRpb25zIiwidHlwZSIsImR1cmF0aW9uIiwiYm91bmN5IiwiZGFtcGluZyIsIm1hc3MiLCJzdGlmZm5lc3MiLCJsYXp5IiwibWVkaXVtIiwic2xvdyIsInF1aWNrIiwidG9vbHRpcCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/config/src/animations.ts\n");

/***/ }),

/***/ "../../packages/config/src/fonts.ts":
/*!******************************************!*\
  !*** ../../packages/config/src/fonts.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bodyFont: () => (/* binding */ bodyFont),\n/* harmony export */   headingFont: () => (/* binding */ headingFont)\n/* harmony export */ });\n/* harmony import */ var _tamagui_font_inter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tamagui/font-inter */ \"../../node_modules/@tamagui/font-inter/dist/esm/index.mjs\");\n\nconst headingFont = (0,_tamagui_font_inter__WEBPACK_IMPORTED_MODULE_0__.createInterFont)({\n    size: {\n        6: 15\n    },\n    transform: {\n        6: \"uppercase\",\n        7: \"none\"\n    },\n    weight: {\n        6: \"400\",\n        7: \"700\"\n    },\n    color: {\n        6: \"$colorFocus\",\n        7: \"$color\"\n    },\n    letterSpacing: {\n        5: 2,\n        6: 1,\n        7: 0,\n        8: -1,\n        9: -2,\n        10: -3,\n        12: -4,\n        14: -5,\n        15: -6\n    },\n    face: {\n        700: {\n            normal: \"InterBold\"\n        }\n    }\n});\nconst bodyFont = (0,_tamagui_font_inter__WEBPACK_IMPORTED_MODULE_0__.createInterFont)({\n    face: {\n        700: {\n            normal: \"InterBold\"\n        }\n    }\n}, {\n    sizeSize: (size)=>Math.round(size * 1.1),\n    sizeLineHeight: (size)=>Math.round(size * 1.1 + (size > 20 ? 10 : 10))\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/config/src/fonts.ts\n");

/***/ }),

/***/ "../../packages/config/src/tamagui.config.ts":
/*!***************************************************!*\
  !*** ../../packages/config/src/tamagui.config.ts ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config)\n/* harmony export */ });\n/* harmony import */ var _tamagui_config_v4__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tamagui/config/v4 */ \"../../node_modules/@tamagui/config/dist/esm/v4.mjs\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/createTamagui.mjs\");\n/* harmony import */ var _fonts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./fonts */ \"../../packages/config/src/fonts.ts\");\n/* harmony import */ var _animations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./animations */ \"../../packages/config/src/animations.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_tamagui_config_v4__WEBPACK_IMPORTED_MODULE_1__]);\n_tamagui_config_v4__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst config = (0,tamagui__WEBPACK_IMPORTED_MODULE_0__.createTamagui)({\n    ..._tamagui_config_v4__WEBPACK_IMPORTED_MODULE_1__.defaultConfig,\n    animations: _animations__WEBPACK_IMPORTED_MODULE_2__.animations,\n    fonts: {\n        body: _fonts__WEBPACK_IMPORTED_MODULE_3__.bodyFont,\n        heading: _fonts__WEBPACK_IMPORTED_MODULE_3__.headingFont\n    }\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvY29uZmlnL3NyYy90YW1hZ3VpLmNvbmZpZy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFrRDtBQUNYO0FBQ1E7QUFDTjtBQUVsQyxNQUFNSyxTQUFTSixzREFBYUEsQ0FBQztJQUNsQyxHQUFHRCw2REFBYTtJQUNoQkksVUFBVUEscURBQUFBO0lBQ1ZFLE9BQU87UUFDTEMsTUFBTUwsNENBQVFBO1FBQ2RNLFNBQVNMLCtDQUFXQTtJQUN0QjtBQUNGLEdBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9wYWNrYWdlcy9jb25maWcvc3JjL3RhbWFndWkuY29uZmlnLnRzPzJkNjAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGVmYXVsdENvbmZpZyB9IGZyb20gJ0B0YW1hZ3VpL2NvbmZpZy92NCdcbmltcG9ydCB7IGNyZWF0ZVRhbWFndWkgfSBmcm9tICd0YW1hZ3VpJ1xuaW1wb3J0IHsgYm9keUZvbnQsIGhlYWRpbmdGb250IH0gZnJvbSAnLi9mb250cydcbmltcG9ydCB7IGFuaW1hdGlvbnMgfSBmcm9tICcuL2FuaW1hdGlvbnMnXG5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBjcmVhdGVUYW1hZ3VpKHtcbiAgLi4uZGVmYXVsdENvbmZpZyxcbiAgYW5pbWF0aW9ucyxcbiAgZm9udHM6IHtcbiAgICBib2R5OiBib2R5Rm9udCxcbiAgICBoZWFkaW5nOiBoZWFkaW5nRm9udCxcbiAgfSxcbn0pXG4iXSwibmFtZXMiOlsiZGVmYXVsdENvbmZpZyIsImNyZWF0ZVRhbWFndWkiLCJib2R5Rm9udCIsImhlYWRpbmdGb250IiwiYW5pbWF0aW9ucyIsImNvbmZpZyIsImZvbnRzIiwiYm9keSIsImhlYWRpbmciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/config/src/tamagui.config.ts\n");

/***/ }),

/***/ "@react-native/assets-registry/registry":
/*!*********************************************************!*\
  !*** external "@react-native/assets-registry/registry" ***!
  \*********************************************************/
/***/ ((module) => {

module.exports = require("@react-native/assets-registry/registry");

/***/ }),

/***/ "@react-native/normalize-color":
/*!************************************************!*\
  !*** external "@react-native/normalize-color" ***!
  \************************************************/
/***/ ((module) => {

module.exports = require("@react-native/normalize-color");

/***/ }),

/***/ "fbjs/lib/invariant":
/*!*************************************!*\
  !*** external "fbjs/lib/invariant" ***!
  \*************************************/
/***/ ((module) => {

module.exports = require("fbjs/lib/invariant");

/***/ }),

/***/ "fbjs/lib/warning":
/*!***********************************!*\
  !*** external "fbjs/lib/warning" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("fbjs/lib/warning");

/***/ }),

/***/ "inline-style-prefixer/lib/createPrefixer":
/*!***********************************************************!*\
  !*** external "inline-style-prefixer/lib/createPrefixer" ***!
  \***********************************************************/
/***/ ((module) => {

module.exports = require("inline-style-prefixer/lib/createPrefixer");

/***/ }),

/***/ "inline-style-prefixer/lib/plugins/backgroundClip":
/*!*******************************************************************!*\
  !*** external "inline-style-prefixer/lib/plugins/backgroundClip" ***!
  \*******************************************************************/
/***/ ((module) => {

module.exports = require("inline-style-prefixer/lib/plugins/backgroundClip");

/***/ }),

/***/ "inline-style-prefixer/lib/plugins/crossFade":
/*!**************************************************************!*\
  !*** external "inline-style-prefixer/lib/plugins/crossFade" ***!
  \**************************************************************/
/***/ ((module) => {

module.exports = require("inline-style-prefixer/lib/plugins/crossFade");

/***/ }),

/***/ "inline-style-prefixer/lib/plugins/cursor":
/*!***********************************************************!*\
  !*** external "inline-style-prefixer/lib/plugins/cursor" ***!
  \***********************************************************/
/***/ ((module) => {

module.exports = require("inline-style-prefixer/lib/plugins/cursor");

/***/ }),

/***/ "inline-style-prefixer/lib/plugins/filter":
/*!***********************************************************!*\
  !*** external "inline-style-prefixer/lib/plugins/filter" ***!
  \***********************************************************/
/***/ ((module) => {

module.exports = require("inline-style-prefixer/lib/plugins/filter");

/***/ }),

/***/ "inline-style-prefixer/lib/plugins/imageSet":
/*!*************************************************************!*\
  !*** external "inline-style-prefixer/lib/plugins/imageSet" ***!
  \*************************************************************/
/***/ ((module) => {

module.exports = require("inline-style-prefixer/lib/plugins/imageSet");

/***/ }),

/***/ "inline-style-prefixer/lib/plugins/logical":
/*!************************************************************!*\
  !*** external "inline-style-prefixer/lib/plugins/logical" ***!
  \************************************************************/
/***/ ((module) => {

module.exports = require("inline-style-prefixer/lib/plugins/logical");

/***/ }),

/***/ "inline-style-prefixer/lib/plugins/position":
/*!*************************************************************!*\
  !*** external "inline-style-prefixer/lib/plugins/position" ***!
  \*************************************************************/
/***/ ((module) => {

module.exports = require("inline-style-prefixer/lib/plugins/position");

/***/ }),

/***/ "inline-style-prefixer/lib/plugins/sizing":
/*!***********************************************************!*\
  !*** external "inline-style-prefixer/lib/plugins/sizing" ***!
  \***********************************************************/
/***/ ((module) => {

module.exports = require("inline-style-prefixer/lib/plugins/sizing");

/***/ }),

/***/ "inline-style-prefixer/lib/plugins/transition":
/*!***************************************************************!*\
  !*** external "inline-style-prefixer/lib/plugins/transition" ***!
  \***************************************************************/
/***/ ((module) => {

module.exports = require("inline-style-prefixer/lib/plugins/transition");

/***/ }),

/***/ "memoize-one":
/*!******************************!*\
  !*** external "memoize-one" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("memoize-one");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ "nullthrows":
/*!*****************************!*\
  !*** external "nullthrows" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("nullthrows");

/***/ }),

/***/ "postcss-value-parser":
/*!***************************************!*\
  !*** external "postcss-value-parser" ***!
  \***************************************/
/***/ ((module) => {

module.exports = require("postcss-value-parser");

/***/ }),

/***/ "raf/polyfill":
/*!*******************************!*\
  !*** external "raf/polyfill" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("raf/polyfill");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("react-dom");

/***/ }),

/***/ "react-dom/client":
/*!***********************************!*\
  !*** external "react-dom/client" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("react-dom/client");

/***/ }),

/***/ "react-remove-scroll":
/*!**************************************!*\
  !*** external "react-remove-scroll" ***!
  \**************************************/
/***/ ((module) => {

module.exports = require("react-remove-scroll");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("styled-jsx/style");

/***/ }),

/***/ "styleq":
/*!*************************!*\
  !*** external "styleq" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("styleq");

/***/ }),

/***/ "styleq/transform-localize-style":
/*!**************************************************!*\
  !*** external "styleq/transform-localize-style" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = require("styleq/transform-localize-style");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "color2k":
/*!**************************!*\
  !*** external "color2k" ***!
  \**************************/
/***/ ((module) => {

module.exports = import("color2k");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@tamagui","vendor-chunks/react-native-web","vendor-chunks/next","vendor-chunks/tamagui","vendor-chunks/@babel","vendor-chunks/expo-constants","vendor-chunks/expo-modules-core","vendor-chunks/react-native-svg","vendor-chunks/solito"], () => (__webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fpages-example&preferredRegion=&absolutePagePath=.%2Fpages%2Fpages-example%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();