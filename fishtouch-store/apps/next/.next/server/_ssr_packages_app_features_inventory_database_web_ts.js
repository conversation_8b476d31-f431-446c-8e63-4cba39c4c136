"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_packages_app_features_inventory_database_web_ts";
exports.ids = ["_ssr_packages_app_features_inventory_database_web_ts"];
exports.modules = {

/***/ "(ssr)/../../packages/app/features/inventory/database.web.ts":
/*!*************************************************************!*\
  !*** ../../packages/app/features/inventory/database.web.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearDatabase: () => (/* binding */ clearDatabase),\n/* harmony export */   closeDatabase: () => (/* binding */ closeDatabase),\n/* harmony export */   executeQuery: () => (/* binding */ executeQuery),\n/* harmony export */   executeQueryFirst: () => (/* binding */ executeQueryFirst),\n/* harmony export */   executeTransaction: () => (/* binding */ executeTransaction),\n/* harmony export */   executeUpdate: () => (/* binding */ executeUpdate),\n/* harmony export */   getDatabase: () => (/* binding */ getDatabase),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase),\n/* harmony export */   seedDatabase: () => (/* binding */ seedDatabase)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(ssr)/../../packages/app/features/inventory/types.ts\");\n// Web-compatible database implementation using localStorage for demo purposes\n// In a real app, you'd use IndexedDB or connect to a backend API\n\n// Simple in-memory database for web demo\nclass WebDatabase {\n    async execAsync(sql) {\n        // Handle CREATE TABLE statements\n        if (sql.includes(\"CREATE TABLE\")) {\n            const tableMatch = sql.match(/CREATE TABLE (?:IF NOT EXISTS )?(\\w+)/);\n            if (tableMatch) {\n                const tableName = tableMatch[1];\n                if (!this.data[tableName]) {\n                    this.data[tableName] = [];\n                    this.nextId[tableName] = 1;\n                }\n            }\n        }\n    // Handle other SQL statements as needed\n    }\n    async getAllAsync(sql, params = []) {\n        // Simple query parsing for demo\n        if (sql.includes(\"SELECT * FROM products\")) {\n            return this.data.products || [];\n        }\n        if (sql.includes(\"SELECT * FROM inventory\")) {\n            return this.data.inventory || [];\n        }\n        if (sql.includes(\"SELECT * FROM transactions\")) {\n            return this.data.transactions || [];\n        }\n        if (sql.includes(\"SELECT * FROM stock_alerts\")) {\n            return this.data.stock_alerts || [];\n        }\n        if (sql.includes(\"SELECT * FROM packages\")) {\n            return this.data.packages || [];\n        }\n        if (sql.includes(\"SELECT * FROM batches\")) {\n            return this.data.batches || [];\n        }\n        if (sql.includes(\"SELECT * FROM shipments\")) {\n            return this.data.shipments || [];\n        }\n        // Handle COUNT queries\n        if (sql.includes(\"COUNT(*)\")) {\n            if (sql.includes(\"FROM products\")) {\n                return [\n                    {\n                        count: (this.data.products || []).length\n                    }\n                ];\n            }\n        }\n        return [];\n    }\n    async getFirstAsync(sql, params = []) {\n        const results = await this.getAllAsync(sql, params);\n        return results.length > 0 ? results[0] : null;\n    }\n    async runAsync(sql, params = []) {\n        // Handle INSERT statements\n        if (sql.includes(\"INSERT INTO\")) {\n            const tableMatch = sql.match(/INSERT INTO (\\w+)/);\n            if (tableMatch) {\n                const tableName = tableMatch[1];\n                if (!this.data[tableName]) {\n                    this.data[tableName] = [];\n                    this.nextId[tableName] = 1;\n                }\n                const id = this.nextId[tableName]++;\n                const newRecord = {\n                    id,\n                    ...this.parseInsertValues(sql, params)\n                };\n                this.data[tableName].push(newRecord);\n                return {\n                    lastInsertRowId: id,\n                    changes: 1\n                };\n            }\n        }\n        // Handle UPDATE statements\n        if (sql.includes(\"UPDATE\")) {\n            return {\n                changes: 1\n            };\n        }\n        // Handle DELETE statements\n        if (sql.includes(\"DELETE\")) {\n            return {\n                changes: 1\n            };\n        }\n        return {\n            changes: 0\n        };\n    }\n    async closeAsync() {\n    // Nothing to close for in-memory database\n    }\n    parseInsertValues(sql, params) {\n        // Simple parsing for demo - in real implementation you'd parse SQL properly\n        const record = {};\n        // For demo purposes, create some sample data based on table\n        if (sql.includes(\"products\")) {\n            record.name = params[0] || \"Sample Product\";\n            record.sku = params[1] || \"SKU001\";\n            record.description = params[2] || \"Sample description\";\n            record.unit = params[3] || \"piece\";\n            record.min_stock_level = params[4] || 10;\n            record.created_at = new Date().toISOString();\n        }\n        return record;\n    }\n    constructor(){\n        this.data = {};\n        this.nextId = {};\n    }\n}\nlet db = null;\nconst initializeDatabase = async ()=>{\n    if (db) {\n        return db;\n    }\n    try {\n        db = new WebDatabase();\n        // Create tables\n        await db.execAsync(_types__WEBPACK_IMPORTED_MODULE_0__.CREATE_TABLES_SQL);\n        console.log(\"Web database initialized successfully\");\n        return db;\n    } catch (error) {\n        console.error(\"Failed to initialize web database:\", error);\n        throw error;\n    }\n};\nconst getDatabase = async ()=>{\n    if (!db) {\n        return await initializeDatabase();\n    }\n    return db;\n};\nconst closeDatabase = async ()=>{\n    if (db) {\n        await db.closeAsync();\n        db = null;\n    }\n};\n// Utility function to execute queries with error handling\nconst executeQuery = async (query, params = [])=>{\n    try {\n        const database = await getDatabase();\n        const result = await database.getAllAsync(query, params);\n        return result;\n    } catch (error) {\n        console.error(\"Query execution failed:\", error);\n        console.error(\"Query:\", query);\n        console.error(\"Params:\", params);\n        throw error;\n    }\n};\n// Utility function to execute single row queries\nconst executeQueryFirst = async (query, params = [])=>{\n    try {\n        const database = await getDatabase();\n        const result = await database.getFirstAsync(query, params);\n        return result;\n    } catch (error) {\n        console.error(\"Query execution failed:\", error);\n        console.error(\"Query:\", query);\n        console.error(\"Params:\", params);\n        throw error;\n    }\n};\n// Utility function to execute insert/update/delete queries\nconst executeUpdate = async (query, params = [])=>{\n    try {\n        const database = await getDatabase();\n        const result = await database.runAsync(query, params);\n        return result;\n    } catch (error) {\n        console.error(\"Update execution failed:\", error);\n        console.error(\"Query:\", query);\n        console.error(\"Params:\", params);\n        throw error;\n    }\n};\n// Transaction wrapper for multiple operations\nconst executeTransaction = async (operations)=>{\n    const database = await getDatabase();\n    try {\n        const result = await operations(database);\n        return result;\n    } catch (error) {\n        console.error(\"Transaction failed:\", error);\n        throw error;\n    }\n};\n// Database seeding function for development/testing\nconst seedDatabase = async ()=>{\n    try {\n        console.log(\"Seeding web database with sample data...\");\n        // Create sample data directly in the web database\n        const database = await getDatabase();\n        // Sample products\n        const sampleProducts = [\n            {\n                id: 1,\n                name: \"Fish Food Premium\",\n                sku: \"FF001\",\n                unit: \"kg\",\n                min_stock_level: 10,\n                created_at: new Date().toISOString()\n            },\n            {\n                id: 2,\n                name: \"Aquarium Filter\",\n                sku: \"AF001\",\n                unit: \"piece\",\n                min_stock_level: 5,\n                created_at: new Date().toISOString()\n            },\n            {\n                id: 3,\n                name: \"Water Conditioner\",\n                sku: \"WC001\",\n                unit: \"liter\",\n                min_stock_level: 20,\n                created_at: new Date().toISOString()\n            },\n            {\n                id: 4,\n                name: \"LED Light Strip\",\n                sku: \"LS001\",\n                unit: \"piece\",\n                min_stock_level: 3,\n                created_at: new Date().toISOString()\n            },\n            {\n                id: 5,\n                name: \"Gravel Substrate\",\n                sku: \"GS001\",\n                unit: \"kg\",\n                min_stock_level: 50,\n                created_at: new Date().toISOString()\n            }\n        ];\n        // Directly populate the data for web demo\n        database.data.products = sampleProducts;\n        database.nextId.products = 6;\n        console.log(\"Web database seeded successfully\");\n    } catch (error) {\n        console.error(\"Failed to seed web database:\", error);\n        throw error;\n    }\n};\n// Clear all data (for testing purposes)\nconst clearDatabase = async ()=>{\n    try {\n        const database = await getDatabase();\n        database.data = {};\n        database.nextId = {};\n        console.log(\"Web database cleared successfully\");\n    } catch (error) {\n        console.error(\"Failed to clear web database:\", error);\n        throw error;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/app/features/inventory/database.web.ts\n");

/***/ })

};
;