"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/expo-modules-core";
exports.ids = ["vendor-chunks/expo-modules-core"];
exports.modules = {

/***/ "(ssr)/../../node_modules/expo-modules-core/src/errors/CodedError.ts":
/*!*********************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/errors/CodedError.ts ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CodedError: () => (/* binding */ CodedError)\n/* harmony export */ });\n/**\n * A general error class that should be used for all errors in Expo modules.\n * Guarantees a `code` field that can be used to differentiate between different\n * types of errors without further subclassing Error.\n */ class CodedError extends Error {\n    constructor(code, message){\n        super(message);\n        this.code = code;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tbW9kdWxlcy1jb3JlL3NyYy9lcnJvcnMvQ29kZWRFcnJvci50cyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7Ozs7Q0FJQyxHQUNNLE1BQU1BLG1CQUFtQkM7SUFJOUJDLFlBQVlDLElBQVksRUFBRUMsT0FBZSxDQUFFO1FBQ3pDLEtBQUssQ0FBQ0E7UUFDTixJQUFJLENBQUNELElBQUksR0FBR0E7SUFDZDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vbm9kZV9tb2R1bGVzL2V4cG8tbW9kdWxlcy1jb3JlL3NyYy9lcnJvcnMvQ29kZWRFcnJvci50cz8xYWFkIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQSBnZW5lcmFsIGVycm9yIGNsYXNzIHRoYXQgc2hvdWxkIGJlIHVzZWQgZm9yIGFsbCBlcnJvcnMgaW4gRXhwbyBtb2R1bGVzLlxuICogR3VhcmFudGVlcyBhIGBjb2RlYCBmaWVsZCB0aGF0IGNhbiBiZSB1c2VkIHRvIGRpZmZlcmVudGlhdGUgYmV0d2VlbiBkaWZmZXJlbnRcbiAqIHR5cGVzIG9mIGVycm9ycyB3aXRob3V0IGZ1cnRoZXIgc3ViY2xhc3NpbmcgRXJyb3IuXG4gKi9cbmV4cG9ydCBjbGFzcyBDb2RlZEVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICBjb2RlOiBzdHJpbmc7XG4gIGluZm8/OiBhbnk7XG5cbiAgY29uc3RydWN0b3IoY29kZTogc3RyaW5nLCBtZXNzYWdlOiBzdHJpbmcpIHtcbiAgICBzdXBlcihtZXNzYWdlKTtcbiAgICB0aGlzLmNvZGUgPSBjb2RlO1xuICB9XG59XG4iXSwibmFtZXMiOlsiQ29kZWRFcnJvciIsIkVycm9yIiwiY29uc3RydWN0b3IiLCJjb2RlIiwibWVzc2FnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/expo-modules-core/src/errors/CodedError.ts\n");

/***/ }),

/***/ "(ssr)/../../node_modules/expo-modules-core/src/requireNativeModule.web.ts":
/*!***************************************************************************!*\
  !*** ../../node_modules/expo-modules-core/src/requireNativeModule.web.ts ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   requireNativeModule: () => (/* binding */ requireNativeModule),\n/* harmony export */   requireOptionalNativeModule: () => (/* binding */ requireOptionalNativeModule)\n/* harmony export */ });\nfunction requireNativeModule(moduleName) {\n    const nativeModule = requireOptionalNativeModule(moduleName);\n    if (nativeModule != null) {\n        return nativeModule;\n    }\n    if (true) {\n        // For SSR, we expect not to have native modules available, but to avoid crashing from SSR resolutions, we return an empty object.\n        return {};\n    }\n    throw new Error(`Cannot find native module '${moduleName}'`);\n}\nfunction requireOptionalNativeModule(moduleName) {\n    if (typeof globalThis.ExpoDomWebView === \"object\" && globalThis?.expo?.modules != null) {\n        return globalThis.expo?.modules?.[moduleName] ?? null;\n    }\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/expo-modules-core/src/requireNativeModule.web.ts\n");

/***/ })

};
;