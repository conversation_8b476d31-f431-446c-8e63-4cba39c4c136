"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/solito";
exports.ids = ["vendor-chunks/solito"];
exports.modules = {

/***/ "../../node_modules/solito/build/app/navigation/use-link.js":
/*!******************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/use-link.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLink: () => (/* binding */ useLink)\n/* harmony export */ });\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n/* harmony import */ var _use_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-router */ \"../../node_modules/solito/build/app/navigation/use-router.js\");\n\n\nfunction useLink({ href, replace, experimental }) {\n    const router = (0,_use_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    // https://github.com/react-navigation/react-navigation/blob/main/packages/native/src/useLinkProps.tsx#L64\n    const onPress = (e)=>{\n        let shouldHandle = false;\n        if (react_native__WEBPACK_IMPORTED_MODULE_1__.Platform.OS !== \"web\" || !e) {\n            shouldHandle = e ? !e.defaultPrevented : true;\n        } else if (!e.defaultPrevented && // onPress prevented default\n        // @ts-expect-error: these properties exist on web, but not in React Native\n        !(e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) && // ignore clicks with modifier keys\n        // @ts-expect-error: these properties exist on web, but not in React Native\n        (e.button == null || e.button === 0) && // ignore everything but left clicks\n        // @ts-expect-error: these properties exist on web, but not in React Native\n        [\n            undefined,\n            null,\n            \"\",\n            \"self\"\n        ].includes(e.currentTarget?.target) // let browser handle \"target=_blank\" etc.\n        ) {\n            e.preventDefault();\n            shouldHandle = true;\n        }\n        if (shouldHandle) {\n            if (href === \"#\") {\n                // this is a way on web to stay on the same page\n                // useful for conditional hrefs\n                return;\n            }\n            if (replace) {\n                router.replace(href, {\n                    experimental\n                });\n            } else {\n                router.push(href);\n            }\n        }\n    };\n    return {\n        accessibilityRole: \"link\",\n        onPress,\n        href\n    };\n} //# sourceMappingURL=use-link.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/app/navigation/use-link.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/app/navigation/use-router.js":
/*!********************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/use-router.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRouter: () => (/* binding */ useRouter)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n/* harmony import */ var _router_parse_next_path__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../router/parse-next-path */ \"../../node_modules/solito/build/router/parse-next-path.js\");\n/* harmony import */ var _router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../router/replace-helpers */ \"../../node_modules/solito/build/router/replace-helpers.web.js\");\n/* harmony import */ var _router_use_link_to__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../router/use-link-to */ \"../../node_modules/solito/build/router/use-link-to.web.js\");\n/* harmony import */ var _router_use_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../router/use-navigation */ \"../../node_modules/solito/build/router/use-navigation.web.js\");\n/* harmony import */ var _use_next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-next-router */ \"../../node_modules/next/navigation.js\");\n/* harmony import */ var _use_next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_use_next_router__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n\n\n\nfunction useRouter() {\n    const linkTo = (0,_router_use_link_to__WEBPACK_IMPORTED_MODULE_1__.useLinkTo)();\n    const navigation = (0,_router_use_navigation__WEBPACK_IMPORTED_MODULE_2__.useNavigation)();\n    const nextRouter = (0,_use_next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const linking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.LinkingContext);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({\n            push: (url, navigateOptions)=>{\n                if (react_native__WEBPACK_IMPORTED_MODULE_5__.Platform.OS === \"web\") {\n                    nextRouter?.push(url, navigateOptions);\n                } else {\n                    const to = (0,_router_parse_next_path__WEBPACK_IMPORTED_MODULE_6__.parseNextPath)(url);\n                    if (to) {\n                        linkTo(to);\n                    }\n                }\n            },\n            replace: (url, navigateOptions)=>{\n                if (react_native__WEBPACK_IMPORTED_MODULE_5__.Platform.OS === \"web\") {\n                    nextRouter?.replace(url, navigateOptions);\n                } else {\n                    const to = (0,_router_parse_next_path__WEBPACK_IMPORTED_MODULE_6__.parseNextPath)(url);\n                    if (to) {\n                        if (navigateOptions?.experimental?.nativeBehavior === \"stack-replace\") {\n                            if (linking?.options) {\n                                // custom logic to create a replace() from a URL on native\n                                // https://github.com/react-navigation/react-navigation/discussions/10517\n                                const { options } = linking;\n                                const state = options?.getStateFromPath ? options.getStateFromPath(to, options.config) : (0,_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.getStateFromPath)(to, options?.config);\n                                if (state) {\n                                    const action = (0,_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.getActionFromState)(state, options?.config);\n                                    if (action !== undefined) {\n                                        if (\"payload\" in action && action.payload && \"name\" in action.payload && action.payload.name) {\n                                            const { name, params } = action.payload;\n                                            if (navigateOptions?.experimental?.isNestedNavigator && params && \"screen\" in params && params.screen) {\n                                                navigation?.dispatch(_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.StackActions.replace(params.screen, params.params));\n                                            } else {\n                                                navigation?.dispatch(_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.StackActions.replace(name, params));\n                                            }\n                                        } else {\n                                            navigation?.dispatch(action);\n                                        }\n                                    } else {\n                                        navigation?.reset(state);\n                                    }\n                                }\n                            } else {\n                                // fallback in case the linking context didn't work\n                                console.warn(`[solito] replace(\"${to}\") faced an issue. You should still see your new screen, but it probably didn't replace the previous one. This may be due to a breaking change in React Navigation. \n  Please open an issue at https://github.com/nandorojo/solito and report how this happened. Thanks!`);\n                                linkTo(to);\n                            }\n                        } else {\n                            linkTo(to);\n                        }\n                    }\n                }\n            },\n            back: ()=>{\n                if (react_native__WEBPACK_IMPORTED_MODULE_5__.Platform.OS === \"web\") {\n                    nextRouter?.back();\n                } else {\n                    navigation?.goBack();\n                }\n            },\n            parseNextPath: _router_parse_next_path__WEBPACK_IMPORTED_MODULE_6__.parseNextPath\n        }), [\n        linkTo,\n        navigation\n    ]);\n} //# sourceMappingURL=use-router.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/app/navigation/use-router.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/params/index.js":
/*!*******************************************************!*\
  !*** ../../node_modules/solito/build/params/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createParam: () => (/* binding */ createParam)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n/* harmony import */ var _router_use_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../router/use-navigation */ \"../../node_modules/solito/build/router/use-navigation.web.js\");\n/* harmony import */ var _router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var _router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _use_route__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-route */ \"../../node_modules/solito/build/params/use-route.web.js\");\n/* harmony import */ var _use_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-router */ \"../../node_modules/solito/build/params/use-router.web.js\");\n/* eslint-disable react-hooks/rules-of-hooks */ // From https://gist.github.com/nandorojo/052887f99bb61b54845474f324aa41cc\n\n\n\n\n\n\nfunction useStable(value) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(value);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        ref.current = value;\n    }, [\n        value\n    ]);\n    return ref;\n}\nfunction useStableCallback(callback) {\n    const callbackRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(callback);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        callbackRef.current = callback;\n    });\n    // https://github.com/facebook/react/issues/19240\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>(...args)=>callbackRef.current?.(...args), []);\n}\nfunction createParam() {\n    function useParam(...[name, maybeConfig]) {\n        const { parse = (value)=>value, initial, stringify, paramsToClearOnSetState } = maybeConfig || {};\n        const nextRouter = (0,_use_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n        const nativeRoute = (0,_use_route__WEBPACK_IMPORTED_MODULE_2__.useRoute)();\n        const nativeNavigation = (0,_router_use_navigation__WEBPACK_IMPORTED_MODULE_3__.useNavigation)();\n        const nativeStateFromParams = nativeRoute?.params?.[name];\n        const [nativeStateFromReact, setNativeStateFromReact] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(()=>nativeStateFromParams ?? initial);\n        const setNativeStateFromParams = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((value)=>{\n            nativeNavigation?.setParams({\n                [name]: value\n            });\n        }, []);\n        const nativeState = nativeRoute ? nativeStateFromParams : nativeStateFromReact;\n        const setNativeState = nativeRoute ? setNativeStateFromParams : setNativeStateFromReact;\n        const stableStringify = useStable(stringify);\n        const stableParse = useStableCallback(parse);\n        const stableParamsToClear = useStable(paramsToClearOnSetState);\n        const initialValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(initial);\n        const hasSetState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n        const setState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((value, options)=>{\n            hasSetState.current = true;\n            const { pathname, query } = (_router__WEBPACK_IMPORTED_MODULE_4___default());\n            const newQuery = {\n                ...query\n            };\n            if (value != null && value !== \"\") {\n                if (stableStringify.current) {\n                    newQuery[name] = stableStringify.current(value);\n                } else {\n                    newQuery[name] = value;\n                }\n            } else {\n                delete newQuery[name];\n            }\n            if (stableParamsToClear.current) {\n                for (const paramKey of stableParamsToClear.current){\n                    delete newQuery[paramKey];\n                }\n            }\n            const willChangeExistingParam = query[name] && newQuery[name];\n            let action = willChangeExistingParam ? (_router__WEBPACK_IMPORTED_MODULE_4___default().replace) : (_router__WEBPACK_IMPORTED_MODULE_4___default().push);\n            if (options?.webBehavior) {\n                action = (_router__WEBPACK_IMPORTED_MODULE_4___default())[options.webBehavior];\n            }\n            action({\n                pathname,\n                query: newQuery\n            }, undefined, {\n                shallow: true\n            });\n        }, [\n            name,\n            stableStringify,\n            stableParamsToClear\n        ]);\n        const webParam = nextRouter?.query?.[name];\n        const state = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n            let state;\n            if (webParam === undefined && !hasSetState.current) {\n                state = initialValue.current;\n            } else {\n                state = stableParse(webParam);\n            }\n            return state;\n        }, [\n            stableParse,\n            webParam\n        ]);\n        if (react_native__WEBPACK_IMPORTED_MODULE_5__.Platform.OS !== \"web\") {\n            if (!nativeRoute) {\n                console.error(`[solito] useParam('${name}') called when there is no React Navigation route available. In a future version, this will throw an error. Please fix this by only calling useParam() inside of a React Navigation route. For now, Solito will fallback to using React state.`);\n            }\n            return [\n                nativeState,\n                setNativeState\n            ];\n        }\n        return [\n            state,\n            setState\n        ];\n    }\n    function useUpdateParams() {\n        const nativeNavigation = (0,_router_use_navigation__WEBPACK_IMPORTED_MODULE_3__.useNavigation)();\n        const setNativeStateFromParams = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((value)=>{\n            nativeNavigation?.setParams(value);\n        }, []);\n        const setWebState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((value, options)=>{\n            const { pathname, query } = (_router__WEBPACK_IMPORTED_MODULE_4___default());\n            const newQuery = {\n                ...query,\n                ...value\n            };\n            for(const key in value){\n                if (value[key] == null || value[key] === \"\") {\n                    delete newQuery[key];\n                }\n            }\n            const action = options?.web?.replace ? (_router__WEBPACK_IMPORTED_MODULE_4___default().replace) : (_router__WEBPACK_IMPORTED_MODULE_4___default().push);\n            action({\n                pathname,\n                query: newQuery\n            }, undefined, {\n                shallow: true\n            });\n        }, []);\n        return react_native__WEBPACK_IMPORTED_MODULE_5__.Platform.select({\n            web: setWebState,\n            default: setNativeStateFromParams\n        });\n    }\n    const empty = {};\n    function useParams() {\n        if (react_native__WEBPACK_IMPORTED_MODULE_5__.Platform.OS !== \"web\") {\n            const nativeRoute = (0,_use_route__WEBPACK_IMPORTED_MODULE_2__.useRoute)();\n            const nativeNavigation = (0,_router_use_navigation__WEBPACK_IMPORTED_MODULE_3__.useNavigation)();\n            return {\n                params: nativeRoute?.params ?? empty,\n                setParams: (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((params)=>nativeNavigation?.setParams(params), [\n                    nativeNavigation\n                ])\n            };\n        }\n        const nextRouter = (0,_use_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n        return {\n            params: nextRouter?.query,\n            setParams: (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((params, options)=>{\n                const { pathname, query } = (_router__WEBPACK_IMPORTED_MODULE_4___default());\n                const newQuery = {\n                    ...query,\n                    ...params\n                };\n                for(const key in params){\n                    if (params[key] == null) {\n                        delete newQuery[key];\n                    }\n                }\n                const action = (_router__WEBPACK_IMPORTED_MODULE_4___default())[options?.webBehavior ?? \"push\"];\n                action({\n                    pathname,\n                    query: newQuery\n                }, undefined, {\n                    shallow: true\n                });\n            }, [])\n        };\n    }\n    return {\n        useParam,\n        useUpdateParams,\n        useParams\n    };\n} //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/params/index.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/params/use-route.web.js":
/*!***************************************************************!*\
  !*** ../../node_modules/solito/build/params/use-route.web.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRoute: () => (/* binding */ useRoute)\n/* harmony export */ });\nconst useRoute = ()=>undefined; //# sourceMappingURL=use-route.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9wYXJhbXMvdXNlLXJvdXRlLndlYi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU8sTUFBTUEsV0FBVyxJQUFNQyxVQUFTIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vc3JjL3BhcmFtcy91c2Utcm91dGUud2ViLnRzPzM4NDkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHVzZVJvdXRlID0gKCkgPT4gdW5kZWZpbmVkXG4iXSwibmFtZXMiOlsidXNlUm91dGUiLCJ1bmRlZmluZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/params/use-route.web.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/params/use-router.web.js":
/*!****************************************************************!*\
  !*** ../../node_modules/solito/build/params/use-router.web.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRouter: () => (/* binding */ useRouter)\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useRouter = ()=>(0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)(); //# sourceMappingURL=use-router.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9wYXJhbXMvdXNlLXJvdXRlci53ZWIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXNEO0FBRS9DLE1BQU1BLFlBQVksSUFBTUMsc0RBQVVBLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9zcmMvcGFyYW1zL3VzZS1yb3V0ZXIud2ViLnRzP2UyODYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlUm91dGVyIGFzIF91c2VSb3V0ZXIgfSBmcm9tICduZXh0L3JvdXRlcic7XG5cbmV4cG9ydCBjb25zdCB1c2VSb3V0ZXIgPSAoKSA9PiBfdXNlUm91dGVyKCk7Il0sIm5hbWVzIjpbInVzZVJvdXRlciIsIl91c2VSb3V0ZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/params/use-router.web.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/router/parse-next-path.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/solito/build/router/parse-next-path.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseNextPath: () => (/* binding */ parseNextPath)\n/* harmony export */ });\nconst parseNextPath = (from)=>{\n    let path = (typeof from == \"string\" ? from : from.pathname) || \"\";\n    // replace each instance of [key] with the corresponding value from query[key]\n    // this ensures we're navigating to the correct URL\n    // it currently ignores [[...param]]\n    // but I can't see why you would use this with RN + Next.js\n    if (typeof from == \"object\" && from.query && typeof from.query == \"object\") {\n        const query = {\n            ...from.query\n        };\n        // replace dynamic routes\n        // and [...param] syntax\n        for(const key in query){\n            if (path.includes(`[${key}]`)) {\n                path = path.replace(`[${key}]`, `${query[key] ?? \"\"}`);\n                delete query[key];\n            } else if (path.includes(`[...${key}]`)) {\n                const values = query[key];\n                if (Array.isArray(values)) {\n                    path = path.replace(`[...${key}]`, values.join(\"/\"));\n                    delete query[key];\n                }\n            }\n        }\n        if (Object.keys(query).length) {\n            // add query param separator\n            path += \"?\";\n            for(const key in query){\n                const value = query[key];\n                if (Array.isArray(value)) {\n                    value.forEach((item)=>{\n                        path += `${key}=${item}&`;\n                    });\n                } else if (value != null) {\n                    path += `${key}=${value}&`;\n                }\n            }\n            if (path.endsWith(\"&\") || path.endsWith(\"?\")) {\n                path = path.slice(0, -1);\n            }\n        }\n    }\n    return path;\n};\n //# sourceMappingURL=parse-next-path.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/router/parse-next-path.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/router/replace-helpers.web.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/solito/build/router/replace-helpers.web.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LinkingContext: () => (/* binding */ LinkingContext),\n/* harmony export */   StackActions: () => (/* binding */ StackActions),\n/* harmony export */   getActionFromState: () => (/* binding */ getActionFromState),\n/* harmony export */   getStateFromPath: () => (/* binding */ getStateFromPath)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst LinkingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n    options: undefined\n});\nlet StackActions, getStateFromPath, getActionFromState;\n //# sourceMappingURL=replace-helpers.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9yb3V0ZXIvcmVwbGFjZS1oZWxwZXJzLndlYi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBcUM7QUFFckMsTUFBTUMsK0JBQWlCRCxvREFBYUEsQ0FBQztJQUNuQ0UsU0FBU0M7O0FBR1gsSUFBSUMsY0FBY0Msa0JBQWtCQztBQUV5QyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL3NyYy9yb3V0ZXIvcmVwbGFjZS1oZWxwZXJzLndlYi50cz80ZjA4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUNvbnRleHQgfSBmcm9tICdyZWFjdCdcblxuY29uc3QgTGlua2luZ0NvbnRleHQgPSBjcmVhdGVDb250ZXh0KHtcbiAgb3B0aW9uczogdW5kZWZpbmVkLFxufSlcblxubGV0IFN0YWNrQWN0aW9ucywgZ2V0U3RhdGVGcm9tUGF0aCwgZ2V0QWN0aW9uRnJvbVN0YXRlXG5cbmV4cG9ydCB7IExpbmtpbmdDb250ZXh0LCBTdGFja0FjdGlvbnMsIGdldFN0YXRlRnJvbVBhdGgsIGdldEFjdGlvbkZyb21TdGF0ZSB9XG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsIkxpbmtpbmdDb250ZXh0Iiwib3B0aW9ucyIsInVuZGVmaW5lZCIsIlN0YWNrQWN0aW9ucyIsImdldFN0YXRlRnJvbVBhdGgiLCJnZXRBY3Rpb25Gcm9tU3RhdGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/router/replace-helpers.web.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/router/use-link-to.web.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/solito/build/router/use-link-to.web.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLinkTo: () => (/* binding */ useLinkTo)\n/* harmony export */ });\nconst noOp = ()=>{\n    throw new Error(\"[use-link-to] is not supported on the web. Something went wrong if you called this.\");\n};\n/**\n * @deprecated imported from the wrong file. Use `use-link-to` instead.\n */ const useLinkTo = ()=>noOp; //# sourceMappingURL=use-link-to.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9yb3V0ZXIvdXNlLWxpbmstdG8ud2ViLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxNQUFNQSxPQUFPO0lBQ1gsTUFBTSxJQUFJQyxNQUNSO0FBRUo7QUFFQTs7SUFHTyxNQUFNQyxZQUFZLElBQU1GLEtBQUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9zcmMvcm91dGVyL3VzZS1saW5rLXRvLndlYi50cz8wMTkwIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG5vT3AgPSAoKSA9PiB7XG4gIHRocm93IG5ldyBFcnJvcihcbiAgICAnW3VzZS1saW5rLXRvXSBpcyBub3Qgc3VwcG9ydGVkIG9uIHRoZSB3ZWIuIFNvbWV0aGluZyB3ZW50IHdyb25nIGlmIHlvdSBjYWxsZWQgdGhpcy4nXG4gIClcbn1cblxuLyoqXG4gKiBAZGVwcmVjYXRlZCBpbXBvcnRlZCBmcm9tIHRoZSB3cm9uZyBmaWxlLiBVc2UgYHVzZS1saW5rLXRvYCBpbnN0ZWFkLlxuICovXG5leHBvcnQgY29uc3QgdXNlTGlua1RvID0gKCkgPT4gbm9PcFxuIl0sIm5hbWVzIjpbIm5vT3AiLCJFcnJvciIsInVzZUxpbmtUbyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/router/use-link-to.web.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/router/use-navigation.web.js":
/*!********************************************************************!*\
  !*** ../../node_modules/solito/build/router/use-navigation.web.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useNavigation: () => (/* binding */ useNavigation)\n/* harmony export */ });\nconst useNavigation = ()=>undefined; //# sourceMappingURL=use-navigation.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9yb3V0ZXIvdXNlLW5hdmlnYXRpb24ud2ViLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxNQUFNQSxnQkFBZ0IsSUFBTUMsVUFBUyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL3NyYy9yb3V0ZXIvdXNlLW5hdmlnYXRpb24ud2ViLnRzP2E4NjciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHVzZU5hdmlnYXRpb24gPSAoKSA9PiB1bmRlZmluZWRcbiJdLCJuYW1lcyI6WyJ1c2VOYXZpZ2F0aW9uIiwidW5kZWZpbmVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/router/use-navigation.web.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/solito/build/app/navigation/use-link.js":
/*!******************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/use-link.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLink: () => (/* binding */ useLink)\n/* harmony export */ });\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-native */ \"(ssr)/../../node_modules/react-native-web/dist/exports/Platform/index.js\");\n/* harmony import */ var _use_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-router */ \"(ssr)/../../node_modules/solito/build/app/navigation/use-router.js\");\n\n\nfunction useLink({ href, replace, experimental }) {\n    const router = (0,_use_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    // https://github.com/react-navigation/react-navigation/blob/main/packages/native/src/useLinkProps.tsx#L64\n    const onPress = (e)=>{\n        let shouldHandle = false;\n        if (react_native__WEBPACK_IMPORTED_MODULE_1__[\"default\"].OS !== \"web\" || !e) {\n            shouldHandle = e ? !e.defaultPrevented : true;\n        } else if (!e.defaultPrevented && // onPress prevented default\n        // @ts-expect-error: these properties exist on web, but not in React Native\n        !(e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) && // ignore clicks with modifier keys\n        // @ts-expect-error: these properties exist on web, but not in React Native\n        (e.button == null || e.button === 0) && // ignore everything but left clicks\n        // @ts-expect-error: these properties exist on web, but not in React Native\n        [\n            undefined,\n            null,\n            \"\",\n            \"self\"\n        ].includes(e.currentTarget?.target) // let browser handle \"target=_blank\" etc.\n        ) {\n            e.preventDefault();\n            shouldHandle = true;\n        }\n        if (shouldHandle) {\n            if (href === \"#\") {\n                // this is a way on web to stay on the same page\n                // useful for conditional hrefs\n                return;\n            }\n            if (replace) {\n                router.replace(href, {\n                    experimental\n                });\n            } else {\n                router.push(href);\n            }\n        }\n    };\n    return {\n        accessibilityRole: \"link\",\n        onPress,\n        href\n    };\n} //# sourceMappingURL=use-link.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/solito/build/app/navigation/use-link.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/solito/build/app/navigation/use-next-params.js":
/*!*************************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/use-next-params.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/next/dist/api/navigation.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (()=>{\n    // need to cast this type to appease TS, idk why\n    return (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.useParams)();\n}); //# sourceMappingURL=use-next-params.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9hcHAvbmF2aWdhdGlvbi91c2UtbmV4dC1wYXJhbXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMkM7QUFFM0MsaUVBQWU7SUFDYixnREFBZ0Q7SUFDaEQsT0FBT0EsMERBQVNBO0FBQ2xCLEdBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi8uLi9zcmMvYXBwL25hdmlnYXRpb24vdXNlLW5leHQtcGFyYW1zLnRzPzJhNmYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlUGFyYW1zIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuXG5leHBvcnQgZGVmYXVsdCAoKSA9PiB7XG4gIC8vIG5lZWQgdG8gY2FzdCB0aGlzIHR5cGUgdG8gYXBwZWFzZSBUUywgaWRrIHdoeVxuICByZXR1cm4gdXNlUGFyYW1zKCkgYXMgUmVjb3JkPHN0cmluZywgc3RyaW5nPiB8IHVuZGVmaW5lZFxufVxuIl0sIm5hbWVzIjpbInVzZVBhcmFtcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/solito/build/app/navigation/use-next-params.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/solito/build/app/navigation/use-params.js":
/*!********************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/use-params.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useParams: () => (/* binding */ useParams)\n/* harmony export */ });\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-native */ \"(ssr)/../../node_modules/react-native-web/dist/exports/Platform/index.js\");\n/* harmony import */ var _use_next_params__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-next-params */ \"(ssr)/../../node_modules/solito/build/app/navigation/use-next-params.js\");\n/* harmony import */ var _params_use_route__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../params/use-route */ \"(ssr)/../../node_modules/solito/build/params/use-route.web.js\");\n\n\n\nfunction useParams(_settings = {}) {\n    if (react_native__WEBPACK_IMPORTED_MODULE_0__[\"default\"].OS === \"web\") {\n        return (0,_use_next_params__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    }\n    const route = (0,_params_use_route__WEBPACK_IMPORTED_MODULE_2__.useRoute)();\n    if (!route) {\n        console.error(`[useParams] route is undefined. Is your ${react_native__WEBPACK_IMPORTED_MODULE_0__[\"default\"].OS} app properly configured for React Navigation?`);\n    }\n    return route?.params;\n} //# sourceMappingURL=use-params.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/solito/build/app/navigation/use-params.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/solito/build/app/navigation/use-router.js":
/*!********************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/use-router.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRouter: () => (/* binding */ useRouter)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-native */ \"(ssr)/../../node_modules/react-native-web/dist/exports/Platform/index.js\");\n/* harmony import */ var _router_parse_next_path__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../router/parse-next-path */ \"(ssr)/../../node_modules/solito/build/router/parse-next-path.js\");\n/* harmony import */ var _router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../router/replace-helpers */ \"(ssr)/../../node_modules/solito/build/router/replace-helpers.web.js\");\n/* harmony import */ var _router_use_link_to__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../router/use-link-to */ \"(ssr)/../../node_modules/solito/build/router/use-link-to.web.js\");\n/* harmony import */ var _router_use_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../router/use-navigation */ \"(ssr)/../../node_modules/solito/build/router/use-navigation.web.js\");\n/* harmony import */ var _use_next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-next-router */ \"(ssr)/../../node_modules/next/dist/api/navigation.js\");\n\n\n\n\n\n\n\nfunction useRouter() {\n    const linkTo = (0,_router_use_link_to__WEBPACK_IMPORTED_MODULE_1__.useLinkTo)();\n    const navigation = (0,_router_use_navigation__WEBPACK_IMPORTED_MODULE_2__.useNavigation)();\n    const nextRouter = (0,_use_next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const linking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.LinkingContext);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({\n            push: (url, navigateOptions)=>{\n                if (react_native__WEBPACK_IMPORTED_MODULE_5__[\"default\"].OS === \"web\") {\n                    nextRouter?.push(url, navigateOptions);\n                } else {\n                    const to = (0,_router_parse_next_path__WEBPACK_IMPORTED_MODULE_6__.parseNextPath)(url);\n                    if (to) {\n                        linkTo(to);\n                    }\n                }\n            },\n            replace: (url, navigateOptions)=>{\n                if (react_native__WEBPACK_IMPORTED_MODULE_5__[\"default\"].OS === \"web\") {\n                    nextRouter?.replace(url, navigateOptions);\n                } else {\n                    const to = (0,_router_parse_next_path__WEBPACK_IMPORTED_MODULE_6__.parseNextPath)(url);\n                    if (to) {\n                        if (navigateOptions?.experimental?.nativeBehavior === \"stack-replace\") {\n                            if (linking?.options) {\n                                // custom logic to create a replace() from a URL on native\n                                // https://github.com/react-navigation/react-navigation/discussions/10517\n                                const { options } = linking;\n                                const state = options?.getStateFromPath ? options.getStateFromPath(to, options.config) : (0,_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.getStateFromPath)(to, options?.config);\n                                if (state) {\n                                    const action = (0,_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.getActionFromState)(state, options?.config);\n                                    if (action !== undefined) {\n                                        if (\"payload\" in action && action.payload && \"name\" in action.payload && action.payload.name) {\n                                            const { name, params } = action.payload;\n                                            if (navigateOptions?.experimental?.isNestedNavigator && params && \"screen\" in params && params.screen) {\n                                                navigation?.dispatch(_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.StackActions.replace(params.screen, params.params));\n                                            } else {\n                                                navigation?.dispatch(_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.StackActions.replace(name, params));\n                                            }\n                                        } else {\n                                            navigation?.dispatch(action);\n                                        }\n                                    } else {\n                                        navigation?.reset(state);\n                                    }\n                                }\n                            } else {\n                                // fallback in case the linking context didn't work\n                                console.warn(`[solito] replace(\"${to}\") faced an issue. You should still see your new screen, but it probably didn't replace the previous one. This may be due to a breaking change in React Navigation. \n  Please open an issue at https://github.com/nandorojo/solito and report how this happened. Thanks!`);\n                                linkTo(to);\n                            }\n                        } else {\n                            linkTo(to);\n                        }\n                    }\n                }\n            },\n            back: ()=>{\n                if (react_native__WEBPACK_IMPORTED_MODULE_5__[\"default\"].OS === \"web\") {\n                    nextRouter?.back();\n                } else {\n                    navigation?.goBack();\n                }\n            },\n            parseNextPath: _router_parse_next_path__WEBPACK_IMPORTED_MODULE_6__.parseNextPath\n        }), [\n        linkTo,\n        navigation\n    ]);\n} //# sourceMappingURL=use-router.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/solito/build/app/navigation/use-router.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/solito/build/params/use-route.web.js":
/*!***************************************************************!*\
  !*** ../../node_modules/solito/build/params/use-route.web.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRoute: () => (/* binding */ useRoute)\n/* harmony export */ });\nconst useRoute = ()=>undefined; //# sourceMappingURL=use-route.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9wYXJhbXMvdXNlLXJvdXRlLndlYi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU8sTUFBTUEsV0FBVyxJQUFNQyxVQUFTIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vc3JjL3BhcmFtcy91c2Utcm91dGUud2ViLnRzPzM4NDkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHVzZVJvdXRlID0gKCkgPT4gdW5kZWZpbmVkXG4iXSwibmFtZXMiOlsidXNlUm91dGUiLCJ1bmRlZmluZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/solito/build/params/use-route.web.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/solito/build/router/parse-next-path.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/solito/build/router/parse-next-path.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseNextPath: () => (/* binding */ parseNextPath)\n/* harmony export */ });\nconst parseNextPath = (from)=>{\n    let path = (typeof from == \"string\" ? from : from.pathname) || \"\";\n    // replace each instance of [key] with the corresponding value from query[key]\n    // this ensures we're navigating to the correct URL\n    // it currently ignores [[...param]]\n    // but I can't see why you would use this with RN + Next.js\n    if (typeof from == \"object\" && from.query && typeof from.query == \"object\") {\n        const query = {\n            ...from.query\n        };\n        // replace dynamic routes\n        // and [...param] syntax\n        for(const key in query){\n            if (path.includes(`[${key}]`)) {\n                path = path.replace(`[${key}]`, `${query[key] ?? \"\"}`);\n                delete query[key];\n            } else if (path.includes(`[...${key}]`)) {\n                const values = query[key];\n                if (Array.isArray(values)) {\n                    path = path.replace(`[...${key}]`, values.join(\"/\"));\n                    delete query[key];\n                }\n            }\n        }\n        if (Object.keys(query).length) {\n            // add query param separator\n            path += \"?\";\n            for(const key in query){\n                const value = query[key];\n                if (Array.isArray(value)) {\n                    value.forEach((item)=>{\n                        path += `${key}=${item}&`;\n                    });\n                } else if (value != null) {\n                    path += `${key}=${value}&`;\n                }\n            }\n            if (path.endsWith(\"&\") || path.endsWith(\"?\")) {\n                path = path.slice(0, -1);\n            }\n        }\n    }\n    return path;\n};\n //# sourceMappingURL=parse-next-path.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/solito/build/router/parse-next-path.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/solito/build/router/replace-helpers.web.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/solito/build/router/replace-helpers.web.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LinkingContext: () => (/* binding */ LinkingContext),\n/* harmony export */   StackActions: () => (/* binding */ StackActions),\n/* harmony export */   getActionFromState: () => (/* binding */ getActionFromState),\n/* harmony export */   getStateFromPath: () => (/* binding */ getStateFromPath)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst LinkingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n    options: undefined\n});\nlet StackActions, getStateFromPath, getActionFromState;\n //# sourceMappingURL=replace-helpers.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9yb3V0ZXIvcmVwbGFjZS1oZWxwZXJzLndlYi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBcUM7QUFFckMsTUFBTUMsK0JBQWlCRCxvREFBYUEsQ0FBQztJQUNuQ0UsU0FBU0M7O0FBR1gsSUFBSUMsY0FBY0Msa0JBQWtCQztBQUV5QyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL3NyYy9yb3V0ZXIvcmVwbGFjZS1oZWxwZXJzLndlYi50cz80ZjA4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUNvbnRleHQgfSBmcm9tICdyZWFjdCdcblxuY29uc3QgTGlua2luZ0NvbnRleHQgPSBjcmVhdGVDb250ZXh0KHtcbiAgb3B0aW9uczogdW5kZWZpbmVkLFxufSlcblxubGV0IFN0YWNrQWN0aW9ucywgZ2V0U3RhdGVGcm9tUGF0aCwgZ2V0QWN0aW9uRnJvbVN0YXRlXG5cbmV4cG9ydCB7IExpbmtpbmdDb250ZXh0LCBTdGFja0FjdGlvbnMsIGdldFN0YXRlRnJvbVBhdGgsIGdldEFjdGlvbkZyb21TdGF0ZSB9XG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsIkxpbmtpbmdDb250ZXh0Iiwib3B0aW9ucyIsInVuZGVmaW5lZCIsIlN0YWNrQWN0aW9ucyIsImdldFN0YXRlRnJvbVBhdGgiLCJnZXRBY3Rpb25Gcm9tU3RhdGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/solito/build/router/replace-helpers.web.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/solito/build/router/use-link-to.web.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/solito/build/router/use-link-to.web.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLinkTo: () => (/* binding */ useLinkTo)\n/* harmony export */ });\nconst noOp = ()=>{\n    throw new Error(\"[use-link-to] is not supported on the web. Something went wrong if you called this.\");\n};\n/**\n * @deprecated imported from the wrong file. Use `use-link-to` instead.\n */ const useLinkTo = ()=>noOp; //# sourceMappingURL=use-link-to.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9yb3V0ZXIvdXNlLWxpbmstdG8ud2ViLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxNQUFNQSxPQUFPO0lBQ1gsTUFBTSxJQUFJQyxNQUNSO0FBRUo7QUFFQTs7SUFHTyxNQUFNQyxZQUFZLElBQU1GLEtBQUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9zcmMvcm91dGVyL3VzZS1saW5rLXRvLndlYi50cz8wMTkwIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG5vT3AgPSAoKSA9PiB7XG4gIHRocm93IG5ldyBFcnJvcihcbiAgICAnW3VzZS1saW5rLXRvXSBpcyBub3Qgc3VwcG9ydGVkIG9uIHRoZSB3ZWIuIFNvbWV0aGluZyB3ZW50IHdyb25nIGlmIHlvdSBjYWxsZWQgdGhpcy4nXG4gIClcbn1cblxuLyoqXG4gKiBAZGVwcmVjYXRlZCBpbXBvcnRlZCBmcm9tIHRoZSB3cm9uZyBmaWxlLiBVc2UgYHVzZS1saW5rLXRvYCBpbnN0ZWFkLlxuICovXG5leHBvcnQgY29uc3QgdXNlTGlua1RvID0gKCkgPT4gbm9PcFxuIl0sIm5hbWVzIjpbIm5vT3AiLCJFcnJvciIsInVzZUxpbmtUbyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/solito/build/router/use-link-to.web.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/solito/build/router/use-navigation.web.js":
/*!********************************************************************!*\
  !*** ../../node_modules/solito/build/router/use-navigation.web.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useNavigation: () => (/* binding */ useNavigation)\n/* harmony export */ });\nconst useNavigation = ()=>undefined; //# sourceMappingURL=use-navigation.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9yb3V0ZXIvdXNlLW5hdmlnYXRpb24ud2ViLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxNQUFNQSxnQkFBZ0IsSUFBTUMsVUFBUyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL3NyYy9yb3V0ZXIvdXNlLW5hdmlnYXRpb24ud2ViLnRzP2E4NjciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHVzZU5hdmlnYXRpb24gPSAoKSA9PiB1bmRlZmluZWRcbiJdLCJuYW1lcyI6WyJ1c2VOYXZpZ2F0aW9uIiwidW5kZWZpbmVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/solito/build/router/use-navigation.web.js\n");

/***/ })

};
;