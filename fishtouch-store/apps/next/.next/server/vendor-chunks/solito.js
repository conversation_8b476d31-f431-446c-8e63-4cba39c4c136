"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/solito";
exports.ids = ["vendor-chunks/solito"];
exports.modules = {

/***/ "../../node_modules/solito/build/app/navigation/use-link.js":
/*!******************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/use-link.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLink: () => (/* binding */ useLink)\n/* harmony export */ });\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n/* harmony import */ var _use_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-router */ \"../../node_modules/solito/build/app/navigation/use-router.js\");\n\n\nfunction useLink({ href, replace, experimental }) {\n    const router = (0,_use_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    // https://github.com/react-navigation/react-navigation/blob/main/packages/native/src/useLinkProps.tsx#L64\n    const onPress = (e)=>{\n        let shouldHandle = false;\n        if (react_native__WEBPACK_IMPORTED_MODULE_1__.Platform.OS !== \"web\" || !e) {\n            shouldHandle = e ? !e.defaultPrevented : true;\n        } else if (!e.defaultPrevented && // onPress prevented default\n        // @ts-expect-error: these properties exist on web, but not in React Native\n        !(e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) && // ignore clicks with modifier keys\n        // @ts-expect-error: these properties exist on web, but not in React Native\n        (e.button == null || e.button === 0) && // ignore everything but left clicks\n        // @ts-expect-error: these properties exist on web, but not in React Native\n        [\n            undefined,\n            null,\n            \"\",\n            \"self\"\n        ].includes(e.currentTarget?.target) // let browser handle \"target=_blank\" etc.\n        ) {\n            e.preventDefault();\n            shouldHandle = true;\n        }\n        if (shouldHandle) {\n            if (href === \"#\") {\n                // this is a way on web to stay on the same page\n                // useful for conditional hrefs\n                return;\n            }\n            if (replace) {\n                router.replace(href, {\n                    experimental\n                });\n            } else {\n                router.push(href);\n            }\n        }\n    };\n    return {\n        accessibilityRole: \"link\",\n        onPress,\n        href\n    };\n} //# sourceMappingURL=use-link.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/app/navigation/use-link.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/app/navigation/use-router.js":
/*!********************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/use-router.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRouter: () => (/* binding */ useRouter)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n/* harmony import */ var _router_parse_next_path__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../router/parse-next-path */ \"../../node_modules/solito/build/router/parse-next-path.js\");\n/* harmony import */ var _router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../router/replace-helpers */ \"../../node_modules/solito/build/router/replace-helpers.web.js\");\n/* harmony import */ var _router_use_link_to__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../router/use-link-to */ \"../../node_modules/solito/build/router/use-link-to.web.js\");\n/* harmony import */ var _router_use_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../router/use-navigation */ \"../../node_modules/solito/build/router/use-navigation.web.js\");\n/* harmony import */ var _use_next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-next-router */ \"../../node_modules/next/navigation.js\");\n/* harmony import */ var _use_next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_use_next_router__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n\n\n\nfunction useRouter() {\n    const linkTo = (0,_router_use_link_to__WEBPACK_IMPORTED_MODULE_1__.useLinkTo)();\n    const navigation = (0,_router_use_navigation__WEBPACK_IMPORTED_MODULE_2__.useNavigation)();\n    const nextRouter = (0,_use_next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const linking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.LinkingContext);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({\n            push: (url, navigateOptions)=>{\n                if (react_native__WEBPACK_IMPORTED_MODULE_5__.Platform.OS === \"web\") {\n                    nextRouter?.push(url, navigateOptions);\n                } else {\n                    const to = (0,_router_parse_next_path__WEBPACK_IMPORTED_MODULE_6__.parseNextPath)(url);\n                    if (to) {\n                        linkTo(to);\n                    }\n                }\n            },\n            replace: (url, navigateOptions)=>{\n                if (react_native__WEBPACK_IMPORTED_MODULE_5__.Platform.OS === \"web\") {\n                    nextRouter?.replace(url, navigateOptions);\n                } else {\n                    const to = (0,_router_parse_next_path__WEBPACK_IMPORTED_MODULE_6__.parseNextPath)(url);\n                    if (to) {\n                        if (navigateOptions?.experimental?.nativeBehavior === \"stack-replace\") {\n                            if (linking?.options) {\n                                // custom logic to create a replace() from a URL on native\n                                // https://github.com/react-navigation/react-navigation/discussions/10517\n                                const { options } = linking;\n                                const state = options?.getStateFromPath ? options.getStateFromPath(to, options.config) : (0,_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.getStateFromPath)(to, options?.config);\n                                if (state) {\n                                    const action = (0,_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.getActionFromState)(state, options?.config);\n                                    if (action !== undefined) {\n                                        if (\"payload\" in action && action.payload && \"name\" in action.payload && action.payload.name) {\n                                            const { name, params } = action.payload;\n                                            if (navigateOptions?.experimental?.isNestedNavigator && params && \"screen\" in params && params.screen) {\n                                                navigation?.dispatch(_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.StackActions.replace(params.screen, params.params));\n                                            } else {\n                                                navigation?.dispatch(_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.StackActions.replace(name, params));\n                                            }\n                                        } else {\n                                            navigation?.dispatch(action);\n                                        }\n                                    } else {\n                                        navigation?.reset(state);\n                                    }\n                                }\n                            } else {\n                                // fallback in case the linking context didn't work\n                                console.warn(`[solito] replace(\"${to}\") faced an issue. You should still see your new screen, but it probably didn't replace the previous one. This may be due to a breaking change in React Navigation. \n  Please open an issue at https://github.com/nandorojo/solito and report how this happened. Thanks!`);\n                                linkTo(to);\n                            }\n                        } else {\n                            linkTo(to);\n                        }\n                    }\n                }\n            },\n            back: ()=>{\n                if (react_native__WEBPACK_IMPORTED_MODULE_5__.Platform.OS === \"web\") {\n                    nextRouter?.back();\n                } else {\n                    navigation?.goBack();\n                }\n            },\n            parseNextPath: _router_parse_next_path__WEBPACK_IMPORTED_MODULE_6__.parseNextPath\n        }), [\n        linkTo,\n        navigation\n    ]);\n} //# sourceMappingURL=use-router.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/app/navigation/use-router.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/router/parse-next-path.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/solito/build/router/parse-next-path.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseNextPath: () => (/* binding */ parseNextPath)\n/* harmony export */ });\nconst parseNextPath = (from)=>{\n    let path = (typeof from == \"string\" ? from : from.pathname) || \"\";\n    // replace each instance of [key] with the corresponding value from query[key]\n    // this ensures we're navigating to the correct URL\n    // it currently ignores [[...param]]\n    // but I can't see why you would use this with RN + Next.js\n    if (typeof from == \"object\" && from.query && typeof from.query == \"object\") {\n        const query = {\n            ...from.query\n        };\n        // replace dynamic routes\n        // and [...param] syntax\n        for(const key in query){\n            if (path.includes(`[${key}]`)) {\n                path = path.replace(`[${key}]`, `${query[key] ?? \"\"}`);\n                delete query[key];\n            } else if (path.includes(`[...${key}]`)) {\n                const values = query[key];\n                if (Array.isArray(values)) {\n                    path = path.replace(`[...${key}]`, values.join(\"/\"));\n                    delete query[key];\n                }\n            }\n        }\n        if (Object.keys(query).length) {\n            // add query param separator\n            path += \"?\";\n            for(const key in query){\n                const value = query[key];\n                if (Array.isArray(value)) {\n                    value.forEach((item)=>{\n                        path += `${key}=${item}&`;\n                    });\n                } else if (value != null) {\n                    path += `${key}=${value}&`;\n                }\n            }\n            if (path.endsWith(\"&\") || path.endsWith(\"?\")) {\n                path = path.slice(0, -1);\n            }\n        }\n    }\n    return path;\n};\n //# sourceMappingURL=parse-next-path.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/router/parse-next-path.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/router/replace-helpers.web.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/solito/build/router/replace-helpers.web.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LinkingContext: () => (/* binding */ LinkingContext),\n/* harmony export */   StackActions: () => (/* binding */ StackActions),\n/* harmony export */   getActionFromState: () => (/* binding */ getActionFromState),\n/* harmony export */   getStateFromPath: () => (/* binding */ getStateFromPath)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst LinkingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n    options: undefined\n});\nlet StackActions, getStateFromPath, getActionFromState;\n //# sourceMappingURL=replace-helpers.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9yb3V0ZXIvcmVwbGFjZS1oZWxwZXJzLndlYi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBcUM7QUFFckMsTUFBTUMsK0JBQWlCRCxvREFBYUEsQ0FBQztJQUNuQ0UsU0FBU0M7O0FBR1gsSUFBSUMsY0FBY0Msa0JBQWtCQztBQUV5QyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL3NyYy9yb3V0ZXIvcmVwbGFjZS1oZWxwZXJzLndlYi50cz80ZjA4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUNvbnRleHQgfSBmcm9tICdyZWFjdCdcblxuY29uc3QgTGlua2luZ0NvbnRleHQgPSBjcmVhdGVDb250ZXh0KHtcbiAgb3B0aW9uczogdW5kZWZpbmVkLFxufSlcblxubGV0IFN0YWNrQWN0aW9ucywgZ2V0U3RhdGVGcm9tUGF0aCwgZ2V0QWN0aW9uRnJvbVN0YXRlXG5cbmV4cG9ydCB7IExpbmtpbmdDb250ZXh0LCBTdGFja0FjdGlvbnMsIGdldFN0YXRlRnJvbVBhdGgsIGdldEFjdGlvbkZyb21TdGF0ZSB9XG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsIkxpbmtpbmdDb250ZXh0Iiwib3B0aW9ucyIsInVuZGVmaW5lZCIsIlN0YWNrQWN0aW9ucyIsImdldFN0YXRlRnJvbVBhdGgiLCJnZXRBY3Rpb25Gcm9tU3RhdGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/router/replace-helpers.web.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/router/use-link-to.web.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/solito/build/router/use-link-to.web.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLinkTo: () => (/* binding */ useLinkTo)\n/* harmony export */ });\nconst noOp = ()=>{\n    throw new Error(\"[use-link-to] is not supported on the web. Something went wrong if you called this.\");\n};\n/**\n * @deprecated imported from the wrong file. Use `use-link-to` instead.\n */ const useLinkTo = ()=>noOp; //# sourceMappingURL=use-link-to.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9yb3V0ZXIvdXNlLWxpbmstdG8ud2ViLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxNQUFNQSxPQUFPO0lBQ1gsTUFBTSxJQUFJQyxNQUNSO0FBRUo7QUFFQTs7SUFHTyxNQUFNQyxZQUFZLElBQU1GLEtBQUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9zcmMvcm91dGVyL3VzZS1saW5rLXRvLndlYi50cz8wMTkwIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG5vT3AgPSAoKSA9PiB7XG4gIHRocm93IG5ldyBFcnJvcihcbiAgICAnW3VzZS1saW5rLXRvXSBpcyBub3Qgc3VwcG9ydGVkIG9uIHRoZSB3ZWIuIFNvbWV0aGluZyB3ZW50IHdyb25nIGlmIHlvdSBjYWxsZWQgdGhpcy4nXG4gIClcbn1cblxuLyoqXG4gKiBAZGVwcmVjYXRlZCBpbXBvcnRlZCBmcm9tIHRoZSB3cm9uZyBmaWxlLiBVc2UgYHVzZS1saW5rLXRvYCBpbnN0ZWFkLlxuICovXG5leHBvcnQgY29uc3QgdXNlTGlua1RvID0gKCkgPT4gbm9PcFxuIl0sIm5hbWVzIjpbIm5vT3AiLCJFcnJvciIsInVzZUxpbmtUbyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/router/use-link-to.web.js\n");

/***/ }),

/***/ "../../node_modules/solito/build/router/use-navigation.web.js":
/*!********************************************************************!*\
  !*** ../../node_modules/solito/build/router/use-navigation.web.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useNavigation: () => (/* binding */ useNavigation)\n/* harmony export */ });\nconst useNavigation = ()=>undefined; //# sourceMappingURL=use-navigation.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9yb3V0ZXIvdXNlLW5hdmlnYXRpb24ud2ViLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxNQUFNQSxnQkFBZ0IsSUFBTUMsVUFBUyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL3NyYy9yb3V0ZXIvdXNlLW5hdmlnYXRpb24ud2ViLnRzP2E4NjciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHVzZU5hdmlnYXRpb24gPSAoKSA9PiB1bmRlZmluZWRcbiJdLCJuYW1lcyI6WyJ1c2VOYXZpZ2F0aW9uIiwidW5kZWZpbmVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/solito/build/router/use-navigation.web.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/solito/build/app/navigation/use-link.js":
/*!******************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/use-link.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLink: () => (/* binding */ useLink)\n/* harmony export */ });\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-native */ \"(ssr)/../../node_modules/react-native-web/dist/exports/Platform/index.js\");\n/* harmony import */ var _use_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-router */ \"(ssr)/../../node_modules/solito/build/app/navigation/use-router.js\");\n\n\nfunction useLink({ href, replace, experimental }) {\n    const router = (0,_use_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    // https://github.com/react-navigation/react-navigation/blob/main/packages/native/src/useLinkProps.tsx#L64\n    const onPress = (e)=>{\n        let shouldHandle = false;\n        if (react_native__WEBPACK_IMPORTED_MODULE_1__[\"default\"].OS !== \"web\" || !e) {\n            shouldHandle = e ? !e.defaultPrevented : true;\n        } else if (!e.defaultPrevented && // onPress prevented default\n        // @ts-expect-error: these properties exist on web, but not in React Native\n        !(e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) && // ignore clicks with modifier keys\n        // @ts-expect-error: these properties exist on web, but not in React Native\n        (e.button == null || e.button === 0) && // ignore everything but left clicks\n        // @ts-expect-error: these properties exist on web, but not in React Native\n        [\n            undefined,\n            null,\n            \"\",\n            \"self\"\n        ].includes(e.currentTarget?.target) // let browser handle \"target=_blank\" etc.\n        ) {\n            e.preventDefault();\n            shouldHandle = true;\n        }\n        if (shouldHandle) {\n            if (href === \"#\") {\n                // this is a way on web to stay on the same page\n                // useful for conditional hrefs\n                return;\n            }\n            if (replace) {\n                router.replace(href, {\n                    experimental\n                });\n            } else {\n                router.push(href);\n            }\n        }\n    };\n    return {\n        accessibilityRole: \"link\",\n        onPress,\n        href\n    };\n} //# sourceMappingURL=use-link.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/solito/build/app/navigation/use-link.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/solito/build/app/navigation/use-router.js":
/*!********************************************************************!*\
  !*** ../../node_modules/solito/build/app/navigation/use-router.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRouter: () => (/* binding */ useRouter)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-native */ \"(ssr)/../../node_modules/react-native-web/dist/exports/Platform/index.js\");\n/* harmony import */ var _router_parse_next_path__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../router/parse-next-path */ \"(ssr)/../../node_modules/solito/build/router/parse-next-path.js\");\n/* harmony import */ var _router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../router/replace-helpers */ \"(ssr)/../../node_modules/solito/build/router/replace-helpers.web.js\");\n/* harmony import */ var _router_use_link_to__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../router/use-link-to */ \"(ssr)/../../node_modules/solito/build/router/use-link-to.web.js\");\n/* harmony import */ var _router_use_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../router/use-navigation */ \"(ssr)/../../node_modules/solito/build/router/use-navigation.web.js\");\n/* harmony import */ var _use_next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-next-router */ \"(ssr)/../../node_modules/next/dist/api/navigation.js\");\n\n\n\n\n\n\n\nfunction useRouter() {\n    const linkTo = (0,_router_use_link_to__WEBPACK_IMPORTED_MODULE_1__.useLinkTo)();\n    const navigation = (0,_router_use_navigation__WEBPACK_IMPORTED_MODULE_2__.useNavigation)();\n    const nextRouter = (0,_use_next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const linking = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.LinkingContext);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({\n            push: (url, navigateOptions)=>{\n                if (react_native__WEBPACK_IMPORTED_MODULE_5__[\"default\"].OS === \"web\") {\n                    nextRouter?.push(url, navigateOptions);\n                } else {\n                    const to = (0,_router_parse_next_path__WEBPACK_IMPORTED_MODULE_6__.parseNextPath)(url);\n                    if (to) {\n                        linkTo(to);\n                    }\n                }\n            },\n            replace: (url, navigateOptions)=>{\n                if (react_native__WEBPACK_IMPORTED_MODULE_5__[\"default\"].OS === \"web\") {\n                    nextRouter?.replace(url, navigateOptions);\n                } else {\n                    const to = (0,_router_parse_next_path__WEBPACK_IMPORTED_MODULE_6__.parseNextPath)(url);\n                    if (to) {\n                        if (navigateOptions?.experimental?.nativeBehavior === \"stack-replace\") {\n                            if (linking?.options) {\n                                // custom logic to create a replace() from a URL on native\n                                // https://github.com/react-navigation/react-navigation/discussions/10517\n                                const { options } = linking;\n                                const state = options?.getStateFromPath ? options.getStateFromPath(to, options.config) : (0,_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.getStateFromPath)(to, options?.config);\n                                if (state) {\n                                    const action = (0,_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.getActionFromState)(state, options?.config);\n                                    if (action !== undefined) {\n                                        if (\"payload\" in action && action.payload && \"name\" in action.payload && action.payload.name) {\n                                            const { name, params } = action.payload;\n                                            if (navigateOptions?.experimental?.isNestedNavigator && params && \"screen\" in params && params.screen) {\n                                                navigation?.dispatch(_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.StackActions.replace(params.screen, params.params));\n                                            } else {\n                                                navigation?.dispatch(_router_replace_helpers__WEBPACK_IMPORTED_MODULE_4__.StackActions.replace(name, params));\n                                            }\n                                        } else {\n                                            navigation?.dispatch(action);\n                                        }\n                                    } else {\n                                        navigation?.reset(state);\n                                    }\n                                }\n                            } else {\n                                // fallback in case the linking context didn't work\n                                console.warn(`[solito] replace(\"${to}\") faced an issue. You should still see your new screen, but it probably didn't replace the previous one. This may be due to a breaking change in React Navigation. \n  Please open an issue at https://github.com/nandorojo/solito and report how this happened. Thanks!`);\n                                linkTo(to);\n                            }\n                        } else {\n                            linkTo(to);\n                        }\n                    }\n                }\n            },\n            back: ()=>{\n                if (react_native__WEBPACK_IMPORTED_MODULE_5__[\"default\"].OS === \"web\") {\n                    nextRouter?.back();\n                } else {\n                    navigation?.goBack();\n                }\n            },\n            parseNextPath: _router_parse_next_path__WEBPACK_IMPORTED_MODULE_6__.parseNextPath\n        }), [\n        linkTo,\n        navigation\n    ]);\n} //# sourceMappingURL=use-router.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/solito/build/app/navigation/use-router.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/solito/build/router/parse-next-path.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/solito/build/router/parse-next-path.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseNextPath: () => (/* binding */ parseNextPath)\n/* harmony export */ });\nconst parseNextPath = (from)=>{\n    let path = (typeof from == \"string\" ? from : from.pathname) || \"\";\n    // replace each instance of [key] with the corresponding value from query[key]\n    // this ensures we're navigating to the correct URL\n    // it currently ignores [[...param]]\n    // but I can't see why you would use this with RN + Next.js\n    if (typeof from == \"object\" && from.query && typeof from.query == \"object\") {\n        const query = {\n            ...from.query\n        };\n        // replace dynamic routes\n        // and [...param] syntax\n        for(const key in query){\n            if (path.includes(`[${key}]`)) {\n                path = path.replace(`[${key}]`, `${query[key] ?? \"\"}`);\n                delete query[key];\n            } else if (path.includes(`[...${key}]`)) {\n                const values = query[key];\n                if (Array.isArray(values)) {\n                    path = path.replace(`[...${key}]`, values.join(\"/\"));\n                    delete query[key];\n                }\n            }\n        }\n        if (Object.keys(query).length) {\n            // add query param separator\n            path += \"?\";\n            for(const key in query){\n                const value = query[key];\n                if (Array.isArray(value)) {\n                    value.forEach((item)=>{\n                        path += `${key}=${item}&`;\n                    });\n                } else if (value != null) {\n                    path += `${key}=${value}&`;\n                }\n            }\n            if (path.endsWith(\"&\") || path.endsWith(\"?\")) {\n                path = path.slice(0, -1);\n            }\n        }\n    }\n    return path;\n};\n //# sourceMappingURL=parse-next-path.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/solito/build/router/parse-next-path.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/solito/build/router/replace-helpers.web.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/solito/build/router/replace-helpers.web.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LinkingContext: () => (/* binding */ LinkingContext),\n/* harmony export */   StackActions: () => (/* binding */ StackActions),\n/* harmony export */   getActionFromState: () => (/* binding */ getActionFromState),\n/* harmony export */   getStateFromPath: () => (/* binding */ getStateFromPath)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst LinkingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n    options: undefined\n});\nlet StackActions, getStateFromPath, getActionFromState;\n //# sourceMappingURL=replace-helpers.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9yb3V0ZXIvcmVwbGFjZS1oZWxwZXJzLndlYi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBcUM7QUFFckMsTUFBTUMsK0JBQWlCRCxvREFBYUEsQ0FBQztJQUNuQ0UsU0FBU0M7O0FBR1gsSUFBSUMsY0FBY0Msa0JBQWtCQztBQUV5QyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL3NyYy9yb3V0ZXIvcmVwbGFjZS1oZWxwZXJzLndlYi50cz80ZjA4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUNvbnRleHQgfSBmcm9tICdyZWFjdCdcblxuY29uc3QgTGlua2luZ0NvbnRleHQgPSBjcmVhdGVDb250ZXh0KHtcbiAgb3B0aW9uczogdW5kZWZpbmVkLFxufSlcblxubGV0IFN0YWNrQWN0aW9ucywgZ2V0U3RhdGVGcm9tUGF0aCwgZ2V0QWN0aW9uRnJvbVN0YXRlXG5cbmV4cG9ydCB7IExpbmtpbmdDb250ZXh0LCBTdGFja0FjdGlvbnMsIGdldFN0YXRlRnJvbVBhdGgsIGdldEFjdGlvbkZyb21TdGF0ZSB9XG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsIkxpbmtpbmdDb250ZXh0Iiwib3B0aW9ucyIsInVuZGVmaW5lZCIsIlN0YWNrQWN0aW9ucyIsImdldFN0YXRlRnJvbVBhdGgiLCJnZXRBY3Rpb25Gcm9tU3RhdGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/solito/build/router/replace-helpers.web.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/solito/build/router/use-link-to.web.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/solito/build/router/use-link-to.web.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLinkTo: () => (/* binding */ useLinkTo)\n/* harmony export */ });\nconst noOp = ()=>{\n    throw new Error(\"[use-link-to] is not supported on the web. Something went wrong if you called this.\");\n};\n/**\n * @deprecated imported from the wrong file. Use `use-link-to` instead.\n */ const useLinkTo = ()=>noOp; //# sourceMappingURL=use-link-to.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9yb3V0ZXIvdXNlLWxpbmstdG8ud2ViLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxNQUFNQSxPQUFPO0lBQ1gsTUFBTSxJQUFJQyxNQUNSO0FBRUo7QUFFQTs7SUFHTyxNQUFNQyxZQUFZLElBQU1GLEtBQUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9zcmMvcm91dGVyL3VzZS1saW5rLXRvLndlYi50cz8wMTkwIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG5vT3AgPSAoKSA9PiB7XG4gIHRocm93IG5ldyBFcnJvcihcbiAgICAnW3VzZS1saW5rLXRvXSBpcyBub3Qgc3VwcG9ydGVkIG9uIHRoZSB3ZWIuIFNvbWV0aGluZyB3ZW50IHdyb25nIGlmIHlvdSBjYWxsZWQgdGhpcy4nXG4gIClcbn1cblxuLyoqXG4gKiBAZGVwcmVjYXRlZCBpbXBvcnRlZCBmcm9tIHRoZSB3cm9uZyBmaWxlLiBVc2UgYHVzZS1saW5rLXRvYCBpbnN0ZWFkLlxuICovXG5leHBvcnQgY29uc3QgdXNlTGlua1RvID0gKCkgPT4gbm9PcFxuIl0sIm5hbWVzIjpbIm5vT3AiLCJFcnJvciIsInVzZUxpbmtUbyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/solito/build/router/use-link-to.web.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/solito/build/router/use-navigation.web.js":
/*!********************************************************************!*\
  !*** ../../node_modules/solito/build/router/use-navigation.web.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useNavigation: () => (/* binding */ useNavigation)\n/* harmony export */ });\nconst useNavigation = ()=>undefined; //# sourceMappingURL=use-navigation.web.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3NvbGl0by9idWlsZC9yb3V0ZXIvdXNlLW5hdmlnYXRpb24ud2ViLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxNQUFNQSxnQkFBZ0IsSUFBTUMsVUFBUyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL3NyYy9yb3V0ZXIvdXNlLW5hdmlnYXRpb24ud2ViLnRzP2E4NjciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHVzZU5hdmlnYXRpb24gPSAoKSA9PiB1bmRlZmluZWRcbiJdLCJuYW1lcyI6WyJ1c2VOYXZpZ2F0aW9uIiwidW5kZWZpbmVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/solito/build/router/use-navigation.web.js\n");

/***/ })

};
;