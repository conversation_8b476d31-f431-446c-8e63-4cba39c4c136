"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/styleq";
exports.ids = ["vendor-chunks/styleq"];
exports.modules = {

/***/ "(ssr)/../../node_modules/styleq/dist/styleq.js":
/*!************************************************!*\
  !*** ../../node_modules/styleq/dist/styleq.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/**\n * Copyright (c) Nicolas Gallagher\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.styleq = void 0;\nvar cache = new WeakMap();\nvar compiledKey = '$$css';\n\nfunction createStyleq(options) {\n  var disableCache;\n  var disableMix;\n  var transform;\n\n  if (options != null) {\n    disableCache = options.disableCache === true;\n    disableMix = options.disableMix === true;\n    transform = options.transform;\n  }\n\n  return function styleq() {\n    // Keep track of property commits to the className\n    var definedProperties = []; // The className and inline style to build up\n\n    var className = '';\n    var inlineStyle = null; // The current position in the cache graph\n\n    var nextCache = disableCache ? null : cache; // This way of creating an array from arguments is fastest\n\n    var styles = new Array(arguments.length);\n\n    for (var i = 0; i < arguments.length; i++) {\n      styles[i] = arguments[i];\n    } // Iterate over styles from last to first\n\n\n    while (styles.length > 0) {\n      var possibleStyle = styles.pop(); // Skip empty items\n\n      if (possibleStyle == null || possibleStyle === false) {\n        continue;\n      } // Push nested styles back onto the stack to be processed\n\n\n      if (Array.isArray(possibleStyle)) {\n        for (var _i = 0; _i < possibleStyle.length; _i++) {\n          styles.push(possibleStyle[_i]);\n        }\n\n        continue;\n      } // Process an individual style object\n\n\n      var style = transform != null ? transform(possibleStyle) : possibleStyle;\n\n      if (style.$$css) {\n        // Build up the class names defined by this object\n        var classNameChunk = ''; // Check the cache to see if we've already done this work\n\n        if (nextCache != null && nextCache.has(style)) {\n          // Cache: read\n          var cacheEntry = nextCache.get(style);\n\n          if (cacheEntry != null) {\n            classNameChunk = cacheEntry[0]; // $FlowIgnore\n\n            definedProperties.push.apply(definedProperties, cacheEntry[1]);\n            nextCache = cacheEntry[2];\n          }\n        } // Update the chunks with data from this object\n        else {\n          // The properties defined by this object\n          var definedPropertiesChunk = [];\n\n          for (var prop in style) {\n            var value = style[prop];\n            if (prop === compiledKey) continue; // Each property value is used as an HTML class name\n            // { 'debug.string': 'debug.string', opacity: 's-jskmnoqp' }\n\n            if (typeof value === 'string' || value === null) {\n              // Only add to chunks if this property hasn't already been seen\n              if (!definedProperties.includes(prop)) {\n                definedProperties.push(prop);\n\n                if (nextCache != null) {\n                  definedPropertiesChunk.push(prop);\n                }\n\n                if (typeof value === 'string') {\n                  classNameChunk += classNameChunk ? ' ' + value : value;\n                }\n              }\n            } // If we encounter a value that isn't a string or `null`\n            else {\n              console.error(\"styleq: \".concat(prop, \" typeof \").concat(String(value), \" is not \\\"string\\\" or \\\"null\\\".\"));\n            }\n          } // Cache: write\n\n\n          if (nextCache != null) {\n            // Create the next WeakMap for this sequence of styles\n            var weakMap = new WeakMap();\n            nextCache.set(style, [classNameChunk, definedPropertiesChunk, weakMap]);\n            nextCache = weakMap;\n          }\n        } // Order of classes in chunks matches property-iteration order of style\n        // object. Order of chunks matches passed order of styles from first to\n        // last (which we iterate over in reverse).\n\n\n        if (classNameChunk) {\n          className = className ? classNameChunk + ' ' + className : classNameChunk;\n        }\n      } // ----- DYNAMIC: Process inline style object -----\n      else {\n        if (disableMix) {\n          if (inlineStyle == null) {\n            inlineStyle = {};\n          }\n\n          inlineStyle = Object.assign({}, style, inlineStyle);\n        } else {\n          var subStyle = null;\n\n          for (var _prop in style) {\n            var _value = style[_prop];\n\n            if (_value !== undefined) {\n              if (!definedProperties.includes(_prop)) {\n                if (_value != null) {\n                  if (inlineStyle == null) {\n                    inlineStyle = {};\n                  }\n\n                  if (subStyle == null) {\n                    subStyle = {};\n                  }\n\n                  subStyle[_prop] = _value;\n                }\n\n                definedProperties.push(_prop); // Cache is unnecessary overhead if results can't be reused.\n\n                nextCache = null;\n              }\n            }\n          }\n\n          if (subStyle != null) {\n            inlineStyle = Object.assign(subStyle, inlineStyle);\n          }\n        }\n      }\n    }\n\n    var styleProps = [className, inlineStyle];\n    return styleProps;\n  };\n}\n\nvar styleq = createStyleq();\nexports.styleq = styleq;\nstyleq.factory = createStyleq;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/styleq/dist/styleq.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/styleq/dist/transform-localize-style.js":
/*!******************************************************************!*\
  !*** ../../node_modules/styleq/dist/transform-localize-style.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/**\n * Copyright (c) Nicolas Gallagher\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.localizeStyle = localizeStyle;\nvar cache = new WeakMap();\nvar markerProp = '$$css$localize';\n/**\n * The compiler polyfills logical properties and values, generating a class\n * name for both writing directions. The style objects are annotated by\n * the compiler as needing this runtime transform. The results are memoized.\n *\n * { '$$css$localize': true, float: [ 'float-left', 'float-right' ] }\n * => { float: 'float-left' }\n */\n\nfunction compileStyle(style, isRTL) {\n  // Create a new compiled style for styleq\n  var compiledStyle = {};\n\n  for (var prop in style) {\n    if (prop !== markerProp) {\n      var value = style[prop];\n\n      if (Array.isArray(value)) {\n        compiledStyle[prop] = isRTL ? value[1] : value[0];\n      } else {\n        compiledStyle[prop] = value;\n      }\n    }\n  }\n\n  return compiledStyle;\n}\n\nfunction localizeStyle(style, isRTL) {\n  if (style[markerProp] != null) {\n    var compiledStyleIndex = isRTL ? 1 : 0; // Check the cache in case we've already seen this object\n\n    if (cache.has(style)) {\n      var _cachedStyles = cache.get(style);\n\n      var _compiledStyle = _cachedStyles[compiledStyleIndex];\n\n      if (_compiledStyle == null) {\n        // Update the missing cache entry\n        _compiledStyle = compileStyle(style, isRTL);\n        _cachedStyles[compiledStyleIndex] = _compiledStyle;\n        cache.set(style, _cachedStyles);\n      }\n\n      return _compiledStyle;\n    } // Create a new compiled style for styleq\n\n\n    var compiledStyle = compileStyle(style, isRTL);\n    var cachedStyles = new Array(2);\n    cachedStyles[compiledStyleIndex] = compiledStyle;\n    cache.set(style, cachedStyles);\n    return compiledStyle;\n  }\n\n  return style;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/styleq/dist/transform-localize-style.js\n");

/***/ })

};
;