/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/postcss-value-parser";
exports.ids = ["vendor-chunks/postcss-value-parser"];
exports.modules = {

/***/ "(ssr)/../../node_modules/postcss-value-parser/lib/index.js":
/*!************************************************************!*\
  !*** ../../node_modules/postcss-value-parser/lib/index.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var parse = __webpack_require__(/*! ./parse */ \"(ssr)/../../node_modules/postcss-value-parser/lib/parse.js\");\nvar walk = __webpack_require__(/*! ./walk */ \"(ssr)/../../node_modules/postcss-value-parser/lib/walk.js\");\nvar stringify = __webpack_require__(/*! ./stringify */ \"(ssr)/../../node_modules/postcss-value-parser/lib/stringify.js\");\n\nfunction ValueParser(value) {\n  if (this instanceof ValueParser) {\n    this.nodes = parse(value);\n    return this;\n  }\n  return new ValueParser(value);\n}\n\nValueParser.prototype.toString = function() {\n  return Array.isArray(this.nodes) ? stringify(this.nodes) : \"\";\n};\n\nValueParser.prototype.walk = function(cb, bubble) {\n  walk(this.nodes, cb, bubble);\n  return this;\n};\n\nValueParser.unit = __webpack_require__(/*! ./unit */ \"(ssr)/../../node_modules/postcss-value-parser/lib/unit.js\");\n\nValueParser.walk = walk;\n\nValueParser.stringify = stringify;\n\nmodule.exports = ValueParser;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3Bvc3Rjc3MtdmFsdWUtcGFyc2VyL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxZQUFZLG1CQUFPLENBQUMsMkVBQVM7QUFDN0IsV0FBVyxtQkFBTyxDQUFDLHlFQUFRO0FBQzNCLGdCQUFnQixtQkFBTyxDQUFDLG1GQUFhOztBQUVyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsbUJBQW1CLG1CQUFPLENBQUMseUVBQVE7O0FBRW5DOztBQUVBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vbm9kZV9tb2R1bGVzL3Bvc3Rjc3MtdmFsdWUtcGFyc2VyL2xpYi9pbmRleC5qcz81OTQzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBwYXJzZSA9IHJlcXVpcmUoXCIuL3BhcnNlXCIpO1xudmFyIHdhbGsgPSByZXF1aXJlKFwiLi93YWxrXCIpO1xudmFyIHN0cmluZ2lmeSA9IHJlcXVpcmUoXCIuL3N0cmluZ2lmeVwiKTtcblxuZnVuY3Rpb24gVmFsdWVQYXJzZXIodmFsdWUpIHtcbiAgaWYgKHRoaXMgaW5zdGFuY2VvZiBWYWx1ZVBhcnNlcikge1xuICAgIHRoaXMubm9kZXMgPSBwYXJzZSh2YWx1ZSk7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cbiAgcmV0dXJuIG5ldyBWYWx1ZVBhcnNlcih2YWx1ZSk7XG59XG5cblZhbHVlUGFyc2VyLnByb3RvdHlwZS50b1N0cmluZyA9IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gQXJyYXkuaXNBcnJheSh0aGlzLm5vZGVzKSA/IHN0cmluZ2lmeSh0aGlzLm5vZGVzKSA6IFwiXCI7XG59O1xuXG5WYWx1ZVBhcnNlci5wcm90b3R5cGUud2FsayA9IGZ1bmN0aW9uKGNiLCBidWJibGUpIHtcbiAgd2Fsayh0aGlzLm5vZGVzLCBjYiwgYnViYmxlKTtcbiAgcmV0dXJuIHRoaXM7XG59O1xuXG5WYWx1ZVBhcnNlci51bml0ID0gcmVxdWlyZShcIi4vdW5pdFwiKTtcblxuVmFsdWVQYXJzZXIud2FsayA9IHdhbGs7XG5cblZhbHVlUGFyc2VyLnN0cmluZ2lmeSA9IHN0cmluZ2lmeTtcblxubW9kdWxlLmV4cG9ydHMgPSBWYWx1ZVBhcnNlcjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/postcss-value-parser/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/postcss-value-parser/lib/parse.js":
/*!************************************************************!*\
  !*** ../../node_modules/postcss-value-parser/lib/parse.js ***!
  \************************************************************/
/***/ ((module) => {

eval("var openParentheses = \"(\".charCodeAt(0);\nvar closeParentheses = \")\".charCodeAt(0);\nvar singleQuote = \"'\".charCodeAt(0);\nvar doubleQuote = '\"'.charCodeAt(0);\nvar backslash = \"\\\\\".charCodeAt(0);\nvar slash = \"/\".charCodeAt(0);\nvar comma = \",\".charCodeAt(0);\nvar colon = \":\".charCodeAt(0);\nvar star = \"*\".charCodeAt(0);\nvar uLower = \"u\".charCodeAt(0);\nvar uUpper = \"U\".charCodeAt(0);\nvar plus = \"+\".charCodeAt(0);\nvar isUnicodeRange = /^[a-f0-9?-]+$/i;\n\nmodule.exports = function(input) {\n  var tokens = [];\n  var value = input;\n\n  var next,\n    quote,\n    prev,\n    token,\n    escape,\n    escapePos,\n    whitespacePos,\n    parenthesesOpenPos;\n  var pos = 0;\n  var code = value.charCodeAt(pos);\n  var max = value.length;\n  var stack = [{ nodes: tokens }];\n  var balanced = 0;\n  var parent;\n\n  var name = \"\";\n  var before = \"\";\n  var after = \"\";\n\n  while (pos < max) {\n    // Whitespaces\n    if (code <= 32) {\n      next = pos;\n      do {\n        next += 1;\n        code = value.charCodeAt(next);\n      } while (code <= 32);\n      token = value.slice(pos, next);\n\n      prev = tokens[tokens.length - 1];\n      if (code === closeParentheses && balanced) {\n        after = token;\n      } else if (prev && prev.type === \"div\") {\n        prev.after = token;\n        prev.sourceEndIndex += token.length;\n      } else if (\n        code === comma ||\n        code === colon ||\n        (code === slash &&\n          value.charCodeAt(next + 1) !== star &&\n          (!parent ||\n            (parent && parent.type === \"function\" && parent.value !== \"calc\")))\n      ) {\n        before = token;\n      } else {\n        tokens.push({\n          type: \"space\",\n          sourceIndex: pos,\n          sourceEndIndex: next,\n          value: token\n        });\n      }\n\n      pos = next;\n\n      // Quotes\n    } else if (code === singleQuote || code === doubleQuote) {\n      next = pos;\n      quote = code === singleQuote ? \"'\" : '\"';\n      token = {\n        type: \"string\",\n        sourceIndex: pos,\n        quote: quote\n      };\n      do {\n        escape = false;\n        next = value.indexOf(quote, next + 1);\n        if (~next) {\n          escapePos = next;\n          while (value.charCodeAt(escapePos - 1) === backslash) {\n            escapePos -= 1;\n            escape = !escape;\n          }\n        } else {\n          value += quote;\n          next = value.length - 1;\n          token.unclosed = true;\n        }\n      } while (escape);\n      token.value = value.slice(pos + 1, next);\n      token.sourceEndIndex = token.unclosed ? next : next + 1;\n      tokens.push(token);\n      pos = next + 1;\n      code = value.charCodeAt(pos);\n\n      // Comments\n    } else if (code === slash && value.charCodeAt(pos + 1) === star) {\n      next = value.indexOf(\"*/\", pos);\n\n      token = {\n        type: \"comment\",\n        sourceIndex: pos,\n        sourceEndIndex: next + 2\n      };\n\n      if (next === -1) {\n        token.unclosed = true;\n        next = value.length;\n        token.sourceEndIndex = next;\n      }\n\n      token.value = value.slice(pos + 2, next);\n      tokens.push(token);\n\n      pos = next + 2;\n      code = value.charCodeAt(pos);\n\n      // Operation within calc\n    } else if (\n      (code === slash || code === star) &&\n      parent &&\n      parent.type === \"function\" &&\n      parent.value === \"calc\"\n    ) {\n      token = value[pos];\n      tokens.push({\n        type: \"word\",\n        sourceIndex: pos - before.length,\n        sourceEndIndex: pos + token.length,\n        value: token\n      });\n      pos += 1;\n      code = value.charCodeAt(pos);\n\n      // Dividers\n    } else if (code === slash || code === comma || code === colon) {\n      token = value[pos];\n\n      tokens.push({\n        type: \"div\",\n        sourceIndex: pos - before.length,\n        sourceEndIndex: pos + token.length,\n        value: token,\n        before: before,\n        after: \"\"\n      });\n      before = \"\";\n\n      pos += 1;\n      code = value.charCodeAt(pos);\n\n      // Open parentheses\n    } else if (openParentheses === code) {\n      // Whitespaces after open parentheses\n      next = pos;\n      do {\n        next += 1;\n        code = value.charCodeAt(next);\n      } while (code <= 32);\n      parenthesesOpenPos = pos;\n      token = {\n        type: \"function\",\n        sourceIndex: pos - name.length,\n        value: name,\n        before: value.slice(parenthesesOpenPos + 1, next)\n      };\n      pos = next;\n\n      if (name === \"url\" && code !== singleQuote && code !== doubleQuote) {\n        next -= 1;\n        do {\n          escape = false;\n          next = value.indexOf(\")\", next + 1);\n          if (~next) {\n            escapePos = next;\n            while (value.charCodeAt(escapePos - 1) === backslash) {\n              escapePos -= 1;\n              escape = !escape;\n            }\n          } else {\n            value += \")\";\n            next = value.length - 1;\n            token.unclosed = true;\n          }\n        } while (escape);\n        // Whitespaces before closed\n        whitespacePos = next;\n        do {\n          whitespacePos -= 1;\n          code = value.charCodeAt(whitespacePos);\n        } while (code <= 32);\n        if (parenthesesOpenPos < whitespacePos) {\n          if (pos !== whitespacePos + 1) {\n            token.nodes = [\n              {\n                type: \"word\",\n                sourceIndex: pos,\n                sourceEndIndex: whitespacePos + 1,\n                value: value.slice(pos, whitespacePos + 1)\n              }\n            ];\n          } else {\n            token.nodes = [];\n          }\n          if (token.unclosed && whitespacePos + 1 !== next) {\n            token.after = \"\";\n            token.nodes.push({\n              type: \"space\",\n              sourceIndex: whitespacePos + 1,\n              sourceEndIndex: next,\n              value: value.slice(whitespacePos + 1, next)\n            });\n          } else {\n            token.after = value.slice(whitespacePos + 1, next);\n            token.sourceEndIndex = next;\n          }\n        } else {\n          token.after = \"\";\n          token.nodes = [];\n        }\n        pos = next + 1;\n        token.sourceEndIndex = token.unclosed ? next : pos;\n        code = value.charCodeAt(pos);\n        tokens.push(token);\n      } else {\n        balanced += 1;\n        token.after = \"\";\n        token.sourceEndIndex = pos + 1;\n        tokens.push(token);\n        stack.push(token);\n        tokens = token.nodes = [];\n        parent = token;\n      }\n      name = \"\";\n\n      // Close parentheses\n    } else if (closeParentheses === code && balanced) {\n      pos += 1;\n      code = value.charCodeAt(pos);\n\n      parent.after = after;\n      parent.sourceEndIndex += after.length;\n      after = \"\";\n      balanced -= 1;\n      stack[stack.length - 1].sourceEndIndex = pos;\n      stack.pop();\n      parent = stack[balanced];\n      tokens = parent.nodes;\n\n      // Words\n    } else {\n      next = pos;\n      do {\n        if (code === backslash) {\n          next += 1;\n        }\n        next += 1;\n        code = value.charCodeAt(next);\n      } while (\n        next < max &&\n        !(\n          code <= 32 ||\n          code === singleQuote ||\n          code === doubleQuote ||\n          code === comma ||\n          code === colon ||\n          code === slash ||\n          code === openParentheses ||\n          (code === star &&\n            parent &&\n            parent.type === \"function\" &&\n            parent.value === \"calc\") ||\n          (code === slash &&\n            parent.type === \"function\" &&\n            parent.value === \"calc\") ||\n          (code === closeParentheses && balanced)\n        )\n      );\n      token = value.slice(pos, next);\n\n      if (openParentheses === code) {\n        name = token;\n      } else if (\n        (uLower === token.charCodeAt(0) || uUpper === token.charCodeAt(0)) &&\n        plus === token.charCodeAt(1) &&\n        isUnicodeRange.test(token.slice(2))\n      ) {\n        tokens.push({\n          type: \"unicode-range\",\n          sourceIndex: pos,\n          sourceEndIndex: next,\n          value: token\n        });\n      } else {\n        tokens.push({\n          type: \"word\",\n          sourceIndex: pos,\n          sourceEndIndex: next,\n          value: token\n        });\n      }\n\n      pos = next;\n    }\n  }\n\n  for (pos = stack.length - 1; pos; pos -= 1) {\n    stack[pos].unclosed = true;\n    stack[pos].sourceEndIndex = value.length;\n  }\n\n  return stack[0].nodes;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/postcss-value-parser/lib/parse.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/postcss-value-parser/lib/stringify.js":
/*!****************************************************************!*\
  !*** ../../node_modules/postcss-value-parser/lib/stringify.js ***!
  \****************************************************************/
/***/ ((module) => {

eval("function stringifyNode(node, custom) {\n  var type = node.type;\n  var value = node.value;\n  var buf;\n  var customResult;\n\n  if (custom && (customResult = custom(node)) !== undefined) {\n    return customResult;\n  } else if (type === \"word\" || type === \"space\") {\n    return value;\n  } else if (type === \"string\") {\n    buf = node.quote || \"\";\n    return buf + value + (node.unclosed ? \"\" : buf);\n  } else if (type === \"comment\") {\n    return \"/*\" + value + (node.unclosed ? \"\" : \"*/\");\n  } else if (type === \"div\") {\n    return (node.before || \"\") + value + (node.after || \"\");\n  } else if (Array.isArray(node.nodes)) {\n    buf = stringify(node.nodes, custom);\n    if (type !== \"function\") {\n      return buf;\n    }\n    return (\n      value +\n      \"(\" +\n      (node.before || \"\") +\n      buf +\n      (node.after || \"\") +\n      (node.unclosed ? \"\" : \")\")\n    );\n  }\n  return value;\n}\n\nfunction stringify(nodes, custom) {\n  var result, i;\n\n  if (Array.isArray(nodes)) {\n    result = \"\";\n    for (i = nodes.length - 1; ~i; i -= 1) {\n      result = stringifyNode(nodes[i], custom) + result;\n    }\n    return result;\n  }\n  return stringifyNode(nodes, custom);\n}\n\nmodule.exports = stringify;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/postcss-value-parser/lib/stringify.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/postcss-value-parser/lib/unit.js":
/*!***********************************************************!*\
  !*** ../../node_modules/postcss-value-parser/lib/unit.js ***!
  \***********************************************************/
/***/ ((module) => {

eval("var minus = \"-\".charCodeAt(0);\nvar plus = \"+\".charCodeAt(0);\nvar dot = \".\".charCodeAt(0);\nvar exp = \"e\".charCodeAt(0);\nvar EXP = \"E\".charCodeAt(0);\n\n// Check if three code points would start a number\n// https://www.w3.org/TR/css-syntax-3/#starts-with-a-number\nfunction likeNumber(value) {\n  var code = value.charCodeAt(0);\n  var nextCode;\n\n  if (code === plus || code === minus) {\n    nextCode = value.charCodeAt(1);\n\n    if (nextCode >= 48 && nextCode <= 57) {\n      return true;\n    }\n\n    var nextNextCode = value.charCodeAt(2);\n\n    if (nextCode === dot && nextNextCode >= 48 && nextNextCode <= 57) {\n      return true;\n    }\n\n    return false;\n  }\n\n  if (code === dot) {\n    nextCode = value.charCodeAt(1);\n\n    if (nextCode >= 48 && nextCode <= 57) {\n      return true;\n    }\n\n    return false;\n  }\n\n  if (code >= 48 && code <= 57) {\n    return true;\n  }\n\n  return false;\n}\n\n// Consume a number\n// https://www.w3.org/TR/css-syntax-3/#consume-number\nmodule.exports = function(value) {\n  var pos = 0;\n  var length = value.length;\n  var code;\n  var nextCode;\n  var nextNextCode;\n\n  if (length === 0 || !likeNumber(value)) {\n    return false;\n  }\n\n  code = value.charCodeAt(pos);\n\n  if (code === plus || code === minus) {\n    pos++;\n  }\n\n  while (pos < length) {\n    code = value.charCodeAt(pos);\n\n    if (code < 48 || code > 57) {\n      break;\n    }\n\n    pos += 1;\n  }\n\n  code = value.charCodeAt(pos);\n  nextCode = value.charCodeAt(pos + 1);\n\n  if (code === dot && nextCode >= 48 && nextCode <= 57) {\n    pos += 2;\n\n    while (pos < length) {\n      code = value.charCodeAt(pos);\n\n      if (code < 48 || code > 57) {\n        break;\n      }\n\n      pos += 1;\n    }\n  }\n\n  code = value.charCodeAt(pos);\n  nextCode = value.charCodeAt(pos + 1);\n  nextNextCode = value.charCodeAt(pos + 2);\n\n  if (\n    (code === exp || code === EXP) &&\n    ((nextCode >= 48 && nextCode <= 57) ||\n      ((nextCode === plus || nextCode === minus) &&\n        nextNextCode >= 48 &&\n        nextNextCode <= 57))\n  ) {\n    pos += nextCode === plus || nextCode === minus ? 3 : 2;\n\n    while (pos < length) {\n      code = value.charCodeAt(pos);\n\n      if (code < 48 || code > 57) {\n        break;\n      }\n\n      pos += 1;\n    }\n  }\n\n  return {\n    number: value.slice(0, pos),\n    unit: value.slice(pos)\n  };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/postcss-value-parser/lib/unit.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/postcss-value-parser/lib/walk.js":
/*!***********************************************************!*\
  !*** ../../node_modules/postcss-value-parser/lib/walk.js ***!
  \***********************************************************/
/***/ ((module) => {

eval("module.exports = function walk(nodes, cb, bubble) {\n  var i, max, node, result;\n\n  for (i = 0, max = nodes.length; i < max; i += 1) {\n    node = nodes[i];\n    if (!bubble) {\n      result = cb(node, i, nodes);\n    }\n\n    if (\n      result !== false &&\n      node.type === \"function\" &&\n      Array.isArray(node.nodes)\n    ) {\n      walk(node.nodes, cb, bubble);\n    }\n\n    if (bubble) {\n      cb(node, i, nodes);\n    }\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3Bvc3Rjc3MtdmFsdWUtcGFyc2VyL2xpYi93YWxrLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7O0FBRUEsa0NBQWtDLFNBQVM7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL25vZGVfbW9kdWxlcy9wb3N0Y3NzLXZhbHVlLXBhcnNlci9saWIvd2Fsay5qcz9kZDlkIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gd2Fsayhub2RlcywgY2IsIGJ1YmJsZSkge1xuICB2YXIgaSwgbWF4LCBub2RlLCByZXN1bHQ7XG5cbiAgZm9yIChpID0gMCwgbWF4ID0gbm9kZXMubGVuZ3RoOyBpIDwgbWF4OyBpICs9IDEpIHtcbiAgICBub2RlID0gbm9kZXNbaV07XG4gICAgaWYgKCFidWJibGUpIHtcbiAgICAgIHJlc3VsdCA9IGNiKG5vZGUsIGksIG5vZGVzKTtcbiAgICB9XG5cbiAgICBpZiAoXG4gICAgICByZXN1bHQgIT09IGZhbHNlICYmXG4gICAgICBub2RlLnR5cGUgPT09IFwiZnVuY3Rpb25cIiAmJlxuICAgICAgQXJyYXkuaXNBcnJheShub2RlLm5vZGVzKVxuICAgICkge1xuICAgICAgd2Fsayhub2RlLm5vZGVzLCBjYiwgYnViYmxlKTtcbiAgICB9XG5cbiAgICBpZiAoYnViYmxlKSB7XG4gICAgICBjYihub2RlLCBpLCBub2Rlcyk7XG4gICAgfVxuICB9XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/postcss-value-parser/lib/walk.js\n");

/***/ })

};
;