"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/color2k";
exports.ids = ["vendor-chunks/color2k"];
exports.modules = {

/***/ "(ssr)/../../node_modules/color2k/dist/index.exports.import.es.mjs":
/*!*******************************************************************!*\
  !*** ../../node_modules/color2k/dist/index.exports.import.es.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColorError: () => (/* binding */ ColorError$1),\n/* harmony export */   adjustHue: () => (/* binding */ adjustHue),\n/* harmony export */   darken: () => (/* binding */ darken),\n/* harmony export */   desaturate: () => (/* binding */ desaturate),\n/* harmony export */   getContrast: () => (/* binding */ getContrast),\n/* harmony export */   getLuminance: () => (/* binding */ getLuminance),\n/* harmony export */   getScale: () => (/* binding */ getScale),\n/* harmony export */   guard: () => (/* binding */ guard),\n/* harmony export */   hasBadContrast: () => (/* binding */ hasBadContrast),\n/* harmony export */   hsla: () => (/* binding */ hsla),\n/* harmony export */   lighten: () => (/* binding */ lighten),\n/* harmony export */   mix: () => (/* binding */ mix),\n/* harmony export */   opacify: () => (/* binding */ opacify),\n/* harmony export */   parseToHsla: () => (/* binding */ parseToHsla),\n/* harmony export */   parseToRgba: () => (/* binding */ parseToRgba),\n/* harmony export */   readableColor: () => (/* binding */ readableColor),\n/* harmony export */   readableColorIsBlack: () => (/* binding */ readableColorIsBlack),\n/* harmony export */   rgba: () => (/* binding */ rgba),\n/* harmony export */   saturate: () => (/* binding */ saturate),\n/* harmony export */   toHex: () => (/* binding */ toHex),\n/* harmony export */   toHsla: () => (/* binding */ toHsla),\n/* harmony export */   toRgba: () => (/* binding */ toRgba),\n/* harmony export */   transparentize: () => (/* binding */ transparentize)\n/* harmony export */ });\n/**\n * A simple guard function:\n *\n * ```js\n * Math.min(Math.max(low, value), high)\n * ```\n */\nfunction guard(low, high, value) {\n  return Math.min(Math.max(low, value), high);\n}\n\nclass ColorError extends Error {\n  constructor(color) {\n    super(`Failed to parse color: \"${color}\"`);\n  }\n}\nvar ColorError$1 = ColorError;\n\n/**\n * Parses a color into red, gree, blue, alpha parts\n *\n * @param color the input color. Can be a RGB, RBGA, HSL, HSLA, or named color\n */\nfunction parseToRgba(color) {\n  if (typeof color !== 'string') throw new ColorError$1(color);\n  if (color.trim().toLowerCase() === 'transparent') return [0, 0, 0, 0];\n  let normalizedColor = color.trim();\n  normalizedColor = namedColorRegex.test(color) ? nameToHex(color) : color;\n  const reducedHexMatch = reducedHexRegex.exec(normalizedColor);\n  if (reducedHexMatch) {\n    const arr = Array.from(reducedHexMatch).slice(1);\n    return [...arr.slice(0, 3).map(x => parseInt(r(x, 2), 16)), parseInt(r(arr[3] || 'f', 2), 16) / 255];\n  }\n  const hexMatch = hexRegex.exec(normalizedColor);\n  if (hexMatch) {\n    const arr = Array.from(hexMatch).slice(1);\n    return [...arr.slice(0, 3).map(x => parseInt(x, 16)), parseInt(arr[3] || 'ff', 16) / 255];\n  }\n  const rgbaMatch = rgbaRegex.exec(normalizedColor);\n  if (rgbaMatch) {\n    const arr = Array.from(rgbaMatch).slice(1);\n    return [...arr.slice(0, 3).map(x => parseInt(x, 10)), parseFloat(arr[3] || '1')];\n  }\n  const hslaMatch = hslaRegex.exec(normalizedColor);\n  if (hslaMatch) {\n    const [h, s, l, a] = Array.from(hslaMatch).slice(1).map(parseFloat);\n    if (guard(0, 100, s) !== s) throw new ColorError$1(color);\n    if (guard(0, 100, l) !== l) throw new ColorError$1(color);\n    return [...hslToRgb(h, s, l), Number.isNaN(a) ? 1 : a];\n  }\n  throw new ColorError$1(color);\n}\nfunction hash(str) {\n  let hash = 5381;\n  let i = str.length;\n  while (i) {\n    hash = hash * 33 ^ str.charCodeAt(--i);\n  }\n\n  /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */\n  return (hash >>> 0) % 2341;\n}\nconst colorToInt = x => parseInt(x.replace(/_/g, ''), 36);\nconst compressedColorMap = '1q29ehhb 1n09sgk7 1kl1ekf_ _yl4zsno 16z9eiv3 1p29lhp8 _bd9zg04 17u0____ _iw9zhe5 _to73___ _r45e31e _7l6g016 _jh8ouiv _zn3qba8 1jy4zshs 11u87k0u 1ro9yvyo 1aj3xael 1gz9zjz0 _3w8l4xo 1bf1ekf_ _ke3v___ _4rrkb__ 13j776yz _646mbhl _nrjr4__ _le6mbhl 1n37ehkb _m75f91n _qj3bzfz 1939yygw 11i5z6x8 _1k5f8xs 1509441m 15t5lwgf _ae2th1n _tg1ugcv 1lp1ugcv 16e14up_ _h55rw7n _ny9yavn _7a11xb_ 1ih442g9 _pv442g9 1mv16xof 14e6y7tu 1oo9zkds 17d1cisi _4v9y70f _y98m8kc 1019pq0v 12o9zda8 _348j4f4 1et50i2o _8epa8__ _ts6senj 1o350i2o 1mi9eiuo 1259yrp0 1ln80gnw _632xcoy 1cn9zldc _f29edu4 1n490c8q _9f9ziet 1b94vk74 _m49zkct 1kz6s73a 1eu9dtog _q58s1rz 1dy9sjiq __u89jo3 _aj5nkwg _ld89jo3 13h9z6wx _qa9z2ii _l119xgq _bs5arju 1hj4nwk9 1qt4nwk9 1ge6wau6 14j9zlcw 11p1edc_ _ms1zcxe _439shk6 _jt9y70f _754zsow 1la40eju _oq5p___ _x279qkz 1fa5r3rv _yd2d9ip _424tcku _8y1di2_ _zi2uabw _yy7rn9h 12yz980_ __39ljp6 1b59zg0x _n39zfzp 1fy9zest _b33k___ _hp9wq92 1il50hz4 _io472ub _lj9z3eo 19z9ykg0 _8t8iu3a 12b9bl4a 1ak5yw0o _896v4ku _tb8k8lv _s59zi6t _c09ze0p 1lg80oqn 1id9z8wb _238nba5 1kq6wgdi _154zssg _tn3zk49 _da9y6tc 1sg7cv4f _r12jvtt 1gq5fmkz 1cs9rvci _lp9jn1c _xw1tdnb 13f9zje6 16f6973h _vo7ir40 _bt5arjf _rc45e4t _hr4e100 10v4e100 _hc9zke2 _w91egv_ _sj2r1kk 13c87yx8 _vqpds__ _ni8ggk8 _tj9yqfb 1ia2j4r4 _7x9b10u 1fc9ld4j 1eq9zldr _5j9lhpx _ez9zl6o _md61fzm'.split(' ').reduce((acc, next) => {\n  const key = colorToInt(next.substring(0, 3));\n  const hex = colorToInt(next.substring(3)).toString(16);\n\n  // NOTE: padStart could be used here but it breaks Node 6 compat\n  // https://github.com/ricokahler/color2k/issues/351\n  let prefix = '';\n  for (let i = 0; i < 6 - hex.length; i++) {\n    prefix += '0';\n  }\n  acc[key] = `${prefix}${hex}`;\n  return acc;\n}, {});\n\n/**\n * Checks if a string is a CSS named color and returns its equivalent hex value, otherwise returns the original color.\n */\nfunction nameToHex(color) {\n  const normalizedColorName = color.toLowerCase().trim();\n  const result = compressedColorMap[hash(normalizedColorName)];\n  if (!result) throw new ColorError$1(color);\n  return `#${result}`;\n}\nconst r = (str, amount) => Array.from(Array(amount)).map(() => str).join('');\nconst reducedHexRegex = new RegExp(`^#${r('([a-f0-9])', 3)}([a-f0-9])?$`, 'i');\nconst hexRegex = new RegExp(`^#${r('([a-f0-9]{2})', 3)}([a-f0-9]{2})?$`, 'i');\nconst rgbaRegex = new RegExp(`^rgba?\\\\(\\\\s*(\\\\d+)\\\\s*${r(',\\\\s*(\\\\d+)\\\\s*', 2)}(?:,\\\\s*([\\\\d.]+))?\\\\s*\\\\)$`, 'i');\nconst hslaRegex = /^hsla?\\(\\s*([\\d.]+)\\s*,\\s*([\\d.]+)%\\s*,\\s*([\\d.]+)%(?:\\s*,\\s*([\\d.]+))?\\s*\\)$/i;\nconst namedColorRegex = /^[a-z]+$/i;\nconst roundColor = color => {\n  return Math.round(color * 255);\n};\nconst hslToRgb = (hue, saturation, lightness) => {\n  let l = lightness / 100;\n  if (saturation === 0) {\n    // achromatic\n    return [l, l, l].map(roundColor);\n  }\n\n  // formulae from https://en.wikipedia.org/wiki/HSL_and_HSV\n  const huePrime = (hue % 360 + 360) % 360 / 60;\n  const chroma = (1 - Math.abs(2 * l - 1)) * (saturation / 100);\n  const secondComponent = chroma * (1 - Math.abs(huePrime % 2 - 1));\n  let red = 0;\n  let green = 0;\n  let blue = 0;\n  if (huePrime >= 0 && huePrime < 1) {\n    red = chroma;\n    green = secondComponent;\n  } else if (huePrime >= 1 && huePrime < 2) {\n    red = secondComponent;\n    green = chroma;\n  } else if (huePrime >= 2 && huePrime < 3) {\n    green = chroma;\n    blue = secondComponent;\n  } else if (huePrime >= 3 && huePrime < 4) {\n    green = secondComponent;\n    blue = chroma;\n  } else if (huePrime >= 4 && huePrime < 5) {\n    red = secondComponent;\n    blue = chroma;\n  } else if (huePrime >= 5 && huePrime < 6) {\n    red = chroma;\n    blue = secondComponent;\n  }\n  const lightnessModification = l - chroma / 2;\n  const finalRed = red + lightnessModification;\n  const finalGreen = green + lightnessModification;\n  const finalBlue = blue + lightnessModification;\n  return [finalRed, finalGreen, finalBlue].map(roundColor);\n};\n\n// taken from:\n// https://github.com/styled-components/polished/blob/a23a6a2bb26802b3d922d9c3b67bac3f3a54a310/src/internalHelpers/_rgbToHsl.js\n\n/**\n * Parses a color in hue, saturation, lightness, and the alpha channel.\n *\n * Hue is a number between 0 and 360, saturation, lightness, and alpha are\n * decimal percentages between 0 and 1\n */\nfunction parseToHsla(color) {\n  const [red, green, blue, alpha] = parseToRgba(color).map((value, index) =>\n  // 3rd index is alpha channel which is already normalized\n  index === 3 ? value : value / 255);\n  const max = Math.max(red, green, blue);\n  const min = Math.min(red, green, blue);\n  const lightness = (max + min) / 2;\n\n  // achromatic\n  if (max === min) return [0, 0, lightness, alpha];\n  const delta = max - min;\n  const saturation = lightness > 0.5 ? delta / (2 - max - min) : delta / (max + min);\n  const hue = 60 * (red === max ? (green - blue) / delta + (green < blue ? 6 : 0) : green === max ? (blue - red) / delta + 2 : (red - green) / delta + 4);\n  return [hue, saturation, lightness, alpha];\n}\n\n/**\n * Takes in hsla parts and constructs an hsla string\n *\n * @param hue The color circle (from 0 to 360) - 0 (or 360) is red, 120 is green, 240 is blue\n * @param saturation Percentage of saturation, given as a decimal between 0 and 1\n * @param lightness Percentage of lightness, given as a decimal between 0 and 1\n * @param alpha Percentage of opacity, given as a decimal between 0 and 1\n */\nfunction hsla(hue, saturation, lightness, alpha) {\n  return `hsla(${(hue % 360).toFixed()}, ${guard(0, 100, saturation * 100).toFixed()}%, ${guard(0, 100, lightness * 100).toFixed()}%, ${parseFloat(guard(0, 1, alpha).toFixed(3))})`;\n}\n\n/**\n * Adjusts the current hue of the color by the given degrees. Wraps around when\n * over 360.\n *\n * @param color input color\n * @param degrees degrees to adjust the input color, accepts degree integers\n * (0 - 360) and wraps around on overflow\n */\nfunction adjustHue(color, degrees) {\n  const [h, s, l, a] = parseToHsla(color);\n  return hsla(h + degrees, s, l, a);\n}\n\n/**\n * Darkens using lightness. This is equivalent to subtracting the lightness\n * from the L in HSL.\n *\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction darken(color, amount) {\n  const [hue, saturation, lightness, alpha] = parseToHsla(color);\n  return hsla(hue, saturation, lightness - amount, alpha);\n}\n\n/**\n * Desaturates the input color by the given amount via subtracting from the `s`\n * in `hsla`.\n *\n * @param amount The amount to desaturate, given as a decimal between 0 and 1\n */\nfunction desaturate(color, amount) {\n  const [h, s, l, a] = parseToHsla(color);\n  return hsla(h, s - amount, l, a);\n}\n\n// taken from:\n// https://github.com/styled-components/polished/blob/0764c982551b487469043acb56281b0358b3107b/src/color/getLuminance.js\n\n/**\n * Returns a number (float) representing the luminance of a color.\n */\nfunction getLuminance(color) {\n  if (color === 'transparent') return 0;\n  function f(x) {\n    const channel = x / 255;\n    return channel <= 0.04045 ? channel / 12.92 : Math.pow((channel + 0.055) / 1.055, 2.4);\n  }\n  const [r, g, b] = parseToRgba(color);\n  return 0.2126 * f(r) + 0.7152 * f(g) + 0.0722 * f(b);\n}\n\n// taken from:\n// https://github.com/styled-components/polished/blob/0764c982551b487469043acb56281b0358b3107b/src/color/getContrast.js\n\n/**\n * Returns the contrast ratio between two colors based on\n * [W3's recommended equation for calculating contrast](http://www.w3.org/TR/WCAG20/#contrast-ratiodef).\n */\nfunction getContrast(color1, color2) {\n  const luminance1 = getLuminance(color1);\n  const luminance2 = getLuminance(color2);\n  return luminance1 > luminance2 ? (luminance1 + 0.05) / (luminance2 + 0.05) : (luminance2 + 0.05) / (luminance1 + 0.05);\n}\n\n/**\n * Takes in rgba parts and returns an rgba string\n *\n * @param red The amount of red in the red channel, given in a number between 0 and 255 inclusive\n * @param green The amount of green in the red channel, given in a number between 0 and 255 inclusive\n * @param blue The amount of blue in the red channel, given in a number between 0 and 255 inclusive\n * @param alpha Percentage of opacity, given as a decimal between 0 and 1\n */\nfunction rgba(red, green, blue, alpha) {\n  return `rgba(${guard(0, 255, red).toFixed()}, ${guard(0, 255, green).toFixed()}, ${guard(0, 255, blue).toFixed()}, ${parseFloat(guard(0, 1, alpha).toFixed(3))})`;\n}\n\n/**\n * Mixes two colors together. Taken from sass's implementation.\n */\nfunction mix(color1, color2, weight) {\n  const normalize = (n, index) =>\n  // 3rd index is alpha channel which is already normalized\n  index === 3 ? n : n / 255;\n  const [r1, g1, b1, a1] = parseToRgba(color1).map(normalize);\n  const [r2, g2, b2, a2] = parseToRgba(color2).map(normalize);\n\n  // The formula is copied from the original Sass implementation:\n  // http://sass-lang.com/documentation/Sass/Script/Functions.html#mix-instance_method\n  const alphaDelta = a2 - a1;\n  const normalizedWeight = weight * 2 - 1;\n  const combinedWeight = normalizedWeight * alphaDelta === -1 ? normalizedWeight : normalizedWeight + alphaDelta / (1 + normalizedWeight * alphaDelta);\n  const weight2 = (combinedWeight + 1) / 2;\n  const weight1 = 1 - weight2;\n  const r = (r1 * weight1 + r2 * weight2) * 255;\n  const g = (g1 * weight1 + g2 * weight2) * 255;\n  const b = (b1 * weight1 + b2 * weight2) * 255;\n  const a = a2 * weight + a1 * (1 - weight);\n  return rgba(r, g, b, a);\n}\n\n/**\n * Given a series colors, this function will return a `scale(x)` function that\n * accepts a percentage as a decimal between 0 and 1 and returns the color at\n * that percentage in the scale.\n *\n * ```js\n * const scale = getScale('red', 'yellow', 'green');\n * console.log(scale(0)); // rgba(255, 0, 0, 1)\n * console.log(scale(0.5)); // rgba(255, 255, 0, 1)\n * console.log(scale(1)); // rgba(0, 128, 0, 1)\n * ```\n *\n * If you'd like to limit the domain and range like chroma-js, we recommend\n * wrapping scale again.\n *\n * ```js\n * const _scale = getScale('red', 'yellow', 'green');\n * const scale = x => _scale(x / 100);\n *\n * console.log(scale(0)); // rgba(255, 0, 0, 1)\n * console.log(scale(50)); // rgba(255, 255, 0, 1)\n * console.log(scale(100)); // rgba(0, 128, 0, 1)\n * ```\n */\nfunction getScale(...colors) {\n  return n => {\n    const lastIndex = colors.length - 1;\n    const lowIndex = guard(0, lastIndex, Math.floor(n * lastIndex));\n    const highIndex = guard(0, lastIndex, Math.ceil(n * lastIndex));\n    const color1 = colors[lowIndex];\n    const color2 = colors[highIndex];\n    const unit = 1 / lastIndex;\n    const weight = (n - unit * lowIndex) / unit;\n    return mix(color1, color2, weight);\n  };\n}\n\nconst guidelines = {\n  decorative: 1.5,\n  readable: 3,\n  aa: 4.5,\n  aaa: 7\n};\n\n/**\n * Returns whether or not a color has bad contrast against a background\n * according to a given standard.\n */\nfunction hasBadContrast(color, standard = 'aa', background = '#fff') {\n  return getContrast(color, background) < guidelines[standard];\n}\n\n/**\n * Lightens a color by a given amount. This is equivalent to\n * `darken(color, -amount)`\n *\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction lighten(color, amount) {\n  return darken(color, -amount);\n}\n\n/**\n * Takes in a color and makes it more transparent by convert to `rgba` and\n * decreasing the amount in the alpha channel.\n *\n * @param amount The amount to increase the transparency by, given as a decimal between 0 and 1\n */\nfunction transparentize(color, amount) {\n  const [r, g, b, a] = parseToRgba(color);\n  return rgba(r, g, b, a - amount);\n}\n\n/**\n * Takes a color and un-transparentizes it. Equivalent to\n * `transparentize(color, -amount)`\n *\n * @param amount The amount to increase the opacity by, given as a decimal between 0 and 1\n */\nfunction opacify(color, amount) {\n  return transparentize(color, -amount);\n}\n\n/**\n * An alternative function to `readableColor`. Returns whether or not the \n * readable color (i.e. the color to be place on top the input color) should be\n * black.\n */\nfunction readableColorIsBlack(color) {\n  return getLuminance(color) > 0.179;\n}\n\n/**\n * Returns black or white for best contrast depending on the luminosity of the\n * given color.\n */\nfunction readableColor(color) {\n  return readableColorIsBlack(color) ? '#000' : '#fff';\n}\n\n/**\n * Saturates a color by converting it to `hsl` and increasing the saturation\n * amount. Equivalent to `desaturate(color, -amount)`\n * \n * @param color Input color\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction saturate(color, amount) {\n  return desaturate(color, -amount);\n}\n\n/**\n * Takes in any color and returns it as a hex code.\n */\nfunction toHex(color) {\n  const [r, g, b, a] = parseToRgba(color);\n  let hex = x => {\n    const h = guard(0, 255, x).toString(16);\n    // NOTE: padStart could be used here but it breaks Node 6 compat\n    // https://github.com/ricokahler/color2k/issues/351\n    return h.length === 1 ? `0${h}` : h;\n  };\n  return `#${hex(r)}${hex(g)}${hex(b)}${a < 1 ? hex(Math.round(a * 255)) : ''}`;\n}\n\n/**\n * Takes in any color and returns it as an rgba string.\n */\nfunction toRgba(color) {\n  return rgba(...parseToRgba(color));\n}\n\n/**\n * Takes in any color and returns it as an hsla string.\n */\nfunction toHsla(color) {\n  return hsla(...parseToHsla(color));\n}\n\n\n//# sourceMappingURL=index.exports.import.es.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/color2k/dist/index.exports.import.es.mjs\n");

/***/ })

};
;