"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tamagui";
exports.ids = ["vendor-chunks/tamagui"];
exports.modules = {

/***/ "(ssr)/../../node_modules/tamagui/dist/esm/createTamagui.mjs":
/*!*************************************************************!*\
  !*** ../../node_modules/tamagui/dist/esm/createTamagui.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTamagui: () => (/* binding */ createTamagui)\n/* harmony export */ });\n/* harmony import */ var _tamagui_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tamagui/core */ \"(ssr)/../../node_modules/@tamagui/core/dist/cjs/index.cjs\");\n\nconst createTamagui =  false ? 0 : conf => {\n  const sizeTokenKeys = [\"$true\"],\n    hasKeys = (expectedKeys, obj) => expectedKeys.every(k => typeof obj[k] < \"u\"),\n    tamaguiConfig = (0,_tamagui_core__WEBPACK_IMPORTED_MODULE_0__.createTamagui)(conf);\n  for (const name of [\"size\", \"space\"]) {\n    const tokenSet = tamaguiConfig.tokensParsed[name];\n    if (!tokenSet) throw new Error(`Expected tokens for \"${name}\" in ${Object.keys(tamaguiConfig.tokensParsed).join(\", \")}`);\n    if (!hasKeys(sizeTokenKeys, tokenSet)) throw new Error(`\ncreateTamagui() missing expected tokens.${name}:\n\nReceived: ${Object.keys(tokenSet).join(\", \")}\n\nExpected: ${sizeTokenKeys.join(\", \")}\n\nTamagui expects a \"true\" key that is the same value as your default size. This is so \nit can size things up or down from the defaults without assuming which keys you use.\n\nPlease define a \"true\" or \"$true\" key on your size and space tokens like so (example):\n\nsize: {\n  sm: 2,\n  md: 10,\n  true: 10, // this means \"md\" is your default size\n  lg: 20,\n}\n\n`);\n  }\n  const expected = Object.keys(tamaguiConfig.tokensParsed.size);\n  for (const name of [\"radius\", \"zIndex\"]) {\n    const tokenSet = tamaguiConfig.tokensParsed[name],\n      received = Object.keys(tokenSet);\n    if (!received.some(rk => expected.includes(rk))) throw new Error(`\ncreateTamagui() invalid tokens.${name}:\n\nReceived: ${received.join(\", \")}\n\nExpected a subset of: ${expected.join(\", \")}\n\n`);\n  }\n  return tamaguiConfig;\n};\n\n//# sourceMappingURL=createTamagui.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/tamagui/dist/esm/createTamagui.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/tamagui/dist/esm/views/Anchor.mjs":
/*!************************************************************!*\
  !*** ../../node_modules/tamagui/dist/esm/views/Anchor.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Anchor: () => (/* binding */ Anchor)\n/* harmony export */ });\n/* harmony import */ var _tamagui_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tamagui/constants */ \"(ssr)/../../node_modules/@tamagui/constants/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tamagui/core */ \"(ssr)/../../node_modules/@tamagui/core/dist/cjs/index.cjs\");\n/* harmony import */ var _tamagui_text__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tamagui/text */ \"(ssr)/../../node_modules/@tamagui/text/dist/esm/SizableText.mjs\");\n/* harmony import */ var react_native_web__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-native-web */ \"(ssr)/../../node_modules/react-native-web/dist/exports/Linking/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n\n\nconst AnchorFrame = (0,_tamagui_core__WEBPACK_IMPORTED_MODULE_2__.styled)(_tamagui_text__WEBPACK_IMPORTED_MODULE_3__.SizableText, {\n    name: \"Anchor\",\n    tag: \"a\",\n    accessibilityRole: \"link\"\n  }),\n  Anchor = AnchorFrame.styleable(({\n    href,\n    target,\n    ...props\n  }, ref) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AnchorFrame, {\n    ...props,\n    ...(_tamagui_constants__WEBPACK_IMPORTED_MODULE_0__.isWeb ? {\n      href,\n      target\n    } : {\n      onPress: event => {\n        props.onPress?.(event), href !== void 0 && react_native_web__WEBPACK_IMPORTED_MODULE_4__[\"default\"].openURL(href);\n      }\n    }),\n    ref\n  }));\n\n//# sourceMappingURL=Anchor.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3RhbWFndWkvZGlzdC9lc20vdmlld3MvQW5jaG9yLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMkM7QUFDSjtBQUNLO0FBQ0Q7QUFDSDtBQUN4QyxvQkFBb0IscURBQU0sQ0FBQyxzREFBVztBQUN0QztBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHLHlCQUF5QixzREFBRztBQUMvQjtBQUNBLFFBQVEscURBQUs7QUFDYjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsbURBQW1ELHdEQUFPO0FBQzFEO0FBQ0EsS0FBSztBQUNMO0FBQ0EsR0FBRztBQUNlO0FBQ2xCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vbm9kZV9tb2R1bGVzL3RhbWFndWkvZGlzdC9lc20vdmlld3MvQW5jaG9yLm1qcz9lY2MwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzV2ViIH0gZnJvbSBcIkB0YW1hZ3VpL2NvbnN0YW50c1wiO1xuaW1wb3J0IHsgc3R5bGVkIH0gZnJvbSBcIkB0YW1hZ3VpL2NvcmVcIjtcbmltcG9ydCB7IFNpemFibGVUZXh0IH0gZnJvbSBcIkB0YW1hZ3VpL3RleHRcIjtcbmltcG9ydCB7IExpbmtpbmcgfSBmcm9tIFwicmVhY3QtbmF0aXZlLXdlYlwiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5jb25zdCBBbmNob3JGcmFtZSA9IHN0eWxlZChTaXphYmxlVGV4dCwge1xuICAgIG5hbWU6IFwiQW5jaG9yXCIsXG4gICAgdGFnOiBcImFcIixcbiAgICBhY2Nlc3NpYmlsaXR5Um9sZTogXCJsaW5rXCJcbiAgfSksXG4gIEFuY2hvciA9IEFuY2hvckZyYW1lLnN0eWxlYWJsZSgoe1xuICAgIGhyZWYsXG4gICAgdGFyZ2V0LFxuICAgIC4uLnByb3BzXG4gIH0sIHJlZikgPT4gLyogQF9fUFVSRV9fICovanN4KEFuY2hvckZyYW1lLCB7XG4gICAgLi4ucHJvcHMsXG4gICAgLi4uKGlzV2ViID8ge1xuICAgICAgaHJlZixcbiAgICAgIHRhcmdldFxuICAgIH0gOiB7XG4gICAgICBvblByZXNzOiBldmVudCA9PiB7XG4gICAgICAgIHByb3BzLm9uUHJlc3M/LihldmVudCksIGhyZWYgIT09IHZvaWQgMCAmJiBMaW5raW5nLm9wZW5VUkwoaHJlZik7XG4gICAgICB9XG4gICAgfSksXG4gICAgcmVmXG4gIH0pKTtcbmV4cG9ydCB7IEFuY2hvciB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9QW5jaG9yLm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/tamagui/dist/esm/views/Anchor.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/tamagui/dist/esm/views/TamaguiProvider.mjs":
/*!*********************************************************************!*\
  !*** ../../node_modules/tamagui/dist/esm/views/TamaguiProvider.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TamaguiProvider: () => (/* binding */ TamaguiProvider)\n/* harmony export */ });\n/* harmony import */ var _tamagui_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tamagui/core */ \"(ssr)/../../node_modules/@tamagui/core/dist/cjs/index.cjs\");\n/* harmony import */ var _tamagui_portal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tamagui/portal */ \"(ssr)/../../node_modules/@tamagui/portal/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_z_index_stack__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tamagui/z-index-stack */ \"(ssr)/../../node_modules/@tamagui/z-index-stack/dist/esm/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n\nconst TamaguiProvider = ({\n  children,\n  ...props\n}) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tamagui_core__WEBPACK_IMPORTED_MODULE_3__.TamaguiProvider, {\n  ...props,\n  children: /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tamagui_z_index_stack__WEBPACK_IMPORTED_MODULE_1__.ZIndexStackContext.Provider, {\n    value: 1,\n    children: /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tamagui_portal__WEBPACK_IMPORTED_MODULE_0__.PortalProvider, {\n      shouldAddRootHost: !0,\n      children\n    })\n  })\n});\n\n//# sourceMappingURL=TamaguiProvider.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3RhbWFndWkvZGlzdC9lc20vdmlld3MvVGFtYWd1aVByb3ZpZGVyLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUE4RDtBQUNiO0FBQ1c7QUFDcEI7QUFDeEM7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxvQkFBb0Isc0RBQUcsQ0FBQywwREFBVTtBQUNuQztBQUNBLDJCQUEyQixzREFBRyxDQUFDLHNFQUFrQjtBQUNqRDtBQUNBLDZCQUE2QixzREFBRyxDQUFDLDJEQUFjO0FBQy9DO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNILENBQUM7QUFDMEI7QUFDM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9ub2RlX21vZHVsZXMvdGFtYWd1aS9kaXN0L2VzbS92aWV3cy9UYW1hZ3VpUHJvdmlkZXIubWpzP2UzMDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVGFtYWd1aVByb3ZpZGVyIGFzIE9HUHJvdmlkZXIgfSBmcm9tIFwiQHRhbWFndWkvY29yZVwiO1xuaW1wb3J0IHsgUG9ydGFsUHJvdmlkZXIgfSBmcm9tIFwiQHRhbWFndWkvcG9ydGFsXCI7XG5pbXBvcnQgeyBaSW5kZXhTdGFja0NvbnRleHQgfSBmcm9tIFwiQHRhbWFndWkvei1pbmRleC1zdGFja1wiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5jb25zdCBUYW1hZ3VpUHJvdmlkZXIgPSAoe1xuICBjaGlsZHJlbixcbiAgLi4ucHJvcHNcbn0pID0+IC8qIEBfX1BVUkVfXyAqL2pzeChPR1Byb3ZpZGVyLCB7XG4gIC4uLnByb3BzLFxuICBjaGlsZHJlbjogLyogQF9fUFVSRV9fICovanN4KFpJbmRleFN0YWNrQ29udGV4dC5Qcm92aWRlciwge1xuICAgIHZhbHVlOiAxLFxuICAgIGNoaWxkcmVuOiAvKiBAX19QVVJFX18gKi9qc3goUG9ydGFsUHJvdmlkZXIsIHtcbiAgICAgIHNob3VsZEFkZFJvb3RIb3N0OiAhMCxcbiAgICAgIGNoaWxkcmVuXG4gICAgfSlcbiAgfSlcbn0pO1xuZXhwb3J0IHsgVGFtYWd1aVByb3ZpZGVyIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1UYW1hZ3VpUHJvdmlkZXIubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/tamagui/dist/esm/views/TamaguiProvider.mjs\n");

/***/ }),

/***/ "../../node_modules/tamagui/dist/esm/createTamagui.mjs":
/*!*************************************************************!*\
  !*** ../../node_modules/tamagui/dist/esm/createTamagui.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTamagui: () => (/* binding */ createTamagui)\n/* harmony export */ });\n/* harmony import */ var _tamagui_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tamagui/core */ \"../../node_modules/@tamagui/core/dist/cjs/index.cjs\");\n\nconst createTamagui =  false ? 0 : conf => {\n  const sizeTokenKeys = [\"$true\"],\n    hasKeys = (expectedKeys, obj) => expectedKeys.every(k => typeof obj[k] < \"u\"),\n    tamaguiConfig = (0,_tamagui_core__WEBPACK_IMPORTED_MODULE_0__.createTamagui)(conf);\n  for (const name of [\"size\", \"space\"]) {\n    const tokenSet = tamaguiConfig.tokensParsed[name];\n    if (!tokenSet) throw new Error(`Expected tokens for \"${name}\" in ${Object.keys(tamaguiConfig.tokensParsed).join(\", \")}`);\n    if (!hasKeys(sizeTokenKeys, tokenSet)) throw new Error(`\ncreateTamagui() missing expected tokens.${name}:\n\nReceived: ${Object.keys(tokenSet).join(\", \")}\n\nExpected: ${sizeTokenKeys.join(\", \")}\n\nTamagui expects a \"true\" key that is the same value as your default size. This is so \nit can size things up or down from the defaults without assuming which keys you use.\n\nPlease define a \"true\" or \"$true\" key on your size and space tokens like so (example):\n\nsize: {\n  sm: 2,\n  md: 10,\n  true: 10, // this means \"md\" is your default size\n  lg: 20,\n}\n\n`);\n  }\n  const expected = Object.keys(tamaguiConfig.tokensParsed.size);\n  for (const name of [\"radius\", \"zIndex\"]) {\n    const tokenSet = tamaguiConfig.tokensParsed[name],\n      received = Object.keys(tokenSet);\n    if (!received.some(rk => expected.includes(rk))) throw new Error(`\ncreateTamagui() invalid tokens.${name}:\n\nReceived: ${received.join(\", \")}\n\nExpected a subset of: ${expected.join(\", \")}\n\n`);\n  }\n  return tamaguiConfig;\n};\n\n//# sourceMappingURL=createTamagui.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/tamagui/dist/esm/createTamagui.mjs\n");

/***/ }),

/***/ "../../node_modules/tamagui/dist/esm/views/TamaguiProvider.mjs":
/*!*********************************************************************!*\
  !*** ../../node_modules/tamagui/dist/esm/views/TamaguiProvider.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TamaguiProvider: () => (/* binding */ TamaguiProvider)\n/* harmony export */ });\n/* harmony import */ var _tamagui_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tamagui/core */ \"../../node_modules/@tamagui/core/dist/cjs/index.cjs\");\n/* harmony import */ var _tamagui_portal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tamagui/portal */ \"../../node_modules/@tamagui/portal/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_z_index_stack__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tamagui/z-index-stack */ \"../../node_modules/@tamagui/z-index-stack/dist/esm/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n\n\n\n\nconst TamaguiProvider = ({\n  children,\n  ...props\n}) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tamagui_core__WEBPACK_IMPORTED_MODULE_3__.TamaguiProvider, {\n  ...props,\n  children: /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tamagui_z_index_stack__WEBPACK_IMPORTED_MODULE_1__.ZIndexStackContext.Provider, {\n    value: 1,\n    children: /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tamagui_portal__WEBPACK_IMPORTED_MODULE_0__.PortalProvider, {\n      shouldAddRootHost: !0,\n      children\n    })\n  })\n});\n\n//# sourceMappingURL=TamaguiProvider.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3RhbWFndWkvZGlzdC9lc20vdmlld3MvVGFtYWd1aVByb3ZpZGVyLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUE4RDtBQUNiO0FBQ1c7QUFDcEI7QUFDeEM7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxvQkFBb0Isc0RBQUcsQ0FBQywwREFBVTtBQUNuQztBQUNBLDJCQUEyQixzREFBRyxDQUFDLHNFQUFrQjtBQUNqRDtBQUNBLDZCQUE2QixzREFBRyxDQUFDLDJEQUFjO0FBQy9DO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNILENBQUM7QUFDMEI7QUFDM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9ub2RlX21vZHVsZXMvdGFtYWd1aS9kaXN0L2VzbS92aWV3cy9UYW1hZ3VpUHJvdmlkZXIubWpzPzQ2MDIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVGFtYWd1aVByb3ZpZGVyIGFzIE9HUHJvdmlkZXIgfSBmcm9tIFwiQHRhbWFndWkvY29yZVwiO1xuaW1wb3J0IHsgUG9ydGFsUHJvdmlkZXIgfSBmcm9tIFwiQHRhbWFndWkvcG9ydGFsXCI7XG5pbXBvcnQgeyBaSW5kZXhTdGFja0NvbnRleHQgfSBmcm9tIFwiQHRhbWFndWkvei1pbmRleC1zdGFja1wiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5jb25zdCBUYW1hZ3VpUHJvdmlkZXIgPSAoe1xuICBjaGlsZHJlbixcbiAgLi4ucHJvcHNcbn0pID0+IC8qIEBfX1BVUkVfXyAqL2pzeChPR1Byb3ZpZGVyLCB7XG4gIC4uLnByb3BzLFxuICBjaGlsZHJlbjogLyogQF9fUFVSRV9fICovanN4KFpJbmRleFN0YWNrQ29udGV4dC5Qcm92aWRlciwge1xuICAgIHZhbHVlOiAxLFxuICAgIGNoaWxkcmVuOiAvKiBAX19QVVJFX18gKi9qc3goUG9ydGFsUHJvdmlkZXIsIHtcbiAgICAgIHNob3VsZEFkZFJvb3RIb3N0OiAhMCxcbiAgICAgIGNoaWxkcmVuXG4gICAgfSlcbiAgfSlcbn0pO1xuZXhwb3J0IHsgVGFtYWd1aVByb3ZpZGVyIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1UYW1hZ3VpUHJvdmlkZXIubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/tamagui/dist/esm/views/TamaguiProvider.mjs\n");

/***/ })

};
;