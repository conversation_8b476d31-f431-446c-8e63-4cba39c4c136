"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tamagui";
exports.ids = ["vendor-chunks/tamagui"];
exports.modules = {

/***/ "(ssr)/../../node_modules/tamagui/dist/esm/createTamagui.mjs":
/*!*************************************************************!*\
  !*** ../../node_modules/tamagui/dist/esm/createTamagui.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTamagui: () => (/* binding */ createTamagui)\n/* harmony export */ });\n/* harmony import */ var _tamagui_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tamagui/core */ \"(ssr)/../../node_modules/@tamagui/core/dist/cjs/index.cjs\");\n\nconst createTamagui =  false ? 0 : conf => {\n  const sizeTokenKeys = [\"$true\"],\n    hasKeys = (expectedKeys, obj) => expectedKeys.every(k => typeof obj[k] < \"u\"),\n    tamaguiConfig = (0,_tamagui_core__WEBPACK_IMPORTED_MODULE_0__.createTamagui)(conf);\n  for (const name of [\"size\", \"space\"]) {\n    const tokenSet = tamaguiConfig.tokensParsed[name];\n    if (!tokenSet) throw new Error(`Expected tokens for \"${name}\" in ${Object.keys(tamaguiConfig.tokensParsed).join(\", \")}`);\n    if (!hasKeys(sizeTokenKeys, tokenSet)) throw new Error(`\ncreateTamagui() missing expected tokens.${name}:\n\nReceived: ${Object.keys(tokenSet).join(\", \")}\n\nExpected: ${sizeTokenKeys.join(\", \")}\n\nTamagui expects a \"true\" key that is the same value as your default size. This is so \nit can size things up or down from the defaults without assuming which keys you use.\n\nPlease define a \"true\" or \"$true\" key on your size and space tokens like so (example):\n\nsize: {\n  sm: 2,\n  md: 10,\n  true: 10, // this means \"md\" is your default size\n  lg: 20,\n}\n\n`);\n  }\n  const expected = Object.keys(tamaguiConfig.tokensParsed.size);\n  for (const name of [\"radius\", \"zIndex\"]) {\n    const tokenSet = tamaguiConfig.tokensParsed[name],\n      received = Object.keys(tokenSet);\n    if (!received.some(rk => expected.includes(rk))) throw new Error(`\ncreateTamagui() invalid tokens.${name}:\n\nReceived: ${received.join(\", \")}\n\nExpected a subset of: ${expected.join(\", \")}\n\n`);\n  }\n  return tamaguiConfig;\n};\n\n//# sourceMappingURL=createTamagui.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/tamagui/dist/esm/createTamagui.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/tamagui/dist/esm/helpers/inputHelpers.mjs":
/*!********************************************************************!*\
  !*** ../../node_modules/tamagui/dist/esm/helpers/inputHelpers.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inputSizeVariant: () => (/* binding */ inputSizeVariant),\n/* harmony export */   textAreaSizeVariant: () => (/* binding */ textAreaSizeVariant)\n/* harmony export */ });\n/* harmony import */ var _tamagui_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tamagui/core */ \"(ssr)/../../node_modules/@tamagui/core/dist/cjs/index.cjs\");\n/* harmony import */ var _tamagui_get_button_sized__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tamagui/get-button-sized */ \"(ssr)/../../node_modules/@tamagui/get-button-sized/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_get_font_sized__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tamagui/get-font-sized */ \"(ssr)/../../node_modules/@tamagui/get-font-sized/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_get_token__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tamagui/get-token */ \"(ssr)/../../node_modules/@tamagui/get-token/dist/esm/index.mjs\");\n\n\n\n\nconst inputSizeVariant = (val = \"$true\", extras) => {\n    if (extras.props.multiline || extras.props.numberOfLines > 1) return textAreaSizeVariant(val, extras);\n    const buttonStyles = (0,_tamagui_get_button_sized__WEBPACK_IMPORTED_MODULE_0__.getButtonSized)(val, extras),\n      paddingHorizontal = (0,_tamagui_get_token__WEBPACK_IMPORTED_MODULE_2__.getSpace)(val, {\n        shift: -1,\n        bounds: [2]\n      }),\n      fontStyle = (0,_tamagui_get_font_sized__WEBPACK_IMPORTED_MODULE_1__.getFontSized)(val, extras);\n    return !_tamagui_core__WEBPACK_IMPORTED_MODULE_3__.isWeb && fontStyle && delete fontStyle.lineHeight, {\n      ...fontStyle,\n      ...buttonStyles,\n      paddingHorizontal\n    };\n  },\n  textAreaSizeVariant = (val = \"$true\", extras) => {\n    const {\n        props\n      } = extras,\n      buttonStyles = (0,_tamagui_get_button_sized__WEBPACK_IMPORTED_MODULE_0__.getButtonSized)(val, extras),\n      fontStyle = (0,_tamagui_get_font_sized__WEBPACK_IMPORTED_MODULE_1__.getFontSized)(val, extras),\n      lines = props.rows ?? props.numberOfLines,\n      height = typeof lines == \"number\" ? lines * (0,_tamagui_core__WEBPACK_IMPORTED_MODULE_3__.getVariableValue)(fontStyle.lineHeight) : \"auto\",\n      paddingVertical = (0,_tamagui_get_token__WEBPACK_IMPORTED_MODULE_2__.getSpace)(val, {\n        shift: -2,\n        bounds: [2]\n      }),\n      paddingHorizontal = (0,_tamagui_get_token__WEBPACK_IMPORTED_MODULE_2__.getSpace)(val, {\n        shift: -1,\n        bounds: [2]\n      });\n    return {\n      ...buttonStyles,\n      ...fontStyle,\n      paddingVertical,\n      paddingHorizontal,\n      height\n    };\n  };\n\n//# sourceMappingURL=inputHelpers.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/tamagui/dist/esm/helpers/inputHelpers.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/tamagui/dist/esm/views/Anchor.mjs":
/*!************************************************************!*\
  !*** ../../node_modules/tamagui/dist/esm/views/Anchor.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Anchor: () => (/* binding */ Anchor)\n/* harmony export */ });\n/* harmony import */ var _tamagui_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tamagui/constants */ \"(ssr)/../../node_modules/@tamagui/constants/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tamagui/core */ \"(ssr)/../../node_modules/@tamagui/core/dist/cjs/index.cjs\");\n/* harmony import */ var _tamagui_text__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tamagui/text */ \"(ssr)/../../node_modules/@tamagui/text/dist/esm/SizableText.mjs\");\n/* harmony import */ var react_native_web__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-native-web */ \"(ssr)/../../node_modules/react-native-web/dist/exports/Linking/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n\n\nconst AnchorFrame = (0,_tamagui_core__WEBPACK_IMPORTED_MODULE_2__.styled)(_tamagui_text__WEBPACK_IMPORTED_MODULE_3__.SizableText, {\n    name: \"Anchor\",\n    tag: \"a\",\n    accessibilityRole: \"link\"\n  }),\n  Anchor = AnchorFrame.styleable(({\n    href,\n    target,\n    ...props\n  }, ref) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AnchorFrame, {\n    ...props,\n    ...(_tamagui_constants__WEBPACK_IMPORTED_MODULE_0__.isWeb ? {\n      href,\n      target\n    } : {\n      onPress: event => {\n        props.onPress?.(event), href !== void 0 && react_native_web__WEBPACK_IMPORTED_MODULE_4__[\"default\"].openURL(href);\n      }\n    }),\n    ref\n  }));\n\n//# sourceMappingURL=Anchor.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3RhbWFndWkvZGlzdC9lc20vdmlld3MvQW5jaG9yLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMkM7QUFDSjtBQUNLO0FBQ0Q7QUFDSDtBQUN4QyxvQkFBb0IscURBQU0sQ0FBQyxzREFBVztBQUN0QztBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHLHlCQUF5QixzREFBRztBQUMvQjtBQUNBLFFBQVEscURBQUs7QUFDYjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsbURBQW1ELHdEQUFPO0FBQzFEO0FBQ0EsS0FBSztBQUNMO0FBQ0EsR0FBRztBQUNlO0FBQ2xCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vbm9kZV9tb2R1bGVzL3RhbWFndWkvZGlzdC9lc20vdmlld3MvQW5jaG9yLm1qcz9lY2MwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzV2ViIH0gZnJvbSBcIkB0YW1hZ3VpL2NvbnN0YW50c1wiO1xuaW1wb3J0IHsgc3R5bGVkIH0gZnJvbSBcIkB0YW1hZ3VpL2NvcmVcIjtcbmltcG9ydCB7IFNpemFibGVUZXh0IH0gZnJvbSBcIkB0YW1hZ3VpL3RleHRcIjtcbmltcG9ydCB7IExpbmtpbmcgfSBmcm9tIFwicmVhY3QtbmF0aXZlLXdlYlwiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5jb25zdCBBbmNob3JGcmFtZSA9IHN0eWxlZChTaXphYmxlVGV4dCwge1xuICAgIG5hbWU6IFwiQW5jaG9yXCIsXG4gICAgdGFnOiBcImFcIixcbiAgICBhY2Nlc3NpYmlsaXR5Um9sZTogXCJsaW5rXCJcbiAgfSksXG4gIEFuY2hvciA9IEFuY2hvckZyYW1lLnN0eWxlYWJsZSgoe1xuICAgIGhyZWYsXG4gICAgdGFyZ2V0LFxuICAgIC4uLnByb3BzXG4gIH0sIHJlZikgPT4gLyogQF9fUFVSRV9fICovanN4KEFuY2hvckZyYW1lLCB7XG4gICAgLi4ucHJvcHMsXG4gICAgLi4uKGlzV2ViID8ge1xuICAgICAgaHJlZixcbiAgICAgIHRhcmdldFxuICAgIH0gOiB7XG4gICAgICBvblByZXNzOiBldmVudCA9PiB7XG4gICAgICAgIHByb3BzLm9uUHJlc3M/LihldmVudCksIGhyZWYgIT09IHZvaWQgMCAmJiBMaW5raW5nLm9wZW5VUkwoaHJlZik7XG4gICAgICB9XG4gICAgfSksXG4gICAgcmVmXG4gIH0pKTtcbmV4cG9ydCB7IEFuY2hvciB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9QW5jaG9yLm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/tamagui/dist/esm/views/Anchor.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/tamagui/dist/esm/views/Input.mjs":
/*!***********************************************************!*\
  !*** ../../node_modules/tamagui/dist/esm/views/Input.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input),\n/* harmony export */   InputFrame: () => (/* binding */ InputFrame),\n/* harmony export */   defaultStyles: () => (/* binding */ defaultStyles),\n/* harmony export */   useInputProps: () => (/* binding */ useInputProps)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tamagui_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tamagui/constants */ \"(ssr)/../../node_modules/@tamagui/constants/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tamagui/core */ \"(ssr)/../../node_modules/@tamagui/core/dist/cjs/index.cjs\");\n/* harmony import */ var _tamagui_focusable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tamagui/focusable */ \"(ssr)/../../node_modules/@tamagui/focusable/dist/esm/focusableInputHOC.mjs\");\n/* harmony import */ var react_native_web__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-native-web */ \"(ssr)/../../node_modules/react-native-web/dist/exports/TextInput/index.js\");\n/* harmony import */ var _helpers_inputHelpers_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../helpers/inputHelpers.mjs */ \"(ssr)/../../node_modules/tamagui/dist/esm/helpers/inputHelpers.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n\n\n\n\nconst defaultStyles = {\n    size: \"$true\",\n    fontFamily: \"$body\",\n    borderWidth: 1,\n    outlineWidth: 0,\n    color: \"$color\",\n    ...(_tamagui_constants__WEBPACK_IMPORTED_MODULE_1__.isWeb ? {\n      tabIndex: 0\n    } : {\n      focusable: !0\n    }),\n    borderColor: \"$borderColor\",\n    backgroundColor: \"$background\",\n    // this fixes a flex bug where it overflows container\n    minWidth: 0,\n    hoverStyle: {\n      borderColor: \"$borderColorHover\"\n    },\n    focusStyle: {\n      borderColor: \"$borderColorFocus\"\n    },\n    focusVisibleStyle: {\n      outlineColor: \"$outlineColor\",\n      outlineWidth: 2,\n      outlineStyle: \"solid\"\n    }\n  },\n  InputFrame = (0,_tamagui_core__WEBPACK_IMPORTED_MODULE_3__.styled)(react_native_web__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n    name: \"Input\",\n    variants: {\n      unstyled: {\n        false: defaultStyles\n      },\n      size: {\n        \"...size\": _helpers_inputHelpers_mjs__WEBPACK_IMPORTED_MODULE_5__.inputSizeVariant\n      },\n      disabled: {\n        true: {}\n      }\n    },\n    defaultVariants: {\n      unstyled: process.env.TAMAGUI_HEADLESS === \"1\"\n    }\n  }, {\n    isInput: !0,\n    accept: {\n      placeholderTextColor: \"color\",\n      selectionColor: \"color\"\n    }\n  }),\n  Input = InputFrame.styleable((propsIn, forwardedRef) => {\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null),\n      composedRefs = (0,_tamagui_core__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref),\n      props = useInputProps(propsIn, composedRefs);\n    return /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(InputFrame, {\n      ...props\n    });\n  });\nfunction useInputProps(props, ref) {\n  const theme = (0,_tamagui_core__WEBPACK_IMPORTED_MODULE_3__.useTheme)(),\n    focusableProps = (0,_tamagui_focusable__WEBPACK_IMPORTED_MODULE_6__.useFocusable)({\n      props,\n      ref,\n      isInput: !0\n    }),\n    placeholderTextColor = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n      const placeholderColorProp = props.placeholderTextColor;\n      return theme[placeholderColorProp]?.get() ?? placeholderColorProp ?? theme.placeholderColor?.get();\n    }, [props.placeholderTextColor, theme]);\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n    ref: focusableProps.ref,\n    readOnly: props.disabled,\n    ...props,\n    placeholderTextColor,\n    onChangeText: focusableProps.onChangeText\n  }), [focusableProps.ref, focusableProps.onChangeText, props.disabled, props, placeholderTextColor]);\n}\n\n//# sourceMappingURL=Input.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/tamagui/dist/esm/views/Input.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/tamagui/dist/esm/views/Spinner.mjs":
/*!*************************************************************!*\
  !*** ../../node_modules/tamagui/dist/esm/views/Spinner.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Spinner: () => (/* binding */ Spinner)\n/* harmony export */ });\n/* harmony import */ var _tamagui_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tamagui/core */ \"(ssr)/../../node_modules/@tamagui/core/dist/cjs/index.cjs\");\n/* harmony import */ var _tamagui_stacks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tamagui/stacks */ \"(ssr)/../../node_modules/@tamagui/stacks/dist/esm/Stacks.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_native_web__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-native-web */ \"(ssr)/../../node_modules/react-native-web/dist/exports/ActivityIndicator/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n\n\nconst Spinner = _tamagui_stacks__WEBPACK_IMPORTED_MODULE_2__.YStack.extractable((0,_tamagui_core__WEBPACK_IMPORTED_MODULE_3__.themeable)(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, ref) => {\n  const {\n      size,\n      color: colorProp,\n      ...stackProps\n    } = props,\n    theme = (0,_tamagui_core__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n  let color = colorProp;\n  return color && color[0] === \"$\" && (color = (0,_tamagui_core__WEBPACK_IMPORTED_MODULE_3__.variableToString)(theme[color])), /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_tamagui_stacks__WEBPACK_IMPORTED_MODULE_2__.YStack, {\n    ref,\n    ...stackProps,\n    children: /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_native_web__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n      size,\n      color\n    })\n  });\n}), {\n  componentName: \"Spinner\"\n}));\n\n//# sourceMappingURL=Spinner.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3RhbWFndWkvZGlzdC9lc20vdmlld3MvU3Bpbm5lci5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXNFO0FBQzdCO0FBQ1Y7QUFDc0I7QUFDYjtBQUN4QyxnQkFBZ0IsbURBQU0sYUFBYSx3REFBUyxDQUFDLDZDQUFnQjtBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTixZQUFZLHVEQUFRO0FBQ3BCO0FBQ0EsK0NBQStDLCtEQUFnQixnQ0FBZ0Msc0RBQUcsQ0FBQyxtREFBTTtBQUN6RztBQUNBO0FBQ0EsNkJBQTZCLHNEQUFHLENBQUMsd0RBQWlCO0FBQ2xEO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNILENBQUM7QUFDRDtBQUNBLENBQUM7QUFDa0I7QUFDbkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9ub2RlX21vZHVsZXMvdGFtYWd1aS9kaXN0L2VzbS92aWV3cy9TcGlubmVyLm1qcz85N2ZmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHRoZW1lYWJsZSwgdXNlVGhlbWUsIHZhcmlhYmxlVG9TdHJpbmcgfSBmcm9tIFwiQHRhbWFndWkvY29yZVwiO1xuaW1wb3J0IHsgWVN0YWNrIH0gZnJvbSBcIkB0YW1hZ3VpL3N0YWNrc1wiO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBBY3Rpdml0eUluZGljYXRvciB9IGZyb20gXCJyZWFjdC1uYXRpdmUtd2ViXCI7XG5pbXBvcnQgeyBqc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbmNvbnN0IFNwaW5uZXIgPSBZU3RhY2suZXh0cmFjdGFibGUodGhlbWVhYmxlKFJlYWN0LmZvcndhcmRSZWYoKHByb3BzLCByZWYpID0+IHtcbiAgY29uc3Qge1xuICAgICAgc2l6ZSxcbiAgICAgIGNvbG9yOiBjb2xvclByb3AsXG4gICAgICAuLi5zdGFja1Byb3BzXG4gICAgfSA9IHByb3BzLFxuICAgIHRoZW1lID0gdXNlVGhlbWUoKTtcbiAgbGV0IGNvbG9yID0gY29sb3JQcm9wO1xuICByZXR1cm4gY29sb3IgJiYgY29sb3JbMF0gPT09IFwiJFwiICYmIChjb2xvciA9IHZhcmlhYmxlVG9TdHJpbmcodGhlbWVbY29sb3JdKSksIC8qIEBfX1BVUkVfXyAqL2pzeChZU3RhY2ssIHtcbiAgICByZWYsXG4gICAgLi4uc3RhY2tQcm9wcyxcbiAgICBjaGlsZHJlbjogLyogQF9fUFVSRV9fICovanN4KEFjdGl2aXR5SW5kaWNhdG9yLCB7XG4gICAgICBzaXplLFxuICAgICAgY29sb3JcbiAgICB9KVxuICB9KTtcbn0pLCB7XG4gIGNvbXBvbmVudE5hbWU6IFwiU3Bpbm5lclwiXG59KSk7XG5leHBvcnQgeyBTcGlubmVyIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1TcGlubmVyLm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/tamagui/dist/esm/views/Spinner.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/tamagui/dist/esm/views/TamaguiProvider.mjs":
/*!*********************************************************************!*\
  !*** ../../node_modules/tamagui/dist/esm/views/TamaguiProvider.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TamaguiProvider: () => (/* binding */ TamaguiProvider)\n/* harmony export */ });\n/* harmony import */ var _tamagui_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tamagui/core */ \"(ssr)/../../node_modules/@tamagui/core/dist/cjs/index.cjs\");\n/* harmony import */ var _tamagui_portal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tamagui/portal */ \"(ssr)/../../node_modules/@tamagui/portal/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_z_index_stack__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tamagui/z-index-stack */ \"(ssr)/../../node_modules/@tamagui/z-index-stack/dist/esm/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n\nconst TamaguiProvider = ({\n  children,\n  ...props\n}) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tamagui_core__WEBPACK_IMPORTED_MODULE_3__.TamaguiProvider, {\n  ...props,\n  children: /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tamagui_z_index_stack__WEBPACK_IMPORTED_MODULE_1__.ZIndexStackContext.Provider, {\n    value: 1,\n    children: /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tamagui_portal__WEBPACK_IMPORTED_MODULE_0__.PortalProvider, {\n      shouldAddRootHost: !0,\n      children\n    })\n  })\n});\n\n//# sourceMappingURL=TamaguiProvider.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3RhbWFndWkvZGlzdC9lc20vdmlld3MvVGFtYWd1aVByb3ZpZGVyLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUE4RDtBQUNiO0FBQ1c7QUFDcEI7QUFDeEM7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxvQkFBb0Isc0RBQUcsQ0FBQywwREFBVTtBQUNuQztBQUNBLDJCQUEyQixzREFBRyxDQUFDLHNFQUFrQjtBQUNqRDtBQUNBLDZCQUE2QixzREFBRyxDQUFDLDJEQUFjO0FBQy9DO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNILENBQUM7QUFDMEI7QUFDM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9ub2RlX21vZHVsZXMvdGFtYWd1aS9kaXN0L2VzbS92aWV3cy9UYW1hZ3VpUHJvdmlkZXIubWpzP2UzMDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVGFtYWd1aVByb3ZpZGVyIGFzIE9HUHJvdmlkZXIgfSBmcm9tIFwiQHRhbWFndWkvY29yZVwiO1xuaW1wb3J0IHsgUG9ydGFsUHJvdmlkZXIgfSBmcm9tIFwiQHRhbWFndWkvcG9ydGFsXCI7XG5pbXBvcnQgeyBaSW5kZXhTdGFja0NvbnRleHQgfSBmcm9tIFwiQHRhbWFndWkvei1pbmRleC1zdGFja1wiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5jb25zdCBUYW1hZ3VpUHJvdmlkZXIgPSAoe1xuICBjaGlsZHJlbixcbiAgLi4ucHJvcHNcbn0pID0+IC8qIEBfX1BVUkVfXyAqL2pzeChPR1Byb3ZpZGVyLCB7XG4gIC4uLnByb3BzLFxuICBjaGlsZHJlbjogLyogQF9fUFVSRV9fICovanN4KFpJbmRleFN0YWNrQ29udGV4dC5Qcm92aWRlciwge1xuICAgIHZhbHVlOiAxLFxuICAgIGNoaWxkcmVuOiAvKiBAX19QVVJFX18gKi9qc3goUG9ydGFsUHJvdmlkZXIsIHtcbiAgICAgIHNob3VsZEFkZFJvb3RIb3N0OiAhMCxcbiAgICAgIGNoaWxkcmVuXG4gICAgfSlcbiAgfSlcbn0pO1xuZXhwb3J0IHsgVGFtYWd1aVByb3ZpZGVyIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1UYW1hZ3VpUHJvdmlkZXIubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/tamagui/dist/esm/views/TamaguiProvider.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/tamagui/dist/esm/views/Text.mjs":
/*!**********************************************************!*\
  !*** ../../node_modules/tamagui/dist/esm/views/Text.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Text: () => (/* binding */ Text)\n/* harmony export */ });\n/* harmony import */ var _tamagui_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tamagui/core */ \"(ssr)/../../node_modules/@tamagui/core/dist/cjs/index.cjs\");\n\nconst Text = (0,_tamagui_core__WEBPACK_IMPORTED_MODULE_0__.styled)(_tamagui_core__WEBPACK_IMPORTED_MODULE_0__.Text, {\n  variants: {\n    unstyled: {\n      false: {\n        color: \"$color\"\n      }\n    }\n  },\n  defaultVariants: {\n    unstyled: process.env.TAMAGUI_HEADLESS === \"1\"\n  }\n});\n\n//# sourceMappingURL=Text.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3RhbWFndWkvZGlzdC9lc20vdmlld3MvVGV4dC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNEQ7QUFDNUQsYUFBYSxxREFBTSxDQUFDLCtDQUFXO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ2U7QUFDaEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9ub2RlX21vZHVsZXMvdGFtYWd1aS9kaXN0L2VzbS92aWV3cy9UZXh0Lm1qcz9lMTJlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFRleHQgYXMgVGFtYWd1aVRleHQsIHN0eWxlZCB9IGZyb20gXCJAdGFtYWd1aS9jb3JlXCI7XG5jb25zdCBUZXh0ID0gc3R5bGVkKFRhbWFndWlUZXh0LCB7XG4gIHZhcmlhbnRzOiB7XG4gICAgdW5zdHlsZWQ6IHtcbiAgICAgIGZhbHNlOiB7XG4gICAgICAgIGNvbG9yOiBcIiRjb2xvclwiXG4gICAgICB9XG4gICAgfVxuICB9LFxuICBkZWZhdWx0VmFyaWFudHM6IHtcbiAgICB1bnN0eWxlZDogcHJvY2Vzcy5lbnYuVEFNQUdVSV9IRUFETEVTUyA9PT0gXCIxXCJcbiAgfVxufSk7XG5leHBvcnQgeyBUZXh0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1UZXh0Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/tamagui/dist/esm/views/Text.mjs\n");

/***/ }),

/***/ "../../node_modules/tamagui/dist/esm/createTamagui.mjs":
/*!*************************************************************!*\
  !*** ../../node_modules/tamagui/dist/esm/createTamagui.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTamagui: () => (/* binding */ createTamagui)\n/* harmony export */ });\n/* harmony import */ var _tamagui_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tamagui/core */ \"../../node_modules/@tamagui/core/dist/cjs/index.cjs\");\n\nconst createTamagui =  false ? 0 : conf => {\n  const sizeTokenKeys = [\"$true\"],\n    hasKeys = (expectedKeys, obj) => expectedKeys.every(k => typeof obj[k] < \"u\"),\n    tamaguiConfig = (0,_tamagui_core__WEBPACK_IMPORTED_MODULE_0__.createTamagui)(conf);\n  for (const name of [\"size\", \"space\"]) {\n    const tokenSet = tamaguiConfig.tokensParsed[name];\n    if (!tokenSet) throw new Error(`Expected tokens for \"${name}\" in ${Object.keys(tamaguiConfig.tokensParsed).join(\", \")}`);\n    if (!hasKeys(sizeTokenKeys, tokenSet)) throw new Error(`\ncreateTamagui() missing expected tokens.${name}:\n\nReceived: ${Object.keys(tokenSet).join(\", \")}\n\nExpected: ${sizeTokenKeys.join(\", \")}\n\nTamagui expects a \"true\" key that is the same value as your default size. This is so \nit can size things up or down from the defaults without assuming which keys you use.\n\nPlease define a \"true\" or \"$true\" key on your size and space tokens like so (example):\n\nsize: {\n  sm: 2,\n  md: 10,\n  true: 10, // this means \"md\" is your default size\n  lg: 20,\n}\n\n`);\n  }\n  const expected = Object.keys(tamaguiConfig.tokensParsed.size);\n  for (const name of [\"radius\", \"zIndex\"]) {\n    const tokenSet = tamaguiConfig.tokensParsed[name],\n      received = Object.keys(tokenSet);\n    if (!received.some(rk => expected.includes(rk))) throw new Error(`\ncreateTamagui() invalid tokens.${name}:\n\nReceived: ${received.join(\", \")}\n\nExpected a subset of: ${expected.join(\", \")}\n\n`);\n  }\n  return tamaguiConfig;\n};\n\n//# sourceMappingURL=createTamagui.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/tamagui/dist/esm/createTamagui.mjs\n");

/***/ }),

/***/ "../../node_modules/tamagui/dist/esm/views/Anchor.mjs":
/*!************************************************************!*\
  !*** ../../node_modules/tamagui/dist/esm/views/Anchor.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Anchor: () => (/* binding */ Anchor)\n/* harmony export */ });\n/* harmony import */ var _tamagui_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tamagui/constants */ \"../../node_modules/@tamagui/constants/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tamagui/core */ \"../../node_modules/@tamagui/core/dist/cjs/index.cjs\");\n/* harmony import */ var _tamagui_text__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tamagui/text */ \"../../node_modules/@tamagui/text/dist/esm/SizableText.mjs\");\n/* harmony import */ var react_native_web__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-native-web */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n\n\n\n\n\nconst AnchorFrame = (0,_tamagui_core__WEBPACK_IMPORTED_MODULE_2__.styled)(_tamagui_text__WEBPACK_IMPORTED_MODULE_3__.SizableText, {\n    name: \"Anchor\",\n    tag: \"a\",\n    accessibilityRole: \"link\"\n  }),\n  Anchor = AnchorFrame.styleable(({\n    href,\n    target,\n    ...props\n  }, ref) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AnchorFrame, {\n    ...props,\n    ...(_tamagui_constants__WEBPACK_IMPORTED_MODULE_0__.isWeb ? {\n      href,\n      target\n    } : {\n      onPress: event => {\n        props.onPress?.(event), href !== void 0 && react_native_web__WEBPACK_IMPORTED_MODULE_4__.Linking.openURL(href);\n      }\n    }),\n    ref\n  }));\n\n//# sourceMappingURL=Anchor.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3RhbWFndWkvZGlzdC9lc20vdmlld3MvQW5jaG9yLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMkM7QUFDSjtBQUNLO0FBQ0Q7QUFDSDtBQUN4QyxvQkFBb0IscURBQU0sQ0FBQyxzREFBVztBQUN0QztBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHLHlCQUF5QixzREFBRztBQUMvQjtBQUNBLFFBQVEscURBQUs7QUFDYjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsbURBQW1ELHFEQUFPO0FBQzFEO0FBQ0EsS0FBSztBQUNMO0FBQ0EsR0FBRztBQUNlO0FBQ2xCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vbm9kZV9tb2R1bGVzL3RhbWFndWkvZGlzdC9lc20vdmlld3MvQW5jaG9yLm1qcz81ZWZmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzV2ViIH0gZnJvbSBcIkB0YW1hZ3VpL2NvbnN0YW50c1wiO1xuaW1wb3J0IHsgc3R5bGVkIH0gZnJvbSBcIkB0YW1hZ3VpL2NvcmVcIjtcbmltcG9ydCB7IFNpemFibGVUZXh0IH0gZnJvbSBcIkB0YW1hZ3VpL3RleHRcIjtcbmltcG9ydCB7IExpbmtpbmcgfSBmcm9tIFwicmVhY3QtbmF0aXZlLXdlYlwiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5jb25zdCBBbmNob3JGcmFtZSA9IHN0eWxlZChTaXphYmxlVGV4dCwge1xuICAgIG5hbWU6IFwiQW5jaG9yXCIsXG4gICAgdGFnOiBcImFcIixcbiAgICBhY2Nlc3NpYmlsaXR5Um9sZTogXCJsaW5rXCJcbiAgfSksXG4gIEFuY2hvciA9IEFuY2hvckZyYW1lLnN0eWxlYWJsZSgoe1xuICAgIGhyZWYsXG4gICAgdGFyZ2V0LFxuICAgIC4uLnByb3BzXG4gIH0sIHJlZikgPT4gLyogQF9fUFVSRV9fICovanN4KEFuY2hvckZyYW1lLCB7XG4gICAgLi4ucHJvcHMsXG4gICAgLi4uKGlzV2ViID8ge1xuICAgICAgaHJlZixcbiAgICAgIHRhcmdldFxuICAgIH0gOiB7XG4gICAgICBvblByZXNzOiBldmVudCA9PiB7XG4gICAgICAgIHByb3BzLm9uUHJlc3M/LihldmVudCksIGhyZWYgIT09IHZvaWQgMCAmJiBMaW5raW5nLm9wZW5VUkwoaHJlZik7XG4gICAgICB9XG4gICAgfSksXG4gICAgcmVmXG4gIH0pKTtcbmV4cG9ydCB7IEFuY2hvciB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9QW5jaG9yLm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/tamagui/dist/esm/views/Anchor.mjs\n");

/***/ }),

/***/ "../../node_modules/tamagui/dist/esm/views/TamaguiProvider.mjs":
/*!*********************************************************************!*\
  !*** ../../node_modules/tamagui/dist/esm/views/TamaguiProvider.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TamaguiProvider: () => (/* binding */ TamaguiProvider)\n/* harmony export */ });\n/* harmony import */ var _tamagui_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tamagui/core */ \"../../node_modules/@tamagui/core/dist/cjs/index.cjs\");\n/* harmony import */ var _tamagui_portal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tamagui/portal */ \"../../node_modules/@tamagui/portal/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_z_index_stack__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tamagui/z-index-stack */ \"../../node_modules/@tamagui/z-index-stack/dist/esm/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n\n\n\n\nconst TamaguiProvider = ({\n  children,\n  ...props\n}) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tamagui_core__WEBPACK_IMPORTED_MODULE_3__.TamaguiProvider, {\n  ...props,\n  children: /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tamagui_z_index_stack__WEBPACK_IMPORTED_MODULE_1__.ZIndexStackContext.Provider, {\n    value: 1,\n    children: /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tamagui_portal__WEBPACK_IMPORTED_MODULE_0__.PortalProvider, {\n      shouldAddRootHost: !0,\n      children\n    })\n  })\n});\n\n//# sourceMappingURL=TamaguiProvider.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL3RhbWFndWkvZGlzdC9lc20vdmlld3MvVGFtYWd1aVByb3ZpZGVyLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUE4RDtBQUNiO0FBQ1c7QUFDcEI7QUFDeEM7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxvQkFBb0Isc0RBQUcsQ0FBQywwREFBVTtBQUNuQztBQUNBLDJCQUEyQixzREFBRyxDQUFDLHNFQUFrQjtBQUNqRDtBQUNBLDZCQUE2QixzREFBRyxDQUFDLDJEQUFjO0FBQy9DO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNILENBQUM7QUFDMEI7QUFDM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9ub2RlX21vZHVsZXMvdGFtYWd1aS9kaXN0L2VzbS92aWV3cy9UYW1hZ3VpUHJvdmlkZXIubWpzPzQ2MDIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVGFtYWd1aVByb3ZpZGVyIGFzIE9HUHJvdmlkZXIgfSBmcm9tIFwiQHRhbWFndWkvY29yZVwiO1xuaW1wb3J0IHsgUG9ydGFsUHJvdmlkZXIgfSBmcm9tIFwiQHRhbWFndWkvcG9ydGFsXCI7XG5pbXBvcnQgeyBaSW5kZXhTdGFja0NvbnRleHQgfSBmcm9tIFwiQHRhbWFndWkvei1pbmRleC1zdGFja1wiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5jb25zdCBUYW1hZ3VpUHJvdmlkZXIgPSAoe1xuICBjaGlsZHJlbixcbiAgLi4ucHJvcHNcbn0pID0+IC8qIEBfX1BVUkVfXyAqL2pzeChPR1Byb3ZpZGVyLCB7XG4gIC4uLnByb3BzLFxuICBjaGlsZHJlbjogLyogQF9fUFVSRV9fICovanN4KFpJbmRleFN0YWNrQ29udGV4dC5Qcm92aWRlciwge1xuICAgIHZhbHVlOiAxLFxuICAgIGNoaWxkcmVuOiAvKiBAX19QVVJFX18gKi9qc3goUG9ydGFsUHJvdmlkZXIsIHtcbiAgICAgIHNob3VsZEFkZFJvb3RIb3N0OiAhMCxcbiAgICAgIGNoaWxkcmVuXG4gICAgfSlcbiAgfSlcbn0pO1xuZXhwb3J0IHsgVGFtYWd1aVByb3ZpZGVyIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1UYW1hZ3VpUHJvdmlkZXIubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/tamagui/dist/esm/views/TamaguiProvider.mjs\n");

/***/ })

};
;