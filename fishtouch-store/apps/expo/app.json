{"expo": {"name": "FishTouchStore", "slug": "FishTouchStore", "scheme": "FishTouchStore", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "updates": {"fallbackToCacheTimeout": 0}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.store.fishtouch.app"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "package": "com.store.fishtouch.app"}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-router", "expo-font"], "experiments": {"typedRoutes": true}}}