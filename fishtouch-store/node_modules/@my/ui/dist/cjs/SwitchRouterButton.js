var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: !0 });
}, __copyProps = (to, from, except, desc) => {
  if (from && typeof from == "object" || typeof from == "function")
    for (let key of __getOwnPropNames(from))
      !__hasOwnProp.call(to, key) && key !== except && __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: !0 }), mod);
var SwitchRouterButton_exports = {};
__export(SwitchRouterButton_exports, {
  SwitchRouterButton: () => SwitchRouterButton
});
module.exports = __toCommonJS(SwitchRouterButton_exports);
var import_tamagui = require("tamagui"), import_jsx_runtime = require("react/jsx-runtime");
const SwitchRouterButton = ({ pagesMode = !1 }) => /* @__PURE__ */ (0, import_jsx_runtime.jsx)(import_tamagui.Anchor, { text: "center", color: "$color12", href: pagesMode ? "/" : "/pages-example", children: /* @__PURE__ */ (0, import_jsx_runtime.jsxs)(import_tamagui.Button, { children: [
  "Change router: ",
  pagesMode ? "pages" : "app"
] }) });
//# sourceMappingURL=SwitchRouterButton.js.map
