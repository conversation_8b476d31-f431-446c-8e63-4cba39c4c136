{"version": 3, "sources": ["/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/ui/src/SwitchRouterButton.tsx"], "mappings": ";;;;;;;;;;;;;;;;;;;uDAAA,iBAA+B,oBAElBA,qBAAqB,SAAA,OAAA;MAAC,EAAEC,YAAY,GAAK,IAA2B;AAC/E,SACE,uCAAAC,KAACC,uBAAAA;IAAOC,MAAK;IAASC,OAAM;IAAWC,MAAML,YAAY,MAAM;cAC7D,uCAAAM,MAACC,uBAAAA;;QAAO;QAAgBP,YAAY,UAAU;;;;AAGpD;", "names": ["SwitchRouterButton", "pagesMode", "_jsx", "<PERSON><PERSON>", "text", "color", "href", "_jsxs", "<PERSON><PERSON>"]}