import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "tamagui";
var SwitchRouterButton = function(param) {
  var { pagesMode = !1 } = param;
  return /* @__PURE__ */ _jsx(Anchor, {
    text: "center",
    color: "$color12",
    href: pagesMode ? "/" : "/pages-example",
    children: /* @__PURE__ */ _jsxs(But<PERSON>, {
      children: [
        "Change router: ",
        pagesMode ? "pages" : "app"
      ]
    })
  });
};
export {
  SwitchRouterButton
};
//# sourceMappingURL=SwitchRouterButton.js.map
