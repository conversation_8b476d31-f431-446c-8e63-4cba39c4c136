import { useState } from "react";
import { Button, useIsomorphicLayoutEffect } from "tamagui";
import { useThemeSetting, useRootTheme } from "@tamagui/next-theme";
import { jsxs } from "react/jsx-runtime";
const SwitchThemeButton = () => {
  const themeSetting = useThemeSetting(),
    [theme] = useRootTheme(),
    [clientTheme, setClientTheme] = useState("light");
  return useIsomorphicLayoutEffect(() => {
    setClientTheme(themeSetting.forcedTheme || themeSetting.current || theme);
  }, [themeSetting.current, themeSetting.resolvedTheme]), /* @__PURE__ */jsxs(Button, {
    onPress: themeSetting.toggle,
    children: ["Change theme: ", clientTheme]
  });
};
export { SwitchThemeButton };
//# sourceMappingURL=SwitchThemeButton.mjs.map
