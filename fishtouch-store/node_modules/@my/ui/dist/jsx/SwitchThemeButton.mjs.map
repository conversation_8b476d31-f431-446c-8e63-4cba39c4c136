{"version": 3, "names": ["useState", "<PERSON><PERSON>", "useIsomorphicLayoutEffect", "useThemeSetting", "useRootTheme", "jsxs", "SwitchThemeButton", "themeSetting", "theme", "clientTheme", "setClientTheme", "forcedTheme", "current", "resolvedTheme", "onPress", "toggle", "children"], "sources": ["../../src/SwitchThemeButton.tsx"], "sourcesContent": [null], "mappings": "AAAA,SAASA,QAAA,QAAgB;AACzB,SAASC,MAAA,EAAQC,yBAAA,QAAiC;AAClD,SAASC,eAAA,EAAiBC,YAAA,QAAoB;AAYrC,SAAAC,IAAA;AAVF,MAAMC,iBAAA,GAAoBA,CAAA,KAAM;EACrC,MAAMC,YAAA,GAAeJ,eAAA,CAAgB;IAC/B,CAACK,KAAK,IAAIJ,YAAA,CAAa;IAEvB,CAACK,WAAA,EAAaC,cAAc,IAAIV,QAAA,CAA6B,OAAO;EAE1E,OAAAE,yBAAA,CAA0B,MAAM;IAC9BQ,cAAA,CAAeH,YAAA,CAAaI,WAAA,IAAeJ,YAAA,CAAaK,OAAA,IAAWJ,KAAK;EAC1E,GAAG,CAACD,YAAA,CAAaK,OAAA,EAASL,YAAA,CAAaM,aAAa,CAAC,GAE9C,eAAAR,IAAA,CAACJ,MAAA;IAAOa,OAAA,EAASP,YAAA,CAAaQ,MAAA;IAAQC,QAAA,qBAAeP,WAAA;EAAA,CAAY;AAC1E", "ignoreList": []}