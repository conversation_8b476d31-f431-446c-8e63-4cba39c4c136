import { <PERSON><PERSON>, <PERSON><PERSON> } from "tamagui";
import { jsx, jsxs } from "react/jsx-runtime";
const SwitchRouterButton = ({
  pagesMode = !1
}) => /* @__PURE__ */jsx(Anchor, {
  text: "center",
  color: "$color12",
  href: pagesMode ? "/" : "/pages-example",
  children: /* @__PURE__ */jsxs(<PERSON><PERSON>, {
    children: ["Change router: ", pagesMode ? "pages" : "app"]
  })
});
export { SwitchRouterButton };
//# sourceMappingURL=SwitchRouterButton.mjs.map
