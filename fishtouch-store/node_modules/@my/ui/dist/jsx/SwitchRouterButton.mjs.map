{"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON>", "jsx", "jsxs", "SwitchRouterButton", "pagesMode", "text", "color", "href", "children"], "sources": ["../../src/SwitchRouterButton.tsx"], "sourcesContent": [null], "mappings": "AAAA,SAASA,MAAA,EAAQC,MAAA,QAAc;AAI3B,SAAAC,GAAA,EACEC,IAAA,QADF;AAFG,MAAMC,kBAAA,GAAqBA,CAAC;EAAEC,SAAA,GAAY;AAAM,MAEnD,eAAAH,GAAA,CAACF,MAAA;EAAOM,IAAA,EAAK;EAASC,KAAA,EAAM;EAAWC,IAAA,EAAMH,SAAA,GAAY,MAAM;EAC7DI,QAAA,iBAAAN,IAAA,CAACF,MAAA;IAAOQ,QAAA,sBAAgBJ,SAAA,GAAY,UAAU;EAAA,CAAM;AAAA,CACtD", "ignoreList": []}