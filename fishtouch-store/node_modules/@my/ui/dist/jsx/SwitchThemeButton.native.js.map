{"version": 3, "sources": ["/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/ui/src/SwitchThemeButton.tsx"], "mappings": ";AAAA,SAASA,gBAAgB;AACzB,SAASC,QAAQC,iCAAiC;AAClD,SAASC,iBAAiBC,oBAAoB;AAEvC,IAAMC,oBAAoB,WAAA;AAC/B,MAAMC,eAAeH,gBAAAA,GACf,CAACI,KAAAA,IAASH,aAAAA,GAEV,CAACI,aAAaC,cAAAA,IAAkBT,SAA6B,OAAA;AAEnEE,mCAA0B,WAAA;AACxBO,mBAAeH,aAAaI,eAAeJ,aAAaK,WAAWJ,KAAAA;EACrE,GAAG;IAACD,aAAaK;IAASL,aAAaM;GAAc,GAE9C,sBAACX,QAAAA;IAAOY,SAASP,aAAaQ;;MAAQ;MAAeN;;;AAC9D;", "names": ["useState", "<PERSON><PERSON>", "useIsomorphicLayoutEffect", "useThemeSetting", "useRootTheme", "SwitchThemeButton", "themeSetting", "theme", "clientTheme", "setClientTheme", "forcedTheme", "current", "resolvedTheme", "onPress", "toggle"]}