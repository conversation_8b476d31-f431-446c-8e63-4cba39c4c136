{"version": 3, "sources": ["/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/ui/src/SwitchRouterButton.tsx"], "mappings": ";AAAA,SAASA,QAAQC,cAAc;AAExB,IAAMC,qBAAqB,SAAA,OAAA;MAAC,EAAEC,YAAY,GAAK,IAA2B;AAC/E,SACE,qBAACH,QAAAA;IAAOI,MAAK;IAASC,OAAM;IAAWC,MAAMH,YAAY,MAAM;cAC7D,sBAACF,QAAAA;;QAAO;QAAgBE,YAAY,UAAU;;;;AAGpD;", "names": ["<PERSON><PERSON>", "<PERSON><PERSON>", "SwitchRouterButton", "pagesMode", "text", "color", "href"]}