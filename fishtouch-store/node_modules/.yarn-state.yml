# Warning: This file is automatically generated. Removing it is fine, but will
# cause your node_modules installation to become invalidated.

__metadata:
  version: 1
  nmMode: classic

"@0no-co/graphql.web@virtual:ab2fdc9886954907a5ac4d23dc604b0ceebcc5cf4535c8f4cf69d490047459cb4d46a1bacb54e3f9c021cc8c7a67af9dd1b246a0f28f582c7351e6f302804802#npm:1.1.1":
  locations:
    - "node_modules/@0no-co/graphql.web"

"@ampproject/remapping@npm:2.3.0":
  locations:
    - "node_modules/@ampproject/remapping"

"@babel/code-frame@npm:7.10.4":
  locations:
    - "node_modules/@expo/xcpretty/node_modules/@babel/code-frame"
    - "node_modules/@expo/json-file/node_modules/@babel/code-frame"
    - "node_modules/@expo/config/node_modules/@babel/code-frame"

"@babel/code-frame@npm:7.26.2":
  locations:
    - "node_modules/@babel/code-frame"

"@babel/compat-data@npm:7.26.8":
  locations:
    - "node_modules/@babel/compat-data"

"@babel/core@npm:7.26.9":
  locations:
    - "node_modules/@babel/core"

"@babel/generator@npm:7.26.9":
  locations:
    - "node_modules/@babel/generator"

"@babel/helper-annotate-as-pure@npm:7.25.9":
  locations:
    - "node_modules/@babel/helper-annotate-as-pure"

"@babel/helper-compilation-targets@npm:7.26.5":
  locations:
    - "node_modules/@babel/helper-compilation-targets"

"@babel/helper-create-class-features-plugin@virtual:79199e52024c831f64d69af0c4a4dc9b831dec2bf041d15a15c02a4d97a143c58c51b042219509073dc7748dd5b4ff6c41cfaf78f799f9a0b528e6ccf5b39305#npm:7.26.9":
  locations:
    - "node_modules/@babel/helper-create-class-features-plugin"
  aliases:
    - "virtual:d33b091bd11cb9e65d0c1cb520fba054f3482a0d1130a3279b7b8a586c04bea12e90a03e0b179ff877b86b22d55d9e004b257b2a92e5165ad1baf55ae0c0c24e#npm:7.26.9"

"@babel/helper-create-regexp-features-plugin@virtual:ae194f9d90b029b32950bedc2af34004c23de7c5d823bc9ca1fa35959f9154d9db6b426189d26433e732f812f7ad7c3aa0fc02cff61e0c7fa352c14734573cef#npm:7.26.3":
  locations:
    - "node_modules/@babel/helper-create-regexp-features-plugin"

"@babel/helper-define-polyfill-provider@virtual:35232139103a14f3071a5811667f4ed8212c5705d48139892177bdc75322e9db8a005834a555c791edb31c7afda0b237c2a81fdfa17272be18a24e427b891fe1#npm:0.6.3":
  locations:
    - "node_modules/@babel/helper-define-polyfill-provider"

"@babel/helper-member-expression-to-functions@npm:7.25.9":
  locations:
    - "node_modules/@babel/helper-member-expression-to-functions"

"@babel/helper-module-imports@npm:7.25.9":
  locations:
    - "node_modules/@babel/helper-module-imports"

"@babel/helper-module-transforms@virtual:12b18cb40d5c617053821811de491a189d02da1d08d58f7656d9f9ecb29c954457e5dfc20f3916931f9e19b353a25ee0340013ea9e29eab98af42567b5bf0497#npm:7.26.0":
  locations:
    - "node_modules/@babel/helper-module-transforms"
  aliases:
    - "virtual:b0e5b0be4c0b695f63ca55323cb2cea1f38bc89ae4f12a6f6d9d64e34a3c161c8a1d820bb2e869d0ca3379b71b6620fff4ddf05f3e23067ff45dec9a09bd3c89#npm:7.26.0"

"@babel/helper-optimise-call-expression@npm:7.25.9":
  locations:
    - "node_modules/@babel/helper-optimise-call-expression"

"@babel/helper-plugin-utils@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-plugin-utils"

"@babel/helper-remap-async-to-generator@virtual:1e9d46de276a483c1d856d0d616a948b9dbcb3f375bca057003b317ebf6ebcf517fbbc233e7124b54db190a83e06eb198dc9d6e584f15a53cbb394d05ac594f7#npm:7.25.9":
  locations:
    - "node_modules/@babel/helper-remap-async-to-generator"

"@babel/helper-replace-supers@virtual:4996548a1e358124358b30b5f29aa9b9fe7a89564f8316c6c88f9342b0bea7173e38a229c135cc76fb9e753d3b1a5f0eae96453911c79989c3ae636bfab6a439#npm:7.26.5":
  locations:
    - "node_modules/@babel/helper-replace-supers"
  aliases:
    - "virtual:8b6fc1ea76abf7937e3d222df272ed047a847a2699bca62a30bcc9af8789b22cb11636a5406b8334350b65a33181ecff72ae74e226e409be7cfe1916e590a8f6#npm:7.26.5"

"@babel/helper-skip-transparent-expression-wrappers@npm:7.25.9":
  locations:
    - "node_modules/@babel/helper-skip-transparent-expression-wrappers"

"@babel/helper-string-parser@npm:7.25.9":
  locations:
    - "node_modules/@babel/helper-string-parser"

"@babel/helper-validator-identifier@npm:7.25.9":
  locations:
    - "node_modules/@babel/helper-validator-identifier"

"@babel/helper-validator-option@npm:7.25.9":
  locations:
    - "node_modules/@babel/helper-validator-option"

"@babel/helper-wrap-function@npm:7.25.9":
  locations:
    - "node_modules/@babel/helper-wrap-function"

"@babel/helpers@npm:7.26.9":
  locations:
    - "node_modules/@babel/helpers"

"@babel/highlight@npm:7.25.9":
  locations:
    - "node_modules/@babel/highlight"

"@babel/parser@npm:7.26.9":
  locations:
    - "node_modules/@babel/parser"

"@babel/plugin-proposal-decorators@virtual:dc73448999d20739c62c8b95dee9efcd6bc601751b66c222699d35727b5caf48257eaa5370986b269e8a92860d46afab0c82782ba3c9ab36340e68baf1b47715#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-proposal-decorators"

"@babel/plugin-proposal-export-default-from@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-proposal-export-default-from"
  aliases:
    - "virtual:dc73448999d20739c62c8b95dee9efcd6bc601751b66c222699d35727b5caf48257eaa5370986b269e8a92860d46afab0c82782ba3c9ab36340e68baf1b47715#npm:7.25.9"

"@babel/plugin-syntax-async-generators@virtual:bd7c2a233f864186fabec5cf7921796bd19a54a835120a69358a508971959cb58b8f00f0882e8769c78e6e776a8f658587ddd0c12cf3aaca41f2babab4b8d518#npm:7.8.4":
  locations:
    - "node_modules/@babel/plugin-syntax-async-generators"

"@babel/plugin-syntax-bigint@virtual:bd7c2a233f864186fabec5cf7921796bd19a54a835120a69358a508971959cb58b8f00f0882e8769c78e6e776a8f658587ddd0c12cf3aaca41f2babab4b8d518#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-bigint"

"@babel/plugin-syntax-class-properties@virtual:bd7c2a233f864186fabec5cf7921796bd19a54a835120a69358a508971959cb58b8f00f0882e8769c78e6e776a8f658587ddd0c12cf3aaca41f2babab4b8d518#npm:7.12.13":
  locations:
    - "node_modules/@babel/plugin-syntax-class-properties"

"@babel/plugin-syntax-class-static-block@virtual:bd7c2a233f864186fabec5cf7921796bd19a54a835120a69358a508971959cb58b8f00f0882e8769c78e6e776a8f658587ddd0c12cf3aaca41f2babab4b8d518#npm:7.14.5":
  locations:
    - "node_modules/@babel/plugin-syntax-class-static-block"

"@babel/plugin-syntax-decorators@virtual:d33b091bd11cb9e65d0c1cb520fba054f3482a0d1130a3279b7b8a586c04bea12e90a03e0b179ff877b86b22d55d9e004b257b2a92e5165ad1baf55ae0c0c24e#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-syntax-decorators"

"@babel/plugin-syntax-dynamic-import@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-dynamic-import"

"@babel/plugin-syntax-export-default-from@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-syntax-export-default-from"
  aliases:
    - "virtual:dc73448999d20739c62c8b95dee9efcd6bc601751b66c222699d35727b5caf48257eaa5370986b269e8a92860d46afab0c82782ba3c9ab36340e68baf1b47715#npm:7.25.9"

"@babel/plugin-syntax-flow@virtual:dbfa5d78ceba91dc4c6903e3f57858034d2ed0ae3caa8fb56389ef486ba370ede79dec0e6a1b07c93471d06023130473151dcf8b375baebc611a5b5af8d409f7#npm:7.26.0":
  locations:
    - "node_modules/@babel/plugin-syntax-flow"

"@babel/plugin-syntax-import-attributes@virtual:bd7c2a233f864186fabec5cf7921796bd19a54a835120a69358a508971959cb58b8f00f0882e8769c78e6e776a8f658587ddd0c12cf3aaca41f2babab4b8d518#npm:7.26.0":
  locations:
    - "node_modules/@babel/plugin-syntax-import-attributes"

"@babel/plugin-syntax-import-meta@virtual:bd7c2a233f864186fabec5cf7921796bd19a54a835120a69358a508971959cb58b8f00f0882e8769c78e6e776a8f658587ddd0c12cf3aaca41f2babab4b8d518#npm:7.10.4":
  locations:
    - "node_modules/@babel/plugin-syntax-import-meta"

"@babel/plugin-syntax-json-strings@virtual:bd7c2a233f864186fabec5cf7921796bd19a54a835120a69358a508971959cb58b8f00f0882e8769c78e6e776a8f658587ddd0c12cf3aaca41f2babab4b8d518#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-json-strings"

"@babel/plugin-syntax-jsx@virtual:46aef736f97a232e0399b0b37f9c3d83e767b676ab75c880c3c929f42945780009aa0a4a7d5e4f3ac772ba36fa2cdd8f99b10f3885bc47bf7bd7f09e29fde868#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-syntax-jsx"
  aliases:
    - "virtual:bb081a16d2161aef512e89bfa280d9f70cd0cdae04ae56c2326b8c2efc344743bee44987f51f71833fe249209c860628807dc11498c8816cf27b871df585cbff#npm:7.25.9"

"@babel/plugin-syntax-logical-assignment-operators@virtual:bd7c2a233f864186fabec5cf7921796bd19a54a835120a69358a508971959cb58b8f00f0882e8769c78e6e776a8f658587ddd0c12cf3aaca41f2babab4b8d518#npm:7.10.4":
  locations:
    - "node_modules/@babel/plugin-syntax-logical-assignment-operators"

"@babel/plugin-syntax-nullish-coalescing-operator@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-nullish-coalescing-operator"
  aliases:
    - "virtual:bd7c2a233f864186fabec5cf7921796bd19a54a835120a69358a508971959cb58b8f00f0882e8769c78e6e776a8f658587ddd0c12cf3aaca41f2babab4b8d518#npm:7.8.3"

"@babel/plugin-syntax-numeric-separator@virtual:bd7c2a233f864186fabec5cf7921796bd19a54a835120a69358a508971959cb58b8f00f0882e8769c78e6e776a8f658587ddd0c12cf3aaca41f2babab4b8d518#npm:7.10.4":
  locations:
    - "node_modules/@babel/plugin-syntax-numeric-separator"

"@babel/plugin-syntax-object-rest-spread@virtual:bd7c2a233f864186fabec5cf7921796bd19a54a835120a69358a508971959cb58b8f00f0882e8769c78e6e776a8f658587ddd0c12cf3aaca41f2babab4b8d518#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-object-rest-spread"

"@babel/plugin-syntax-optional-catch-binding@virtual:bd7c2a233f864186fabec5cf7921796bd19a54a835120a69358a508971959cb58b8f00f0882e8769c78e6e776a8f658587ddd0c12cf3aaca41f2babab4b8d518#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-optional-catch-binding"

"@babel/plugin-syntax-optional-chaining@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-optional-chaining"
  aliases:
    - "virtual:bd7c2a233f864186fabec5cf7921796bd19a54a835120a69358a508971959cb58b8f00f0882e8769c78e6e776a8f658587ddd0c12cf3aaca41f2babab4b8d518#npm:7.8.3"

"@babel/plugin-syntax-private-property-in-object@virtual:bd7c2a233f864186fabec5cf7921796bd19a54a835120a69358a508971959cb58b8f00f0882e8769c78e6e776a8f658587ddd0c12cf3aaca41f2babab4b8d518#npm:7.14.5":
  locations:
    - "node_modules/@babel/plugin-syntax-private-property-in-object"

"@babel/plugin-syntax-top-level-await@virtual:bd7c2a233f864186fabec5cf7921796bd19a54a835120a69358a508971959cb58b8f00f0882e8769c78e6e776a8f658587ddd0c12cf3aaca41f2babab4b8d518#npm:7.14.5":
  locations:
    - "node_modules/@babel/plugin-syntax-top-level-await"

"@babel/plugin-syntax-typescript@virtual:17fe13d7907907fa721dca87f4713ea4ddd08a8ba05e7c2eab72b9f14226d83dc8a953a8d249f89708d3fe47fd0d5be1cf67ca4f7cafa3316d6e739116fba77d#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-syntax-typescript"

"@babel/plugin-transform-arrow-functions@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-arrow-functions"

"@babel/plugin-transform-async-generator-functions@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.26.8":
  locations:
    - "node_modules/@babel/plugin-transform-async-generator-functions"

"@babel/plugin-transform-async-to-generator@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-async-to-generator"

"@babel/plugin-transform-block-scoping@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-block-scoping"

"@babel/plugin-transform-class-properties@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-class-properties"

"@babel/plugin-transform-classes@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-classes"

"@babel/plugin-transform-computed-properties@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-computed-properties"

"@babel/plugin-transform-destructuring@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-destructuring"

"@babel/plugin-transform-export-namespace-from@virtual:dc73448999d20739c62c8b95dee9efcd6bc601751b66c222699d35727b5caf48257eaa5370986b269e8a92860d46afab0c82782ba3c9ab36340e68baf1b47715#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-export-namespace-from"

"@babel/plugin-transform-flow-strip-types@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.26.5":
  locations:
    - "node_modules/@babel/plugin-transform-flow-strip-types"
  aliases:
    - "virtual:dc73448999d20739c62c8b95dee9efcd6bc601751b66c222699d35727b5caf48257eaa5370986b269e8a92860d46afab0c82782ba3c9ab36340e68baf1b47715#npm:7.26.5"

"@babel/plugin-transform-for-of@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.26.9":
  locations:
    - "node_modules/@babel/plugin-transform-for-of"

"@babel/plugin-transform-function-name@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-function-name"

"@babel/plugin-transform-literals@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-literals"

"@babel/plugin-transform-logical-assignment-operators@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-logical-assignment-operators"

"@babel/plugin-transform-modules-commonjs@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.26.3":
  locations:
    - "node_modules/@babel/plugin-transform-modules-commonjs"
  aliases:
    - "virtual:dc73448999d20739c62c8b95dee9efcd6bc601751b66c222699d35727b5caf48257eaa5370986b269e8a92860d46afab0c82782ba3c9ab36340e68baf1b47715#npm:7.26.3"

"@babel/plugin-transform-named-capturing-groups-regex@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-named-capturing-groups-regex"

"@babel/plugin-transform-nullish-coalescing-operator@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.26.6":
  locations:
    - "node_modules/@babel/plugin-transform-nullish-coalescing-operator"

"@babel/plugin-transform-numeric-separator@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-numeric-separator"

"@babel/plugin-transform-object-rest-spread@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-object-rest-spread"
  aliases:
    - "virtual:dc73448999d20739c62c8b95dee9efcd6bc601751b66c222699d35727b5caf48257eaa5370986b269e8a92860d46afab0c82782ba3c9ab36340e68baf1b47715#npm:7.25.9"

"@babel/plugin-transform-optional-catch-binding@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-optional-catch-binding"

"@babel/plugin-transform-optional-chaining@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-optional-chaining"

"@babel/plugin-transform-parameters@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-parameters"
  aliases:
    - "virtual:dc73448999d20739c62c8b95dee9efcd6bc601751b66c222699d35727b5caf48257eaa5370986b269e8a92860d46afab0c82782ba3c9ab36340e68baf1b47715#npm:7.25.9"

"@babel/plugin-transform-private-methods@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-private-methods"
  aliases:
    - "virtual:dc73448999d20739c62c8b95dee9efcd6bc601751b66c222699d35727b5caf48257eaa5370986b269e8a92860d46afab0c82782ba3c9ab36340e68baf1b47715#npm:7.25.9"

"@babel/plugin-transform-private-property-in-object@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-private-property-in-object"
  aliases:
    - "virtual:dc73448999d20739c62c8b95dee9efcd6bc601751b66c222699d35727b5caf48257eaa5370986b269e8a92860d46afab0c82782ba3c9ab36340e68baf1b47715#npm:7.25.9"

"@babel/plugin-transform-react-display-name@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-react-display-name"
  aliases:
    - "virtual:99900390c779a1c3c55779db2014afc5c8cd41ca26ae04f14c9b930bda46e2f2fe54ee8ac06694987b87941d3713e12981a9d72d8bb5aa4463734d7aaf2381f6#npm:7.25.9"

"@babel/plugin-transform-react-jsx-development@virtual:99900390c779a1c3c55779db2014afc5c8cd41ca26ae04f14c9b930bda46e2f2fe54ee8ac06694987b87941d3713e12981a9d72d8bb5aa4463734d7aaf2381f6#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-react-jsx-development"

"@babel/plugin-transform-react-jsx-self@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-react-jsx-self"

"@babel/plugin-transform-react-jsx-source@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-react-jsx-source"

"@babel/plugin-transform-react-jsx@virtual:4b5540d0755d71b727722f75d0998f7d1a1c397790df8d4b89af91a08e73b120351ed29e6760f8b370b0d967b4e5f3cf413adca2bebeef4a08c5e37c08d75738#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-react-jsx"
  aliases:
    - "virtual:99900390c779a1c3c55779db2014afc5c8cd41ca26ae04f14c9b930bda46e2f2fe54ee8ac06694987b87941d3713e12981a9d72d8bb5aa4463734d7aaf2381f6#npm:7.25.9"

"@babel/plugin-transform-react-pure-annotations@virtual:99900390c779a1c3c55779db2014afc5c8cd41ca26ae04f14c9b930bda46e2f2fe54ee8ac06694987b87941d3713e12981a9d72d8bb5aa4463734d7aaf2381f6#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-react-pure-annotations"

"@babel/plugin-transform-regenerator@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-regenerator"

"@babel/plugin-transform-runtime@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.26.9":
  locations:
    - "node_modules/@babel/plugin-transform-runtime"
  aliases:
    - "virtual:dc73448999d20739c62c8b95dee9efcd6bc601751b66c222699d35727b5caf48257eaa5370986b269e8a92860d46afab0c82782ba3c9ab36340e68baf1b47715#npm:7.26.9"

"@babel/plugin-transform-shorthand-properties@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-shorthand-properties"

"@babel/plugin-transform-spread@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-spread"

"@babel/plugin-transform-sticky-regex@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-sticky-regex"

"@babel/plugin-transform-typescript@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.26.8":
  locations:
    - "node_modules/@babel/plugin-transform-typescript"
  aliases:
    - "virtual:7c352417a72056fd956d24f5dd62f4ce9d02e0cfc8c8349b52d3dd60164a43b3bd98e2a3f7b48a147faf8950c763063405b0800169f52428c983b66bd3dfd448#npm:7.26.8"

"@babel/plugin-transform-unicode-regex@virtual:05010411367b099ba612ef0ef6ac99abf8e166e0f94cde7c27205ed7a244d7133f6628b4df72065d26b07fbe2b0dd90dcdb947cad8fd6012db565aef9c596062#npm:7.25.9":
  locations:
    - "node_modules/@babel/plugin-transform-unicode-regex"

"@babel/preset-react@virtual:dc73448999d20739c62c8b95dee9efcd6bc601751b66c222699d35727b5caf48257eaa5370986b269e8a92860d46afab0c82782ba3c9ab36340e68baf1b47715#npm:7.26.3":
  locations:
    - "node_modules/@babel/preset-react"

"@babel/preset-typescript@virtual:dc73448999d20739c62c8b95dee9efcd6bc601751b66c222699d35727b5caf48257eaa5370986b269e8a92860d46afab0c82782ba3c9ab36340e68baf1b47715#npm:7.26.0":
  locations:
    - "node_modules/@babel/preset-typescript"

"@babel/runtime@npm:7.26.9":
  locations:
    - "node_modules/@babel/runtime"

"@babel/template@npm:7.26.9":
  locations:
    - "node_modules/@babel/template"

"@babel/traverse@npm:7.26.9":
  locations:
    - "node_modules/@babel/traverse--for-generate-function-map"
    - "node_modules/@babel/traverse"

"@babel/types@npm:7.26.9":
  locations:
    - "node_modules/@babel/types"

"@biomejs/biome@npm:1.9.4":
  locations:
    - "node_modules/@biomejs/biome"

"@biomejs/cli-darwin-arm64@npm:1.9.4":
  locations:
    - "node_modules/@biomejs/cli-darwin-arm64"

"@colors/colors@npm:1.5.0":
  locations:
    - "node_modules/@colors/colors"

"@cspotcode/source-map-support@npm:0.8.1":
  locations:
    - "node_modules/@cspotcode/source-map-support"

"@edge-runtime/format@npm:2.2.1":
  locations:
    - "node_modules/@edge-runtime/format"

"@edge-runtime/node-utils@npm:2.3.0":
  locations:
    - "node_modules/@edge-runtime/node-utils"

"@edge-runtime/ponyfill@npm:2.4.2":
  locations:
    - "node_modules/@edge-runtime/ponyfill"

"@edge-runtime/primitives@npm:4.1.0":
  locations:
    - "node_modules/@edge-runtime/primitives"

"@edge-runtime/vm@npm:3.2.0":
  locations:
    - "node_modules/@edge-runtime/vm"

"@egjs/hammerjs@npm:2.0.17":
  locations:
    - "node_modules/@egjs/hammerjs"

"@emotion/is-prop-valid@npm:0.8.8":
  locations:
    - "node_modules/@emotion/is-prop-valid"

"@emotion/memoize@npm:0.7.4":
  locations:
    - "node_modules/@emotion/memoize"

"@esbuild/darwin-arm64@npm:0.21.5":
  locations:
    - "node_modules/vite/node_modules/@esbuild/darwin-arm64"

"@esbuild/darwin-arm64@npm:0.25.0":
  locations:
    - "node_modules/@esbuild/darwin-arm64"

"@eslint-community/eslint-utils@virtual:640fd1a7e40accbe3dbf6739da34ea099be98849681bb19149cbdb8c07e9e14d62292b7b19b78012d23d9d72ceb6c069a37befc94eb3f5268d74c3f4ae53e803#npm:4.4.1":
  locations:
    - "node_modules/@eslint-community/eslint-utils"
  aliases:
    - "virtual:9247781966e78ad66a8bb1c37baa033cd35e61575ca62c5fd5f5e6540360406a17c07e47520707f823ae7bdd7d7f3edf58cf12c511409664c3a94f0b0fdac547#npm:4.4.1"

"@eslint-community/regexpp@npm:4.12.1":
  locations:
    - "node_modules/@eslint-community/regexpp"

"@eslint/config-array@npm:0.19.2":
  locations:
    - "node_modules/@eslint/config-array"

"@eslint/core@npm:0.12.0":
  locations:
    - "node_modules/@eslint/core"

"@eslint/eslintrc@npm:3.3.0":
  locations:
    - "node_modules/@eslint/eslintrc"

"@eslint/js@npm:9.21.0":
  locations:
    - "node_modules/@eslint/js"

"@eslint/object-schema@npm:2.1.6":
  locations:
    - "node_modules/@eslint/object-schema"

"@eslint/plugin-kit@npm:0.2.7":
  locations:
    - "node_modules/@eslint/plugin-kit"

"@expo/cli@npm:0.24.13":
  locations:
    - "node_modules/@expo/cli"

"@expo/code-signing-certificates@npm:0.0.5":
  locations:
    - "node_modules/@expo/code-signing-certificates"

"@expo/config-plugins@npm:10.0.2":
  locations:
    - "node_modules/@expo/config-plugins"

"@expo/config-types@npm:53.0.4":
  locations:
    - "node_modules/@expo/config-types"

"@expo/config@npm:11.0.10":
  locations:
    - "node_modules/@expo/config"

"@expo/devcert@npm:1.1.4":
  locations:
    - "node_modules/@expo/devcert"

"@expo/env@npm:1.0.5":
  locations:
    - "node_modules/@expo/env"

"@expo/fingerprint@npm:0.12.4":
  locations:
    - "node_modules/@expo/fingerprint"

"@expo/image-utils@npm:0.7.4":
  locations:
    - "node_modules/@expo/image-utils"

"@expo/json-file@npm:9.1.4":
  locations:
    - "node_modules/@expo/json-file"

"@expo/metro-config@npm:0.20.14":
  locations:
    - "node_modules/@expo/metro-config"

"@expo/metro-runtime@virtual:62d7ccfbe42a92b0c86ed78a084acf28912cb76833266654a0599e8b0f666fa079f7834495bcc7acdd02770b85d65cd77f70aaa34d4ebc8234c988a9bb2fc053#npm:5.0.4":
  locations:
    - "node_modules/@expo/metro-runtime"

"@expo/osascript@npm:2.2.4":
  locations:
    - "node_modules/@expo/osascript"

"@expo/package-manager@npm:1.8.4":
  locations:
    - "node_modules/@expo/package-manager"

"@expo/plist@npm:0.3.4":
  locations:
    - "node_modules/@expo/plist"

"@expo/prebuild-config@npm:9.0.6":
  locations:
    - "node_modules/@expo/prebuild-config"

"@expo/sdk-runtime-versions@npm:1.0.0":
  locations:
    - "node_modules/@expo/sdk-runtime-versions"

"@expo/server@npm:0.6.2":
  locations:
    - "node_modules/@expo/server"

"@expo/spawn-async@npm:1.7.2":
  locations:
    - "node_modules/@expo/spawn-async"

"@expo/vector-icons@npm:14.0.4":
  locations:
    - "node_modules/@expo/vector-icons"

"@expo/ws-tunnel@npm:1.0.5":
  locations:
    - "node_modules/@expo/ws-tunnel"

"@expo/xcpretty@npm:4.3.2":
  locations:
    - "node_modules/@expo/xcpretty"

"@fastify/busboy@npm:2.1.1":
  locations:
    - "node_modules/@fastify/busboy"

"@floating-ui/core@npm:1.6.9":
  locations:
    - "node_modules/@floating-ui/core"

"@floating-ui/dom@npm:1.6.13":
  locations:
    - "node_modules/@floating-ui/dom"

"@floating-ui/react-dom@virtual:2d986783f52ea4e3e99cc097c5457def26d67b2946dc18ae05892ec3e0abce72bd455a03d5adf56ee190e529cb0c87bf989785c24d70b8c014d921c00e2432f6#npm:2.1.3":
  locations:
    - "node_modules/@floating-ui/react-dom"

"@floating-ui/react-native@virtual:5da3869255527c3f2b853561e747360b71fc2d4f90a7a26b79d79f39b4f133e0130f2b764a9329b172b9838dc718880dde1bb696bd7b954027f18bd68a2baf0e#npm:0.10.7":
  locations:
    - "node_modules/@floating-ui/react-native"

"@floating-ui/react@virtual:3deb1344b23f164a1cb9549400c87ceaf9182d05ed57496b101a7a38e101ed195788a92015b56119ce6d32a7375ece5e8f6a0e0e50d1bd8ceb4554f0a54785aa#npm:0.27.12":
  locations:
    - "node_modules/@floating-ui/react"

"@floating-ui/utils@npm:0.2.9":
  locations:
    - "node_modules/@floating-ui/utils"

"@humanfs/core@npm:0.19.1":
  locations:
    - "node_modules/@humanfs/core"

"@humanfs/node@npm:0.16.6":
  locations:
    - "node_modules/@humanfs/node"

"@humanwhocodes/module-importer@npm:1.0.1":
  locations:
    - "node_modules/@humanwhocodes/module-importer"

"@humanwhocodes/retry@npm:0.3.1":
  locations:
    - "node_modules/@humanwhocodes/retry"

"@humanwhocodes/retry@npm:0.4.2":
  locations:
    - "node_modules/eslint/node_modules/@humanwhocodes/retry"

"@isaacs/cliui@npm:8.0.2":
  locations:
    - "node_modules/@isaacs/cliui"

"@isaacs/fs-minipass@npm:4.0.1":
  locations:
    - "node_modules/@isaacs/fs-minipass"

"@isaacs/ttlcache@npm:1.4.1":
  locations:
    - "node_modules/@isaacs/ttlcache"

"@istanbuljs/load-nyc-config@npm:1.1.0":
  locations:
    - "node_modules/@istanbuljs/load-nyc-config"

"@istanbuljs/schema@npm:0.1.3":
  locations:
    - "node_modules/@istanbuljs/schema"

"@jest/create-cache-key-function@npm:29.7.0":
  locations:
    - "node_modules/@jest/create-cache-key-function"

"@jest/environment@npm:29.7.0":
  locations:
    - "node_modules/@jest/environment"

"@jest/fake-timers@npm:29.7.0":
  locations:
    - "node_modules/@jest/fake-timers"

"@jest/schemas@npm:29.6.3":
  locations:
    - "node_modules/@jest/schemas"

"@jest/transform@npm:29.7.0":
  locations:
    - "node_modules/@jest/transform"

"@jest/types@npm:29.6.3":
  locations:
    - "node_modules/@jest/types"

"@jridgewell/gen-mapping@npm:0.3.8":
  locations:
    - "node_modules/@jridgewell/gen-mapping"

"@jridgewell/resolve-uri@npm:3.1.2":
  locations:
    - "node_modules/@jridgewell/resolve-uri"

"@jridgewell/set-array@npm:1.2.1":
  locations:
    - "node_modules/@jridgewell/set-array"

"@jridgewell/source-map@npm:0.3.6":
  locations:
    - "node_modules/@jridgewell/source-map"

"@jridgewell/sourcemap-codec@npm:1.5.0":
  locations:
    - "node_modules/@jridgewell/sourcemap-codec"

"@jridgewell/trace-mapping@npm:0.3.25":
  locations:
    - "node_modules/@jridgewell/trace-mapping"

"@jridgewell/trace-mapping@npm:0.3.9":
  locations:
    - "node_modules/@cspotcode/source-map-support/node_modules/@jridgewell/trace-mapping"

"@mapbox/node-pre-gyp@npm:2.0.0":
  locations:
    - "node_modules/@mapbox/node-pre-gyp"

"@motionone/animation@npm:10.18.0":
  locations:
    - "node_modules/@motionone/animation"

"@motionone/dom@npm:10.12.0":
  locations:
    - "node_modules/@motionone/dom"

"@motionone/easing@npm:10.18.0":
  locations:
    - "node_modules/@motionone/easing"

"@motionone/generators@npm:10.18.0":
  locations:
    - "node_modules/@motionone/generators"

"@motionone/types@npm:10.17.1":
  locations:
    - "node_modules/@motionone/types"

"@motionone/utils@npm:10.18.0":
  locations:
    - "node_modules/@motionone/utils"

"@my/config@workspace:packages/config":
  locations:
    - "node_modules/@my/config"

"@my/ui@workspace:packages/ui":
  locations:
    - "node_modules/@my/ui"

"@next/env@npm:14.2.14":
  locations:
    - "node_modules/@next/env"

"@next/eslint-plugin-next@npm:14.2.24":
  locations:
    - "node_modules/@next/eslint-plugin-next"

"@next/swc-darwin-arm64@npm:14.2.14":
  locations:
    - "node_modules/@next/swc-darwin-arm64"

"@nodelib/fs.scandir@npm:2.1.5":
  locations:
    - "node_modules/@nodelib/fs.scandir"

"@nodelib/fs.stat@npm:2.0.5":
  locations:
    - "node_modules/@nodelib/fs.stat"

"@nodelib/fs.walk@npm:1.2.8":
  locations:
    - "node_modules/@nodelib/fs.walk"

"@nolyfill/is-core-module@npm:1.0.39":
  locations:
    - "node_modules/@nolyfill/is-core-module"

"@npmcli/agent@npm:3.0.0":
  locations:
    - "node_modules/@npmcli/agent"

"@npmcli/fs@npm:4.0.0":
  locations:
    - "node_modules/@npmcli/fs"

"@oxc-transform/binding-darwin-arm64@npm:0.74.0":
  locations:
    - "node_modules/@oxc-transform/binding-darwin-arm64"

"@pkgjs/parseargs@npm:0.11.0":
  locations:
    - "node_modules/@pkgjs/parseargs"

"@radix-ui/react-compose-refs@virtual:3f26aa867afb6b9ae8f04d9356342bfb55e2f10475d28e726d6378b953e64567bd9a572a26571f940686aa81d024e882556e70adccdac7af7071d02e327288d1#npm:1.1.2":
  locations:
    - "node_modules/@radix-ui/react-compose-refs"

"@radix-ui/react-slot@virtual:62d7ccfbe42a92b0c86ed78a084acf28912cb76833266654a0599e8b0f666fa079f7834495bcc7acdd02770b85d65cd77f70aaa34d4ebc8234c988a9bb2fc053#npm:1.2.0":
  locations:
    - "node_modules/@radix-ui/react-slot"

"@react-native/assets-registry@npm:0.79.2":
  locations:
    - "node_modules/@react-native/assets-registry"

"@react-native/babel-plugin-codegen@npm:0.79.2":
  locations:
    - "node_modules/@react-native/babel-plugin-codegen"

"@react-native/babel-preset@virtual:dc73448999d20739c62c8b95dee9efcd6bc601751b66c222699d35727b5caf48257eaa5370986b269e8a92860d46afab0c82782ba3c9ab36340e68baf1b47715#npm:0.79.2":
  locations:
    - "node_modules/@react-native/babel-preset"

"@react-native/codegen@virtual:64b0d24e78fb24a4d23b5fdd0f59007fc38be9e523e31719c695c39d5279857d4201572683e08da89dea76f2678d593d9b2025b691a1da465e3fb941150dddce#npm:0.79.2":
  locations:
    - "node_modules/@react-native/codegen"

"@react-native/community-cli-plugin@virtual:64b0d24e78fb24a4d23b5fdd0f59007fc38be9e523e31719c695c39d5279857d4201572683e08da89dea76f2678d593d9b2025b691a1da465e3fb941150dddce#npm:0.79.2":
  locations:
    - "node_modules/@react-native/community-cli-plugin"

"@react-native/debugger-frontend@npm:0.79.2":
  locations:
    - "node_modules/@react-native/debugger-frontend"

"@react-native/dev-middleware@npm:0.79.2":
  locations:
    - "node_modules/@react-native/dev-middleware"

"@react-native/gradle-plugin@npm:0.79.2":
  locations:
    - "node_modules/@react-native/gradle-plugin"

"@react-native/js-polyfills@npm:0.79.2":
  locations:
    - "node_modules/@react-native/js-polyfills"

"@react-native/normalize-color@npm:2.1.0":
  locations:
    - "node_modules/@react-native/normalize-color"

"@react-native/normalize-colors@npm:0.74.89":
  locations:
    - "node_modules/react-native-web/node_modules/@react-native/normalize-colors"

"@react-native/normalize-colors@npm:0.79.2":
  locations:
    - "node_modules/@react-native/normalize-colors"

"@react-native/virtualized-lists@virtual:13c6c791295c2e29781d066991b2fa922d7d10d43868dd6ae6aec699291481069a33a93dd24c7b4d9dbc775032432ac8b758ee4e764b72c08b5b6ca7dd5c86c5#npm:0.79.2":
  locations:
    - "node_modules/@react-native/virtualized-lists"

"@react-navigation/bottom-tabs@virtual:62d7ccfbe42a92b0c86ed78a084acf28912cb76833266654a0599e8b0f666fa079f7834495bcc7acdd02770b85d65cd77f70aaa34d4ebc8234c988a9bb2fc053#npm:7.3.12":
  locations:
    - "node_modules/@react-navigation/bottom-tabs"
  aliases:
    - "virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:7.3.12"

"@react-navigation/core@virtual:41b25f5d530f179e14c3799b30e5175cdbc6519093c3d16911c5890be0ae628e6d6e9671c6a71a68832a3a17ce9983818d78c85783849f310ea1baac5fb0aa3e#npm:6.4.17":
  locations:
    - "packages/app/node_modules/@react-navigation/core"

"@react-navigation/core@virtual:66fe7c09a5b721f1dd87e49b838d226b04a666b22191a377206f1c74503da20fde0da1a2a7942319ff620563c2e9c33deaa5c2451c966009e008791b84b9a706#npm:7.9.1":
  locations:
    - "node_modules/@react-navigation/core"

"@react-navigation/elements@virtual:18efbca07d82ba8f809f7d5d0f8a489d245170a0bc093b1ba8540c6a1f27e7892a2288d7b913296b4973276a77e04d559f75ff3e40aca3510cbf07c0f1b42476#npm:2.4.1":
  locations:
    - "node_modules/@react-navigation/elements"
  aliases:
    - "virtual:a885b3c148caab1d9e309a457ea2b8b6bd987c0318e9e948d48e56498aa5978bf2a2d6e86d9b8c3fb3692818f22978339024e305725a6ad078f829b4b2f38bba#npm:2.4.1"

"@react-navigation/native-stack@virtual:62d7ccfbe42a92b0c86ed78a084acf28912cb76833266654a0599e8b0f666fa079f7834495bcc7acdd02770b85d65cd77f70aaa34d4ebc8234c988a9bb2fc053#npm:7.3.12":
  locations:
    - "node_modules/@react-navigation/native-stack"

"@react-navigation/native@virtual:62d7ccfbe42a92b0c86ed78a084acf28912cb76833266654a0599e8b0f666fa079f7834495bcc7acdd02770b85d65cd77f70aaa34d4ebc8234c988a9bb2fc053#npm:7.1.8":
  locations:
    - "node_modules/@react-navigation/native"
  aliases:
    - "virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:7.1.8"

"@react-navigation/native@virtual:f87a972e7ee54256c6d8f979d7f3914b32522893226eba595e4efe4ecc641a239c6d88e01eccc6f32db30829d6ac493bfc98cb406a9b0d6059ee4112c0843da9#npm:6.1.18":
  locations:
    - "packages/app/node_modules/@react-navigation/native"

"@react-navigation/routers@npm:6.1.9":
  locations:
    - "packages/app/node_modules/@react-navigation/routers"

"@react-navigation/routers@npm:7.3.7":
  locations:
    - "node_modules/@react-navigation/routers"

"@rollup/pluginutils@virtual:0a569de48427967cf41a8f6e26e07e32ca821b4018672603fe0be4bf2f5846b12157401579435b54dec3acfcb4a704d1ba7be93bebd847a94b7121bf4aa10815#npm:5.1.4":
  locations:
    - "node_modules/@rollup/pluginutils"

"@rollup/rollup-darwin-arm64@npm:4.34.8":
  locations:
    - "node_modules/@rollup/rollup-darwin-arm64"

"@rtsao/scc@npm:1.1.0":
  locations:
    - "node_modules/@rtsao/scc"

"@rushstack/eslint-patch@npm:1.10.5":
  locations:
    - "node_modules/@rushstack/eslint-patch"

"@sinclair/typebox@npm:0.25.24":
  locations:
    - "node_modules/@vercel/gatsby-plugin-vercel-builder/node_modules/@sinclair/typebox"

"@sinclair/typebox@npm:0.27.8":
  locations:
    - "node_modules/@sinclair/typebox"

"@sinonjs/commons@npm:3.0.1":
  locations:
    - "node_modules/@sinonjs/commons"

"@sinonjs/fake-timers@npm:10.3.0":
  locations:
    - "node_modules/@sinonjs/fake-timers"

"@swc/core-darwin-arm64@npm:1.11.4":
  locations:
    - "node_modules/@swc/core-darwin-arm64"

"@swc/core@virtual:6bc04efe15e1ad85c53c118c6fc56caa4d4db47f90d155f84df68bbf9ef305e9bcf9c5fe2ebe91e8243cc1e2f1711f36cc691a8e9a4134839b4e8efc55fb4032#npm:1.11.4":
  locations:
    - "node_modules/@swc/core"
  aliases:
    - "virtual:a7259b19df7980a4162889855ffdd5ee389c65b78b3a2dda44f7d7b99bd1625b4ecc264842b908d3f557b484eb0c26dde98d10045ab1732248a33b3ea76173c9#npm:1.11.4"

"@swc/counter@npm:0.1.3":
  locations:
    - "node_modules/@swc/counter"

"@swc/helpers@npm:0.5.15":
  locations:
    - "node_modules/@swc/helpers"

"@swc/helpers@npm:0.5.5":
  locations:
    - "node_modules/next/node_modules/@swc/helpers"

"@swc/types@npm:0.1.19":
  locations:
    - "node_modules/@swc/types"

"@tamagui/accordion@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/accordion"

"@tamagui/adapt@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/adapt"

"@tamagui/alert-dialog@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/alert-dialog"

"@tamagui/animate-presence@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/animate-presence"

"@tamagui/animate@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/animate"

"@tamagui/animations-css@virtual:de430c1d0a7a33e9d6782bff3ce0c7ddc6eed8a0c83bf556e65fdd53f043fad30664fe74153840e89db4e679dff5d799900e1874292f056470e9b7d81bafab17#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/animations-css"

"@tamagui/animations-moti@virtual:3d3a5b5f7cb5937fea0bf5340f8492015c56c00304e4a1918d7b77c5d0b5c2828b90ae0951985cce0123814c81417854ec1901f02be51cad531f0dad1b76236c#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/animations-moti"

"@tamagui/animations-react-native@virtual:849bf4008bec072a51e443fc3fc587a97b15006d54e5799e209e25dc3121d34d6a620e55950605ff8cb660fb76903a483e9450051d53c93a2a3c2e4d9b99e0e5#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/animations-react-native"
  aliases:
    - "virtual:e34c95b43a8ca7eaa9ebdcf181cba5465fec9c487fa9fd68d2b50037ff2e2cda0e67c5ca42fbc7f05f80a6acd09921a4d9c1adba2c2e683eb34459dac32822c4#npm:1.129.19"
    - "virtual:f87a972e7ee54256c6d8f979d7f3914b32522893226eba595e4efe4ecc641a239c6d88e01eccc6f32db30829d6ac493bfc98cb406a9b0d6059ee4112c0843da9#npm:1.129.19"

"@tamagui/aria-hidden@virtual:80be4b5a7af1f11ef0a5ead9331a52e348ae432cafbf8e20fbc0d4cf61b4f9b121dfb39b01c703d3153743066f3471c2d930c66e6abf299be83eebfb7b925fb3#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/aria-hidden"

"@tamagui/avatar@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/avatar"

"@tamagui/babel-plugin-fully-specified@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/babel-plugin-fully-specified"

"@tamagui/babel-plugin@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/babel-plugin"

"@tamagui/build@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/build"

"@tamagui/button@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/button"

"@tamagui/card@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/card"

"@tamagui/checkbox-headless@virtual:373edb1c7b889bfb6b3ac055100d48a9466df07352116d05e7aad92af35018fac40d52cba298f810cf459a7808d297c650e05f48b3340353ad132fe19eece5a3#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/checkbox-headless"

"@tamagui/checkbox@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/checkbox"

"@tamagui/cli-color@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/cli-color"

"@tamagui/cli@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/cli"

"@tamagui/collapsible@virtual:dee06e83ca0806b5fa2c37ae37583bfc6d0ac2ef32d2b0664cb52855462b8efc4c070b2ac70bc7124d052918751238a1bd6b4f55eff15b642e7639d4574712a7#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/collapsible"

"@tamagui/collection@virtual:dee06e83ca0806b5fa2c37ae37583bfc6d0ac2ef32d2b0664cb52855462b8efc4c070b2ac70bc7124d052918751238a1bd6b4f55eff15b642e7639d4574712a7#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/collection"

"@tamagui/colors@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/colors"

"@tamagui/compose-refs@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/compose-refs"
  aliases:
    - "virtual:5c206461a6a510457b03332e63ce19c5ef1f21a6a8ef282122a9169db40d011e92ee3fa43d93b3f1f8cce56d708023099bc7e7a2c424cf802e99cd1c78ff8ba7#npm:1.129.19"

"@tamagui/config-default@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/config-default"

"@tamagui/config@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/config"

"@tamagui/constants@virtual:51be93addb47a425de7f66d6a4c0add0b8de94088c71aabb7b85d7e29fef9c26bdd72e86beeb0e6b7ec343618212cf642e987ef0baeca606fc659737084307b9#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/constants"
  aliases:
    - "virtual:59e0c0e292e4a40fe1b1e6454f273c590ce9f9e011bced18db94b9ac7ac2b5b2910b229af29c767fa51ecd41d94a9ab023573371772c192c74f1ac2f7cf6094e#npm:1.129.19"

"@tamagui/core@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/core"
  aliases:
    - "virtual:a8a0d1b6b4ded8aef7a81ba24a2ae07e54db58fc4715caed3dfbaabdf314ea32ccf5111a5e5296089bfa5e56e58323ad743e77459a5ee970457f9fc65525261c#npm:1.129.19"
    - "virtual:de430c1d0a7a33e9d6782bff3ce0c7ddc6eed8a0c83bf556e65fdd53f043fad30664fe74153840e89db4e679dff5d799900e1874292f056470e9b7d81bafab17#npm:1.129.19"

"@tamagui/create-context@virtual:009d6623e6bd7415eb5c0e44b71341c30cb0ea66c4bcb8becc333bc141c63aab55432b3f328c1fcfcf79d8749eb811826db62b805610cdcd9cdb0d5f403a8246#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/create-context"
  aliases:
    - "virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19"

"@tamagui/create-theme@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/create-theme"

"@tamagui/cubic-bezier-animator@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/cubic-bezier-animator"

"@tamagui/dialog@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/dialog"

"@tamagui/dismissable@virtual:80be4b5a7af1f11ef0a5ead9331a52e348ae432cafbf8e20fbc0d4cf61b4f9b121dfb39b01c703d3153743066f3471c2d930c66e6abf299be83eebfb7b925fb3#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/dismissable"
  aliases:
    - "virtual:8e72d298e4be3f93edd78b5f9115e64ba4ee15c9dfca55c777398c8a1605d7e5afb9242e938e9ae4746be088c6ccd098679763eab0d088daca391e8f5a67f404#npm:1.129.19"

"@tamagui/elements@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/elements"

"@tamagui/fake-react-native@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/fake-react-native"

"@tamagui/floating@virtual:3deb1344b23f164a1cb9549400c87ceaf9182d05ed57496b101a7a38e101ed195788a92015b56119ce6d32a7375ece5e8f6a0e0e50d1bd8ceb4554f0a54785aa#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/floating"

"@tamagui/focus-scope@virtual:80be4b5a7af1f11ef0a5ead9331a52e348ae432cafbf8e20fbc0d4cf61b4f9b121dfb39b01c703d3153743066f3471c2d930c66e6abf299be83eebfb7b925fb3#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/focus-scope"

"@tamagui/focusable@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/focusable"

"@tamagui/font-inter@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/font-inter"

"@tamagui/font-silkscreen@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/font-silkscreen"

"@tamagui/font-size@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/font-size"

"@tamagui/form@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/form"

"@tamagui/generate-themes@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/generate-themes"

"@tamagui/get-button-sized@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/get-button-sized"

"@tamagui/get-font-sized@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/get-font-sized"

"@tamagui/get-token@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/get-token"

"@tamagui/group@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/group"

"@tamagui/helpers-icon@virtual:a8a0d1b6b4ded8aef7a81ba24a2ae07e54db58fc4715caed3dfbaabdf314ea32ccf5111a5e5296089bfa5e56e58323ad743e77459a5ee970457f9fc65525261c#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/helpers-icon"

"@tamagui/helpers-node@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/helpers-node"

"@tamagui/helpers-tamagui@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/helpers-tamagui"

"@tamagui/helpers@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/helpers"

"@tamagui/image@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/image"

"@tamagui/is-equal-shallow@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/is-equal-shallow"

"@tamagui/label@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/label"

"@tamagui/linear-gradient@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/linear-gradient"

"@tamagui/list-item@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/list-item"

"@tamagui/lucide-icons@virtual:f87a972e7ee54256c6d8f979d7f3914b32522893226eba595e4efe4ecc641a239c6d88e01eccc6f32db30829d6ac493bfc98cb406a9b0d6059ee4112c0843da9#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/lucide-icons"

"@tamagui/next-plugin@virtual:1e21ed5e542f04ac0725f80f20538bd288a2542912556a71424e0ef86756859fcedbc2507675d93c28d09cc2e230d8793e424a169e257d21ba5cb20fce2d2238#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/next-plugin"

"@tamagui/next-theme@virtual:1e21ed5e542f04ac0725f80f20538bd288a2542912556a71424e0ef86756859fcedbc2507675d93c28d09cc2e230d8793e424a169e257d21ba5cb20fce2d2238#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/next-theme"

"@tamagui/normalize-css-color@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/normalize-css-color"

"@tamagui/polyfill-dev@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/polyfill-dev"

"@tamagui/popover@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/popover"

"@tamagui/popper@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/popper"

"@tamagui/portal@virtual:5d1a822391bf9a8e41c34a1bc7275c350f19eefafd375c1cda7f808351676c69617bc18bd824857be8b609ebc4e0b00b44662021746f30a859c449f0a0f88da1#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/portal"

"@tamagui/progress@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/progress"

"@tamagui/proxy-worm@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/proxy-worm"

"@tamagui/radio-group@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/radio-group"

"@tamagui/radio-headless@virtual:3ce6e27553856dd9cba3bb49249ea6935d2d308a5b8516257fc844f70750902b0ba16e6227802a9c8eae847847387520987a656887555b53a4cf749ddd79a696#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/radio-headless"

"@tamagui/react-native-media-driver@virtual:3f1ba6813f77486ab3a1b43833800e85810df209605d7ccda2d858dd52f374abc38cb505ab22b0e67a12e733f26361f995d3b0e02f3d3bd8abab6867194806be#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/react-native-media-driver"

"@tamagui/react-native-svg@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/react-native-svg"

"@tamagui/react-native-use-pressable@virtual:3f1ba6813f77486ab3a1b43833800e85810df209605d7ccda2d858dd52f374abc38cb505ab22b0e67a12e733f26361f995d3b0e02f3d3bd8abab6867194806be#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/react-native-use-pressable"

"@tamagui/react-native-use-responder-events@virtual:3f1ba6813f77486ab3a1b43833800e85810df209605d7ccda2d858dd52f374abc38cb505ab22b0e67a12e733f26361f995d3b0e02f3d3bd8abab6867194806be#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/react-native-use-responder-events"

"@tamagui/react-native-web-internals@virtual:4b5540d0755d71b727722f75d0998f7d1a1c397790df8d4b89af91a08e73b120351ed29e6760f8b370b0d967b4e5f3cf413adca2bebeef4a08c5e37c08d75738#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/react-native-web-internals"

"@tamagui/react-native-web-lite@virtual:4b5540d0755d71b727722f75d0998f7d1a1c397790df8d4b89af91a08e73b120351ed29e6760f8b370b0d967b4e5f3cf413adca2bebeef4a08c5e37c08d75738#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/react-native-web-lite"

"@tamagui/remove-scroll@virtual:80be4b5a7af1f11ef0a5ead9331a52e348ae432cafbf8e20fbc0d4cf61b4f9b121dfb39b01c703d3153743066f3471c2d930c66e6abf299be83eebfb7b925fb3#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/remove-scroll"

"@tamagui/roving-focus@virtual:3ce6e27553856dd9cba3bb49249ea6935d2d308a5b8516257fc844f70750902b0ba16e6227802a9c8eae847847387520987a656887555b53a4cf749ddd79a696#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/roving-focus"

"@tamagui/scroll-view@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/scroll-view"

"@tamagui/select@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/select"

"@tamagui/separator@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/separator"

"@tamagui/shapes@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/shapes"

"@tamagui/sheet@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/sheet"

"@tamagui/shorthands@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/shorthands"

"@tamagui/simple-hash@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/simple-hash"

"@tamagui/slider@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/slider"

"@tamagui/stacks@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/stacks"
  aliases:
    - "virtual:d2cb85ea7a33a485ed3e542f79c187a299b636f7442b479c4d1bfed3db42669545ecff7d04052145b277a1760a26c8e301a1ec3f49bb3af2cbb9dd786ed58111#npm:1.129.19"

"@tamagui/start-transition@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/start-transition"

"@tamagui/static@virtual:2b362252813ee0568c9f504c4a15e653428554df3f5dcbc679c51f64b5ec1ae40f91d1c4376ca2b32eab8915cab5e421a677511bbbbecd577495c7adf3499f5c#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/static"

"@tamagui/switch-headless@virtual:312694b7a94917573d360f81a8271a0ca78d63c6b0b05032d55022aab92e78ca81760f176aeddec196cf05c86bdd4afae7563b81a93246dfbd2ccdc19c711424#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/switch-headless"

"@tamagui/switch@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/switch"

"@tamagui/tabs@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/tabs"

"@tamagui/text@virtual:009d6623e6bd7415eb5c0e44b71341c30cb0ea66c4bcb8becc333bc141c63aab55432b3f328c1fcfcf79d8749eb811826db62b805610cdcd9cdb0d5f403a8246#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/text"
  aliases:
    - "virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19"

"@tamagui/theme-builder@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/theme-builder"

"@tamagui/theme@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/theme"

"@tamagui/themes@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/themes"

"@tamagui/timer@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/timer"

"@tamagui/toast@virtual:73ee2411d8f52125a46c15d78d90eb3683df15f09ba5387527c9d65fc8bfe6e2027c1d4d08484a6b8c6ceb45230a532010f714da469b058e7303bb4ebd3cac01#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/toast"

"@tamagui/toggle-group@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/toggle-group"

"@tamagui/tooltip@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/tooltip"

"@tamagui/types@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/types"

"@tamagui/use-async@virtual:3645c9e1bcdc953704a66ed93d3e10046d180accf8c9d7dd094ca7bbc86649299553851880c72360c42b0034884284d733b65b962ca61dd6d556abc96dcf602c#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/use-async"

"@tamagui/use-callback-ref@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/use-callback-ref"

"@tamagui/use-constant@virtual:696a5d7ae743d732cd17e8e861246300960e3e1243adf482563fa8ec5f6011d8ef8d6eb4d8b8827cc18805e77ce1b462184695bef8483c2676dbaca2052c437b#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/use-constant"
  aliases:
    - "virtual:849bf4008bec072a51e443fc3fc587a97b15006d54e5799e209e25dc3121d34d6a620e55950605ff8cb660fb76903a483e9450051d53c93a2a3c2e4d9b99e0e5#npm:1.129.19"

"@tamagui/use-controllable-state@virtual:009d6623e6bd7415eb5c0e44b71341c30cb0ea66c4bcb8becc333bc141c63aab55432b3f328c1fcfcf79d8749eb811826db62b805610cdcd9cdb0d5f403a8246#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/use-controllable-state"
  aliases:
    - "virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19"

"@tamagui/use-debounce@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/use-debounce"

"@tamagui/use-did-finish-ssr@virtual:5c206461a6a510457b03332e63ce19c5ef1f21a6a8ef282122a9169db40d011e92ee3fa43d93b3f1f8cce56d708023099bc7e7a2c424cf802e99cd1c78ff8ba7#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/use-did-finish-ssr"
  aliases:
    - "virtual:c7b586b218fd91d4edd480c8befd2e9cd5fad15ddd42694addd28eeccad06aa7b1fb69e9bc04ce05cf15867f2bd2b3f7b1047153dbd6055868e255f628cb47e5#npm:1.129.19"

"@tamagui/use-direction@virtual:d1001677fb021c666c0c4605c2a91c23b567d6a7938a887a97a973693eb41066c089ced69bb42626d27b36fabf6af50aeb865d58bba7c9660e00772d19e3dc82#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/use-direction"

"@tamagui/use-element-layout@virtual:3f1ba6813f77486ab3a1b43833800e85810df209605d7ccda2d858dd52f374abc38cb505ab22b0e67a12e733f26361f995d3b0e02f3d3bd8abab6867194806be#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/use-element-layout"

"@tamagui/use-escape-keydown@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/use-escape-keydown"

"@tamagui/use-event@virtual:59e0c0e292e4a40fe1b1e6454f273c590ce9f9e011bced18db94b9ac7ac2b5b2910b229af29c767fa51ecd41d94a9ab023573371772c192c74f1ac2f7cf6094e#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/use-event"
  aliases:
    - "virtual:5c206461a6a510457b03332e63ce19c5ef1f21a6a8ef282122a9169db40d011e92ee3fa43d93b3f1f8cce56d708023099bc7e7a2c424cf802e99cd1c78ff8ba7#npm:1.129.19"

"@tamagui/use-force-update@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/use-force-update"
  aliases:
    - "virtual:5c206461a6a510457b03332e63ce19c5ef1f21a6a8ef282122a9169db40d011e92ee3fa43d93b3f1f8cce56d708023099bc7e7a2c424cf802e99cd1c78ff8ba7#npm:1.129.19"

"@tamagui/use-keyboard-visible@virtual:849bf4008bec072a51e443fc3fc587a97b15006d54e5799e209e25dc3121d34d6a620e55950605ff8cb660fb76903a483e9450051d53c93a2a3c2e4d9b99e0e5#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/use-keyboard-visible"

"@tamagui/use-presence@virtual:746dfea7060053a1f14574c86fb38f30f6c0ecb84efabeab18d6dfad60ec8bd80366bf0d43182cbc80261d167808e09ddaadeb5d73a807593246a48826befb3c#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/use-presence"

"@tamagui/use-previous@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/use-previous"

"@tamagui/use-window-dimensions@virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/use-window-dimensions"

"@tamagui/visually-hidden@virtual:009d6623e6bd7415eb5c0e44b71341c30cb0ea66c4bcb8becc333bc141c63aab55432b3f328c1fcfcf79d8749eb811826db62b805610cdcd9cdb0d5f403a8246#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/visually-hidden"
  aliases:
    - "virtual:147a8aa7284313f2298262f6100bfe7cff7a649fd851d4dc3a81ddb5152c967ad67c5a01f242d648fd222980ddaad08934d2e5e4911d8dc7628409b4f3f2487d#npm:1.129.19"

"@tamagui/vite-plugin@virtual:2b362252813ee0568c9f504c4a15e653428554df3f5dcbc679c51f64b5ec1ae40f91d1c4376ca2b32eab8915cab5e421a677511bbbbecd577495c7adf3499f5c#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/vite-plugin"

"@tamagui/web@virtual:91aa4c7867e8a828f25dd7023d2b5624b769a5b702f0c8814a43885a31b5d5c0e415aff4ccd92d2547cc2543f25347e1239049a39525f251fc5df91bb009b2fe#npm:1.129.19":
  locations:
    - "node_modules/@tamagui/web"
  aliases:
    - "virtual:9cf2c470ab606088a4b1a2d99ef8730aca50758410310cdeb9e0c23b2a2620da06402144147f985188dc997ff80b80c0a652d399f818bece03209bc12c536ea1#npm:1.129.19"

"@tamagui/z-index-stack@npm:1.129.19":
  locations:
    - "node_modules/@tamagui/z-index-stack"

"@tootallnate/once@npm:2.0.0":
  locations:
    - "node_modules/@tootallnate/once"

"@ts-morph/common@npm:0.11.1":
  locations:
    - "node_modules/ts-morph/node_modules/@ts-morph/common"

"@ts-morph/common@npm:0.16.0":
  locations:
    - "node_modules/@ts-morph/common"

"@tsconfig/node10@npm:1.0.11":
  locations:
    - "node_modules/@tsconfig/node10"

"@tsconfig/node12@npm:1.0.11":
  locations:
    - "node_modules/@tsconfig/node12"

"@tsconfig/node14@npm:1.0.3":
  locations:
    - "node_modules/@tsconfig/node14"

"@tsconfig/node16@npm:1.0.4":
  locations:
    - "node_modules/@tsconfig/node16"

"@types/babel__core@npm:7.20.5":
  locations:
    - "node_modules/@types/babel__core"

"@types/babel__generator@npm:7.6.8":
  locations:
    - "node_modules/@types/babel__generator"

"@types/babel__template@npm:7.4.4":
  locations:
    - "node_modules/@types/babel__template"

"@types/babel__traverse@npm:7.20.6":
  locations:
    - "node_modules/@types/babel__traverse"

"@types/estree@npm:1.0.6":
  locations:
    - "node_modules/@types/estree"

"@types/fs-extra@npm:9.0.13":
  locations:
    - "node_modules/@types/fs-extra"

"@types/graceful-fs@npm:4.1.9":
  locations:
    - "node_modules/@types/graceful-fs"

"@types/hammerjs@npm:2.0.46":
  locations:
    - "node_modules/@types/hammerjs"

"@types/html-minifier-terser@npm:6.1.0":
  locations:
    - "node_modules/@types/html-minifier-terser"

"@types/istanbul-lib-coverage@npm:2.0.6":
  locations:
    - "node_modules/@types/istanbul-lib-coverage"

"@types/istanbul-lib-report@npm:3.0.3":
  locations:
    - "node_modules/@types/istanbul-lib-report"

"@types/istanbul-reports@npm:3.0.4":
  locations:
    - "node_modules/@types/istanbul-reports"

"@types/js-yaml@npm:4.0.9":
  locations:
    - "node_modules/@types/js-yaml"

"@types/json-schema@npm:7.0.15":
  locations:
    - "node_modules/@types/json-schema"

"@types/json5@npm:0.0.29":
  locations:
    - "node_modules/@types/json5"

"@types/node@npm:16.18.11":
  locations:
    - "node_modules/@vercel/node/node_modules/@types/node"

"@types/node@npm:20.17.19":
  locations:
    - "node_modules/@types/node"

"@types/node@npm:22.13.5":
  locations:
    - "node_modules/jest-worker/node_modules/@types/node"
    - "node_modules/jest-util/node_modules/@types/node"
    - "node_modules/jest-mock/node_modules/@types/node"
    - "node_modules/jest-haste-map/node_modules/@types/node"
    - "node_modules/jest-environment-node/node_modules/@types/node"
    - "node_modules/chromium-edge-launcher/node_modules/@types/node"
    - "node_modules/chrome-launcher/node_modules/@types/node"
    - "node_modules/@types/graceful-fs/node_modules/@types/node"
    - "node_modules/@types/fs-extra/node_modules/@types/node"
    - "node_modules/@jest/types/node_modules/@types/node"
    - "node_modules/@jest/fake-timers/node_modules/@types/node"
    - "node_modules/@jest/environment/node_modules/@types/node"

"@types/react-native@npm:0.73.0":
  locations:
    - "node_modules/@types/react-native"

"@types/react@npm:19.0.14":
  locations:
    - "node_modules/@types/react"

"@types/stack-utils@npm:2.0.3":
  locations:
    - "node_modules/@types/stack-utils"

"@types/yargs-parser@npm:21.0.3":
  locations:
    - "node_modules/@types/yargs-parser"

"@types/yargs@npm:17.0.33":
  locations:
    - "node_modules/@types/yargs"

"@typescript-eslint/eslint-plugin@virtual:1d9de0097585fa69946f3652710a369a1f515e2c8d44d399a55a0a95dc2ae4666b0f81f47e1bb1e7278951572bd8f502b1cc29ae46a4111254c1e8df04500c0f#npm:8.25.0":
  locations:
    - "node_modules/@typescript-eslint/eslint-plugin"

"@typescript-eslint/parser@virtual:1d9de0097585fa69946f3652710a369a1f515e2c8d44d399a55a0a95dc2ae4666b0f81f47e1bb1e7278951572bd8f502b1cc29ae46a4111254c1e8df04500c0f#npm:8.25.0":
  locations:
    - "node_modules/@typescript-eslint/parser"

"@typescript-eslint/scope-manager@npm:8.25.0":
  locations:
    - "node_modules/@typescript-eslint/scope-manager"

"@typescript-eslint/type-utils@virtual:bc79daf1d3272c492f68be83afd8f7dbca94a15f8f28596d929a5eb50faf4ec7c766f11823e41d7bf7b9ea9f511e0525990e4564ffe502487be9bce4a7414e58#npm:8.25.0":
  locations:
    - "node_modules/@typescript-eslint/type-utils"

"@typescript-eslint/types@npm:8.25.0":
  locations:
    - "node_modules/@typescript-eslint/types"

"@typescript-eslint/typescript-estree@virtual:a7f3cb4bbf678a5aa89a947cab1be75940ee95fd5cb86d2da38601f9542cabad57e4fb3b78df7e62d5ec1057a0fb1b97a2ff14693157ef5909d9d0260004372d#npm:8.25.0":
  locations:
    - "node_modules/@typescript-eslint/typescript-estree"

"@typescript-eslint/utils@virtual:bc79daf1d3272c492f68be83afd8f7dbca94a15f8f28596d929a5eb50faf4ec7c766f11823e41d7bf7b9ea9f511e0525990e4564ffe502487be9bce4a7414e58#npm:8.25.0":
  locations:
    - "node_modules/@typescript-eslint/utils"

"@typescript-eslint/visitor-keys@npm:8.25.0":
  locations:
    - "node_modules/@typescript-eslint/visitor-keys"

"@typescript/native-preview-darwin-arm64@npm:7.0.0-dev.20250619.1":
  locations:
    - "node_modules/@typescript/native-preview-darwin-arm64"

"@typescript/native-preview@npm:7.0.0-dev.20250619.1":
  locations:
    - "node_modules/@typescript/native-preview"

"@urql/core@npm:5.1.0":
  locations:
    - "node_modules/@urql/core"

"@urql/exchange-retry@virtual:fb70ee0167e50d6506d79efa4d1b799350b9c189d900f3023580451de2767a7b28b4bcf28e6f8b5a51ae7e7f2c251f876e30b49aef9f644aa67b6b121c09e0b4#npm:1.3.0":
  locations:
    - "node_modules/@urql/exchange-retry"

"@vercel/build-utils@npm:10.3.0":
  locations:
    - "node_modules/@vercel/build-utils"

"@vercel/error-utils@npm:2.0.3":
  locations:
    - "node_modules/@vercel/error-utils"

"@vercel/fun@npm:1.1.5":
  locations:
    - "node_modules/@vercel/fun"

"@vercel/gatsby-plugin-vercel-analytics@npm:1.0.11":
  locations:
    - "node_modules/@vercel/gatsby-plugin-vercel-analytics"

"@vercel/gatsby-plugin-vercel-builder@npm:2.0.75":
  locations:
    - "node_modules/@vercel/gatsby-plugin-vercel-builder"

"@vercel/go@npm:3.2.1":
  locations:
    - "node_modules/@vercel/go"

"@vercel/hydrogen@npm:1.2.0":
  locations:
    - "node_modules/@vercel/hydrogen"

"@vercel/next@npm:4.7.1":
  locations:
    - "node_modules/@vercel/next"

"@vercel/nft@npm:0.27.10":
  locations:
    - "node_modules/@vercel/nft"

"@vercel/node@npm:5.1.9":
  locations:
    - "node_modules/@vercel/node"

"@vercel/python@npm:4.7.1":
  locations:
    - "node_modules/@vercel/python"

"@vercel/redwood@npm:2.3.0":
  locations:
    - "node_modules/@vercel/redwood"

"@vercel/remix-builder@npm:5.4.1":
  locations:
    - "node_modules/@vercel/remix-builder"

"@vercel/ruby@npm:2.2.0":
  locations:
    - "node_modules/@vercel/ruby"

"@vercel/static-build@npm:2.7.1":
  locations:
    - "node_modules/@vercel/static-build"

"@vercel/static-config@npm:3.0.0":
  locations:
    - "node_modules/@vercel/static-config"

"@vitest/expect@npm:2.1.9":
  locations:
    - "node_modules/@vitest/expect"

"@vitest/mocker@virtual:94a9b455e624e54714364a7fb7c57e32309ff9e461ee2f834b715bfc39142d3eb9d29ea15e596ef0604e00efc1ecfd2399e107b636dbead839e5ed7958b6981d#npm:2.1.9":
  locations:
    - "node_modules/@vitest/mocker"

"@vitest/pretty-format@npm:2.1.9":
  locations:
    - "node_modules/@vitest/pretty-format"

"@vitest/runner@npm:2.1.9":
  locations:
    - "node_modules/@vitest/runner"

"@vitest/snapshot@npm:2.1.9":
  locations:
    - "node_modules/@vitest/snapshot"

"@vitest/spy@npm:2.1.9":
  locations:
    - "node_modules/@vitest/spy"

"@vitest/utils@npm:2.1.9":
  locations:
    - "node_modules/@vitest/utils"

"@xmldom/xmldom@npm:0.8.10":
  locations:
    - "node_modules/@xmldom/xmldom"

"abbrev@npm:3.0.0":
  locations:
    - "node_modules/abbrev"

"abort-controller@npm:3.0.0":
  locations:
    - "node_modules/abort-controller"

"accepts@npm:1.3.8":
  locations:
    - "node_modules/accepts"

"acorn-import-attributes@virtual:0a569de48427967cf41a8f6e26e07e32ca821b4018672603fe0be4bf2f5846b12157401579435b54dec3acfcb4a704d1ba7be93bebd847a94b7121bf4aa10815#npm:1.9.5":
  locations:
    - "node_modules/acorn-import-attributes"

"acorn-jsx@virtual:e5b10052f5b961a2f38f3edbfbbcd7baf074e555f688eaf63695fed94838b848c6291487952754e262c19f60bf34d659b71e3d5260e2ea073949e1b6269dc165#npm:5.3.2":
  locations:
    - "node_modules/acorn-jsx"

"acorn-walk@npm:8.3.4":
  locations:
    - "node_modules/acorn-walk"

"acorn@npm:8.14.0":
  locations:
    - "node_modules/acorn"

"agent-base@npm:7.1.3":
  locations:
    - "node_modules/agent-base"

"ajv-formats@virtual:6f0a75e2e24507b64e23679df5b98c6b0a9e9a8621a95c9f8c1ae8bd406eb857104f4065344af3a2d6d0a9ff549b8adbd3d15f60ef166628999040bd862e7a27#npm:2.1.1":
  locations:
    - "node_modules/ajv-formats"

"ajv-keywords@virtual:6f0a75e2e24507b64e23679df5b98c6b0a9e9a8621a95c9f8c1ae8bd406eb857104f4065344af3a2d6d0a9ff549b8adbd3d15f60ef166628999040bd862e7a27#npm:5.1.0":
  locations:
    - "node_modules/ajv-keywords"

"ajv-keywords@virtual:f2b36937f163b579815d3163513b3330d7a31aaf0599eea66346382b8838395c613f4204e9809cc2ff6bba09c17ab0c34b37deadcb147de7e2f5e535d6ccc245#npm:3.5.2":
  locations:
    - "node_modules/url-loader/node_modules/ajv-keywords"
    - "node_modules/file-loader/node_modules/ajv-keywords"

"ajv@npm:6.12.6":
  locations:
    - "node_modules/url-loader/node_modules/ajv"
    - "node_modules/file-loader/node_modules/ajv"
    - "node_modules/eslint/node_modules/ajv"
    - "node_modules/@eslint/eslintrc/node_modules/ajv"

"ajv@npm:8.11.0":
  locations:
    - "node_modules/expo-dev-launcher/node_modules/ajv"

"ajv@npm:8.17.1":
  locations:
    - "node_modules/ajv"

"ajv@npm:8.6.3":
  locations:
    - "node_modules/@vercel/static-config/node_modules/ajv"

"anser@npm:1.4.10":
  locations:
    - "node_modules/anser"

"ansi-escapes@npm:4.3.2":
  locations:
    - "node_modules/ansi-escapes"

"ansi-escapes@npm:6.2.1":
  locations:
    - "node_modules/marked-terminal/node_modules/ansi-escapes"

"ansi-regex@npm:3.0.1":
  locations:
    - "node_modules/ansi-split/node_modules/ansi-regex"

"ansi-regex@npm:4.1.1":
  locations:
    - "node_modules/ora/node_modules/ansi-regex"

"ansi-regex@npm:5.0.1":
  locations:
    - "node_modules/ansi-regex"

"ansi-regex@npm:6.1.0":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/ansi-regex"

"ansi-split@npm:1.0.1":
  locations:
    - "node_modules/ansi-split"

"ansi-styles@npm:3.2.1":
  locations:
    - "node_modules/ora/node_modules/ansi-styles"
    - "node_modules/log-symbols/node_modules/ansi-styles"
    - "node_modules/@babel/highlight/node_modules/ansi-styles"

"ansi-styles@npm:4.3.0":
  locations:
    - "node_modules/ansi-styles"

"ansi-styles@npm:5.2.0":
  locations:
    - "node_modules/pretty-format/node_modules/ansi-styles"

"ansi-styles@npm:6.2.1":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/ansi-styles"

"ansicolors@npm:0.3.2":
  locations:
    - "node_modules/ansicolors"

"any-promise@npm:1.3.0":
  locations:
    - "node_modules/any-promise"

"anymatch@npm:3.1.3":
  locations:
    - "node_modules/anymatch"

"app@workspace:packages/app":
  locations:
    - "node_modules/app"

"application-config-path@npm:0.1.1":
  locations:
    - "node_modules/application-config-path"

"arg@npm:4.1.0":
  locations:
    - "node_modules/micro/node_modules/arg"
    - "node_modules/expo-updates/node_modules/arg"

"arg@npm:4.1.3":
  locations:
    - "node_modules/@vercel/node/node_modules/arg"

"arg@npm:5.0.2":
  locations:
    - "node_modules/arg"

"argparse@npm:1.0.10":
  locations:
    - "node_modules/argparse"

"argparse@npm:2.0.1":
  locations:
    - "node_modules/js-yaml/node_modules/argparse"

"aria-hidden@npm:1.2.4":
  locations:
    - "node_modules/aria-hidden"

"aria-query@npm:5.3.2":
  locations:
    - "node_modules/aria-query"

"array-buffer-byte-length@npm:1.0.2":
  locations:
    - "node_modules/array-buffer-byte-length"

"array-flatten@npm:1.1.1":
  locations:
    - "node_modules/array-flatten"

"array-includes@npm:3.1.8":
  locations:
    - "node_modules/array-includes"

"array.prototype.findlast@npm:1.2.5":
  locations:
    - "node_modules/array.prototype.findlast"

"array.prototype.findlastindex@npm:1.2.5":
  locations:
    - "node_modules/array.prototype.findlastindex"

"array.prototype.flat@npm:1.3.3":
  locations:
    - "node_modules/array.prototype.flat"

"array.prototype.flatmap@npm:1.3.3":
  locations:
    - "node_modules/array.prototype.flatmap"

"array.prototype.tosorted@npm:1.1.4":
  locations:
    - "node_modules/array.prototype.tosorted"

"arraybuffer.prototype.slice@npm:1.0.4":
  locations:
    - "node_modules/arraybuffer.prototype.slice"

"asap@npm:2.0.6":
  locations:
    - "node_modules/asap"

"assertion-error@npm:2.0.1":
  locations:
    - "node_modules/assertion-error"

"ast-types-flow@npm:0.0.8":
  locations:
    - "node_modules/ast-types-flow"

"astral-regex@npm:2.0.0":
  locations:
    - "node_modules/astral-regex"

"async-function@npm:1.0.0":
  locations:
    - "node_modules/async-function"

"async-limiter@npm:1.0.1":
  locations:
    - "node_modules/async-limiter"

"async-listen@npm:1.2.0":
  locations:
    - "node_modules/async-listen"

"async-listen@npm:3.0.0":
  locations:
    - "node_modules/@vercel/node/node_modules/async-listen"

"async-listen@npm:3.0.1":
  locations:
    - "node_modules/edge-runtime/node_modules/async-listen"

"async-sema@npm:3.1.1":
  locations:
    - "node_modules/async-sema"

"available-typed-arrays@npm:1.0.7":
  locations:
    - "node_modules/available-typed-arrays"

"await-lock@npm:2.2.2":
  locations:
    - "node_modules/await-lock"

"axe-core@npm:4.10.2":
  locations:
    - "node_modules/axe-core"

"axobject-query@npm:4.1.0":
  locations:
    - "node_modules/axobject-query"

"babel-jest@virtual:64b0d24e78fb24a4d23b5fdd0f59007fc38be9e523e31719c695c39d5279857d4201572683e08da89dea76f2678d593d9b2025b691a1da465e3fb941150dddce#npm:29.7.0":
  locations:
    - "node_modules/babel-jest"

"babel-literal-to-ast@virtual:4b5540d0755d71b727722f75d0998f7d1a1c397790df8d4b89af91a08e73b120351ed29e6760f8b370b0d967b4e5f3cf413adca2bebeef4a08c5e37c08d75738#npm:2.1.0":
  locations:
    - "node_modules/babel-literal-to-ast"

"babel-loader@virtual:2ed9a9ca06dbfcd7161ed29975c6ac3650af7a2e17f09203c7a1718e71ec47203605a304723cc72d6c65795be10ba94c65f3fcc0298a05a4bf42540d3fead0e8#npm:9.2.1":
  locations:
    - "node_modules/babel-loader"

"babel-plugin-istanbul@npm:6.1.1":
  locations:
    - "node_modules/babel-plugin-istanbul"

"babel-plugin-jest-hoist@npm:29.6.3":
  locations:
    - "node_modules/babel-plugin-jest-hoist"

"babel-plugin-module-resolver@npm:5.0.2":
  locations:
    - "node_modules/babel-plugin-module-resolver"

"babel-plugin-polyfill-corejs2@virtual:ddbca061f533010ee0e16088a3c06b9e7749f5190e93f3817c676f6b26a01d8c7a20e416cf2c1ea477a7a96fa5d7ec620abd5157a0636e4917202e6eb0995a6c#npm:0.4.12":
  locations:
    - "node_modules/babel-plugin-polyfill-corejs2"

"babel-plugin-polyfill-corejs3@virtual:ddbca061f533010ee0e16088a3c06b9e7749f5190e93f3817c676f6b26a01d8c7a20e416cf2c1ea477a7a96fa5d7ec620abd5157a0636e4917202e6eb0995a6c#npm:0.10.6":
  locations:
    - "node_modules/babel-plugin-polyfill-corejs3"

"babel-plugin-polyfill-regenerator@virtual:ddbca061f533010ee0e16088a3c06b9e7749f5190e93f3817c676f6b26a01d8c7a20e416cf2c1ea477a7a96fa5d7ec620abd5157a0636e4917202e6eb0995a6c#npm:0.6.3":
  locations:
    - "node_modules/babel-plugin-polyfill-regenerator"

"babel-plugin-react-native-web@npm:0.19.13":
  locations:
    - "node_modules/babel-plugin-react-native-web"

"babel-plugin-syntax-hermes-parser@npm:0.25.1":
  locations:
    - "node_modules/babel-plugin-syntax-hermes-parser"

"babel-plugin-transform-flow-enums@npm:0.0.2":
  locations:
    - "node_modules/babel-plugin-transform-flow-enums"

"babel-preset-current-node-syntax@virtual:0bb5880f430928ff8b2b8f0537d36dfff682e8aa932b37e669e0bd539108e9b18e5f26bf366ff25547f87b428e7ef114e62d10b6490a7a368a1e3be2cba269a3#npm:1.1.0":
  locations:
    - "node_modules/babel-preset-current-node-syntax"

"babel-preset-expo@virtual:47826c94a6bd2fbcfe47f231c9e9f1c51e34162bc384cfcc2d4d1e0fd4ba4e496b3e6bc2229779ee9366c0dcf91ff9317e25b3f5972d6f01f92d559eb7d769eb#npm:13.1.11":
  locations:
    - "node_modules/babel-preset-expo"

"babel-preset-jest@virtual:8008d74e0ce5e31a65f88415a7abec0c1689a0a9a75f4dfcba64bddd18dbe7463758c44c7f2cff43fa0c0227f46d1a1fec730cb0ffbfd37c9c72baf7f00e4990#npm:29.6.3":
  locations:
    - "node_modules/babel-preset-jest"

"balanced-match@npm:1.0.2":
  locations:
    - "node_modules/balanced-match"

"base64-js@npm:1.5.1":
  locations:
    - "node_modules/base64-js"

"better-opn@npm:3.0.2":
  locations:
    - "node_modules/better-opn"

"big-integer@npm:1.6.52":
  locations:
    - "node_modules/big-integer"

"big.js@npm:5.2.2":
  locations:
    - "node_modules/big.js"

"binary-extensions@npm:2.3.0":
  locations:
    - "node_modules/binary-extensions"

"bindings@npm:1.5.0":
  locations:
    - "node_modules/bindings"

"body-parser@npm:1.20.3":
  locations:
    - "node_modules/body-parser"

"boolbase@npm:1.0.0":
  locations:
    - "node_modules/boolbase"

"bplist-creator@npm:0.1.0":
  locations:
    - "node_modules/@expo/cli/node_modules/bplist-creator"

"bplist-creator@npm:0.1.1":
  locations:
    - "node_modules/bplist-creator"

"bplist-parser@npm:0.3.2":
  locations:
    - "node_modules/bplist-parser"

"brace-expansion@npm:1.1.11":
  locations:
    - "node_modules/minimatch/node_modules/brace-expansion"

"brace-expansion@npm:2.0.1":
  locations:
    - "node_modules/brace-expansion"

"braces@npm:3.0.3":
  locations:
    - "node_modules/braces"

"browserslist@npm:4.24.4":
  locations:
    - "node_modules/browserslist"

"bser@npm:2.1.1":
  locations:
    - "node_modules/bser"

"buffer-crc32@npm:0.2.13":
  locations:
    - "node_modules/buffer-crc32"

"buffer-from@npm:1.1.2":
  locations:
    - "node_modules/buffer-from"

"buffer@npm:5.7.1":
  locations:
    - "node_modules/buffer"

"burnt@virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:0.12.2":
  locations:
    - "node_modules/burnt"
  aliases:
    - "virtual:f87a972e7ee54256c6d8f979d7f3914b32522893226eba595e4efe4ecc641a239c6d88e01eccc6f32db30829d6ac493bfc98cb406a9b0d6059ee4112c0843da9#npm:0.12.2"

"busboy@npm:1.6.0":
  locations:
    - "node_modules/busboy"

"bytes@npm:3.1.0":
  locations:
    - "node_modules/micro/node_modules/bytes"

"bytes@npm:3.1.2":
  locations:
    - "node_modules/bytes"

"cac@npm:6.7.14":
  locations:
    - "node_modules/cac"

"cacache@npm:19.0.1":
  locations:
    - "node_modules/cacache"

"call-bind-apply-helpers@npm:1.0.2":
  locations:
    - "node_modules/call-bind-apply-helpers"

"call-bind@npm:1.0.8":
  locations:
    - "node_modules/call-bind"

"call-bound@npm:1.0.3":
  locations:
    - "node_modules/call-bound"

"caller-callsite@npm:2.0.0":
  locations:
    - "node_modules/caller-callsite"

"caller-path@npm:2.0.0":
  locations:
    - "node_modules/caller-path"

"callsites@npm:2.0.0":
  locations:
    - "node_modules/caller-callsite/node_modules/callsites"

"callsites@npm:3.1.0":
  locations:
    - "node_modules/callsites"

"camel-case@npm:4.1.2":
  locations:
    - "node_modules/camel-case"

"camelcase@npm:5.3.1":
  locations:
    - "node_modules/@istanbuljs/load-nyc-config/node_modules/camelcase"

"camelcase@npm:6.3.0":
  locations:
    - "node_modules/camelcase"

"caniuse-lite@npm:1.0.30001701":
  locations:
    - "node_modules/caniuse-lite"

"capital-case@npm:1.0.4":
  locations:
    - "node_modules/capital-case"

"cardinal@npm:2.1.1":
  locations:
    - "node_modules/cardinal"

"chai@npm:5.2.0":
  locations:
    - "node_modules/chai"

"chalk@npm:2.4.2":
  locations:
    - "node_modules/ora/node_modules/chalk"
    - "node_modules/log-symbols/node_modules/chalk"
    - "node_modules/@babel/highlight/node_modules/chalk"

"chalk@npm:4.1.2":
  locations:
    - "node_modules/chalk"

"chalk@npm:5.4.1":
  locations:
    - "node_modules/marked-terminal/node_modules/chalk"
    - "node_modules/check-dependency-version-consistency/node_modules/chalk"

"change-case@npm:4.1.2":
  locations:
    - "node_modules/change-case"

"check-dependency-version-consistency@npm:4.1.1":
  locations:
    - "node_modules/check-dependency-version-consistency"

"check-error@npm:2.1.1":
  locations:
    - "node_modules/check-error"

"chokidar@npm:3.6.0":
  locations:
    - "node_modules/chokidar"

"chokidar@npm:4.0.0":
  locations:
    - "node_modules/vercel/node_modules/chokidar"

"chownr@npm:2.0.0":
  locations:
    - "node_modules/@vercel/fun/node_modules/chownr"

"chownr@npm:3.0.0":
  locations:
    - "node_modules/chownr"

"chrome-launcher@npm:0.15.2":
  locations:
    - "node_modules/chrome-launcher"

"chromium-edge-launcher@npm:0.2.0":
  locations:
    - "node_modules/chromium-edge-launcher"

"ci-info@npm:2.0.0":
  locations:
    - "node_modules/metro/node_modules/ci-info"

"ci-info@npm:3.9.0":
  locations:
    - "node_modules/ci-info"

"cjs-module-lexer@npm:1.2.3":
  locations:
    - "node_modules/cjs-module-lexer"

"clean-css@npm:5.3.3":
  locations:
    - "node_modules/clean-css"

"cli-cursor@npm:2.1.0":
  locations:
    - "node_modules/cli-cursor"

"cli-spinners@npm:2.9.2":
  locations:
    - "node_modules/cli-spinners"

"cli-table3@npm:0.6.5":
  locations:
    - "node_modules/cli-table3"

"client-only@npm:0.0.1":
  locations:
    - "node_modules/client-only"

"cliui@npm:7.0.4":
  locations:
    - "node_modules/cliui"

"cliui@npm:8.0.1":
  locations:
    - "node_modules/yargs/node_modules/cliui"

"clone@npm:1.0.4":
  locations:
    - "node_modules/clone"

"code-block-writer@npm:10.1.1":
  locations:
    - "node_modules/ts-morph/node_modules/code-block-writer"

"code-block-writer@npm:11.0.3":
  locations:
    - "node_modules/code-block-writer"

"color-convert@npm:1.9.3":
  locations:
    - "node_modules/ora/node_modules/color-convert"
    - "node_modules/log-symbols/node_modules/color-convert"
    - "node_modules/@babel/highlight/node_modules/color-convert"

"color-convert@npm:2.0.1":
  locations:
    - "node_modules/color-convert"

"color-name@npm:1.1.3":
  locations:
    - "node_modules/ora/node_modules/color-name"
    - "node_modules/log-symbols/node_modules/color-name"
    - "node_modules/@babel/highlight/node_modules/color-name"

"color-name@npm:1.1.4":
  locations:
    - "node_modules/color-name"

"color-string@npm:1.9.1":
  locations:
    - "node_modules/color-string"

"color2k@npm:2.0.3":
  locations:
    - "node_modules/color2k"

"color@npm:4.2.3":
  locations:
    - "node_modules/color"

"command-exists@npm:1.2.9":
  locations:
    - "node_modules/command-exists"

"commander@npm:11.1.0":
  locations:
    - "node_modules/commander"

"commander@npm:12.1.0":
  locations:
    - "node_modules/react-native/node_modules/commander"

"commander@npm:2.20.3":
  locations:
    - "node_modules/terser/node_modules/commander"

"commander@npm:4.1.1":
  locations:
    - "node_modules/sucrase/node_modules/commander"

"commander@npm:7.2.0":
  locations:
    - "node_modules/expo-modules-autolinking/node_modules/commander"

"commander@npm:8.3.0":
  locations:
    - "node_modules/html-minifier-terser/node_modules/commander"

"common-path-prefix@npm:3.0.0":
  locations:
    - "node_modules/common-path-prefix"

"commondir@npm:1.0.1":
  locations:
    - "node_modules/commondir"

"compressible@npm:2.0.18":
  locations:
    - "node_modules/compressible"

"compression@npm:1.8.0":
  locations:
    - "node_modules/compression"

"concat-map@npm:0.0.1":
  locations:
    - "node_modules/concat-map"

"connect@npm:3.7.0":
  locations:
    - "node_modules/connect"

"consola@npm:3.4.0":
  locations:
    - "node_modules/consola"

"constant-case@npm:3.0.4":
  locations:
    - "node_modules/constant-case"

"content-disposition@npm:0.5.4":
  locations:
    - "node_modules/content-disposition"

"content-type@npm:1.0.4":
  locations:
    - "node_modules/micro/node_modules/content-type"

"content-type@npm:1.0.5":
  locations:
    - "node_modules/content-type"

"convert-hrtime@npm:3.0.0":
  locations:
    - "node_modules/convert-hrtime"

"convert-source-map@npm:2.0.0":
  locations:
    - "node_modules/convert-source-map"

"cookie-signature@npm:1.0.6":
  locations:
    - "node_modules/cookie-signature"

"cookie@npm:0.7.1":
  locations:
    - "node_modules/cookie"

"core-js-compat@npm:3.40.0":
  locations:
    - "node_modules/core-js-compat"

"cosmiconfig@npm:5.2.1":
  locations:
    - "node_modules/cosmiconfig"

"create-require@npm:1.1.1":
  locations:
    - "node_modules/create-require"

"cross-fetch@npm:3.2.0":
  locations:
    - "node_modules/cross-fetch"

"cross-spawn@npm:7.0.6":
  locations:
    - "node_modules/cross-spawn"

"crypto-random-string@npm:2.0.0":
  locations:
    - "node_modules/crypto-random-string"

"css-in-js-utils@npm:3.1.0":
  locations:
    - "node_modules/css-in-js-utils"

"css-loader@virtual:2ed9a9ca06dbfcd7161ed29975c6ac3650af7a2e17f09203c7a1718e71ec47203605a304723cc72d6c65795be10ba94c65f3fcc0298a05a4bf42540d3fead0e8#npm:6.11.0":
  locations:
    - "node_modules/css-loader"

"css-select@npm:4.3.0":
  locations:
    - "node_modules/renderkid/node_modules/css-select"

"css-select@npm:5.1.0":
  locations:
    - "node_modules/css-select"

"css-tree@npm:1.1.3":
  locations:
    - "node_modules/css-tree"

"css-what@npm:6.1.0":
  locations:
    - "node_modules/css-what"

"cssesc@npm:3.0.0":
  locations:
    - "node_modules/cssesc"

"csstype@npm:3.1.3":
  locations:
    - "node_modules/csstype"

"damerau-levenshtein@npm:1.0.8":
  locations:
    - "node_modules/damerau-levenshtein"

"data-view-buffer@npm:1.0.2":
  locations:
    - "node_modules/data-view-buffer"

"data-view-byte-length@npm:1.0.2":
  locations:
    - "node_modules/data-view-byte-length"

"data-view-byte-offset@npm:1.0.1":
  locations:
    - "node_modules/data-view-byte-offset"

"debug@virtual:440dd294d38e3462f4ec4c6bd5c764a2e47c75e3b30c861d37cac3f4655ca311c1f99590ed77b65694eacff5a5fd5601478e41be6a6a5e433155c34eddedf1a7#npm:3.2.7":
  locations:
    - "node_modules/eslint-plugin-import/node_modules/debug"
    - "node_modules/eslint-module-utils/node_modules/debug"
    - "node_modules/eslint-import-resolver-node/node_modules/debug"
    - "node_modules/@expo/devcert/node_modules/debug"

"debug@virtual:930f7e38f02586080a23d47794f474516dbd6e592ae78b8b1c1a09855658a4a9d2cbc03309d7f03307af737fa32a71f8874d9d7a2c42e0fa6b06659f13878935#npm:4.3.4":
  locations:
    - "node_modules/@vercel/fun/node_modules/debug"

"debug@virtual:b34343a94f4feff9bd0c25e50433ff67b48b053232f0baf08170cc7b87a44750405bfbf58c0a63fc763b37aa3645239e64cc44babf1b72355d4165b74ca70a6b#npm:4.4.0":
  locations:
    - "node_modules/debug"

"debug@virtual:c7b184cd14c02e3ce555ab1875e60cf5033c617e17d82c4c02ea822101d3c817f48bf25a766b4d4335742dc5c9c14c2e88a57ed955a56c4ad0613899f82f5618#npm:2.6.9":
  locations:
    - "node_modules/send/node_modules/debug"
    - "node_modules/lighthouse-logger/node_modules/debug"
    - "node_modules/finalhandler/node_modules/debug"
    - "node_modules/express/node_modules/debug"
    - "node_modules/connect/node_modules/debug"
    - "node_modules/compression/node_modules/debug"
    - "node_modules/body-parser/node_modules/debug"
    - "node_modules/@react-native/dev-middleware/node_modules/debug"
    - "node_modules/@react-native/community-cli-plugin/node_modules/debug"
    - "node_modules/@expo/cli/node_modules/send/node_modules/debug"

"decode-uri-component@npm:0.2.2":
  locations:
    - "node_modules/decode-uri-component"

"deep-eql@npm:5.0.2":
  locations:
    - "node_modules/deep-eql"

"deep-extend@npm:0.6.0":
  locations:
    - "node_modules/deep-extend"

"deep-is@npm:0.1.4":
  locations:
    - "node_modules/deep-is"

"deepmerge@npm:4.3.1":
  locations:
    - "node_modules/deepmerge"

"defaults@npm:1.0.4":
  locations:
    - "node_modules/defaults"

"define-data-property@npm:1.1.4":
  locations:
    - "node_modules/define-data-property"

"define-lazy-prop@npm:2.0.0":
  locations:
    - "node_modules/define-lazy-prop"

"define-properties@npm:1.2.1":
  locations:
    - "node_modules/define-properties"

"depd@npm:1.1.2":
  locations:
    - "node_modules/micro/node_modules/depd"

"depd@npm:2.0.0":
  locations:
    - "node_modules/depd"

"destroy@npm:1.2.0":
  locations:
    - "node_modules/destroy"

"detect-libc@npm:1.0.3":
  locations:
    - "node_modules/detect-libc"

"detect-libc@npm:2.0.3":
  locations:
    - "node_modules/@mapbox/node-pre-gyp/node_modules/detect-libc"

"detect-node-es@npm:1.1.0":
  locations:
    - "node_modules/detect-node-es"

"diff@npm:4.0.2":
  locations:
    - "node_modules/diff"

"dir-glob@npm:3.0.1":
  locations:
    - "node_modules/dir-glob"

"doctrine@npm:2.1.0":
  locations:
    - "node_modules/doctrine"

"dom-converter@npm:0.2.0":
  locations:
    - "node_modules/dom-converter"

"dom-serializer@npm:1.4.1":
  locations:
    - "node_modules/domutils/node_modules/dom-serializer"

"dom-serializer@npm:2.0.0":
  locations:
    - "node_modules/dom-serializer"

"domelementtype@npm:2.3.0":
  locations:
    - "node_modules/domelementtype"

"domhandler@npm:4.3.1":
  locations:
    - "node_modules/domhandler"

"domhandler@npm:5.0.3":
  locations:
    - "node_modules/dom-serializer/node_modules/domhandler"
    - "node_modules/css-select/node_modules/domhandler"

"domutils@npm:2.8.0":
  locations:
    - "node_modules/domutils"

"domutils@npm:3.2.2":
  locations:
    - "node_modules/css-select/node_modules/domutils"

"dot-case@npm:3.0.4":
  locations:
    - "node_modules/dot-case"

"dotenv-expand@npm:11.0.7":
  locations:
    - "node_modules/dotenv-expand"

"dotenv@npm:16.4.7":
  locations:
    - "node_modules/dotenv"

"dunder-proto@npm:1.0.1":
  locations:
    - "node_modules/dunder-proto"

"eastasianwidth@npm:0.2.0":
  locations:
    - "node_modules/eastasianwidth"

"edge-runtime@npm:2.5.9":
  locations:
    - "node_modules/edge-runtime"

"edit-json-file@npm:1.8.1":
  locations:
    - "node_modules/edit-json-file"

"ee-first@npm:1.1.1":
  locations:
    - "node_modules/ee-first"

"electron-to-chromium@npm:1.5.107":
  locations:
    - "node_modules/electron-to-chromium"

"emoji-regex@npm:8.0.0":
  locations:
    - "node_modules/string-width/node_modules/emoji-regex"
    - "node_modules/string-width-cjs/node_modules/emoji-regex"

"emoji-regex@npm:9.2.2":
  locations:
    - "node_modules/emoji-regex"

"emojis-list@npm:3.0.0":
  locations:
    - "node_modules/emojis-list"

"encodeurl@npm:1.0.2":
  locations:
    - "node_modules/send/node_modules/encodeurl"
    - "node_modules/connect/node_modules/encodeurl"

"encodeurl@npm:2.0.0":
  locations:
    - "node_modules/encodeurl"

"encoding@npm:0.1.13":
  locations:
    - "node_modules/encoding"

"end-of-stream@npm:1.1.0":
  locations:
    - "node_modules/end-of-stream"

"enhanced-resolve@npm:5.18.1":
  locations:
    - "node_modules/enhanced-resolve"

"entities@npm:2.2.0":
  locations:
    - "node_modules/entities"

"entities@npm:4.5.0":
  locations:
    - "node_modules/dom-serializer/node_modules/entities"

"env-editor@npm:0.4.2":
  locations:
    - "node_modules/env-editor"

"env-paths@npm:2.2.1":
  locations:
    - "node_modules/env-paths"

"eol@npm:0.9.1":
  locations:
    - "node_modules/eol"

"err-code@npm:2.0.3":
  locations:
    - "node_modules/err-code"

"error-ex@npm:1.3.2":
  locations:
    - "node_modules/error-ex"

"error-stack-parser@npm:2.1.4":
  locations:
    - "node_modules/error-stack-parser"

"es-abstract@npm:1.23.9":
  locations:
    - "node_modules/es-abstract"

"es-define-property@npm:1.0.1":
  locations:
    - "node_modules/es-define-property"

"es-errors@npm:1.3.0":
  locations:
    - "node_modules/es-errors"

"es-iterator-helpers@npm:1.2.1":
  locations:
    - "node_modules/es-iterator-helpers"

"es-module-lexer@npm:1.4.1":
  locations:
    - "node_modules/@vercel/node/node_modules/es-module-lexer"

"es-module-lexer@npm:1.6.0":
  locations:
    - "node_modules/es-module-lexer"

"es-object-atoms@npm:1.1.1":
  locations:
    - "node_modules/es-object-atoms"

"es-set-tostringtag@npm:2.1.0":
  locations:
    - "node_modules/es-set-tostringtag"

"es-shim-unscopables@npm:1.1.0":
  locations:
    - "node_modules/es-shim-unscopables"

"es-to-primitive@npm:1.3.0":
  locations:
    - "node_modules/es-to-primitive"

"esbuild-darwin-arm64@npm:0.14.47":
  locations:
    - "node_modules/esbuild-darwin-arm64"

"esbuild-loader@virtual:961af898e5613e845fc8f35453d9f5f846da9c6a8912f325e6913b3c534a02a3643b75c16c0ede0429d3782e468fa3b53bd30345f2893724214f3fc7b9ab3d00#npm:4.3.0":
  locations:
    - "node_modules/esbuild-loader"

"esbuild-plugin-es5@virtual:6bc04efe15e1ad85c53c118c6fc56caa4d4db47f90d155f84df68bbf9ef305e9bcf9c5fe2ebe91e8243cc1e2f1711f36cc691a8e9a4134839b4e8efc55fb4032#npm:2.1.1":
  locations:
    - "node_modules/esbuild-plugin-es5"

"esbuild-register@virtual:4b5540d0755d71b727722f75d0998f7d1a1c397790df8d4b89af91a08e73b120351ed29e6760f8b370b0d967b4e5f3cf413adca2bebeef4a08c5e37c08d75738#npm:3.6.0":
  locations:
    - "node_modules/esbuild-register"
  aliases:
    - "virtual:749189fdc1ec3452df4264cc4501cfbeda95442d5c3be19132596fdd4295325baab8b6b490d0b831a8d041e0fd600e63e4475b907ab2c76bbe1e3484479f5faf#npm:3.6.0"

"esbuild@npm:0.14.47":
  locations:
    - "node_modules/@vercel/node/node_modules/esbuild"
    - "node_modules/@vercel/gatsby-plugin-vercel-builder/node_modules/esbuild"

"esbuild@npm:0.21.5":
  locations:
    - "node_modules/vite/node_modules/esbuild"

"esbuild@npm:0.25.0":
  locations:
    - "node_modules/esbuild"

"escalade@npm:3.2.0":
  locations:
    - "node_modules/escalade"

"escape-html@npm:1.0.3":
  locations:
    - "node_modules/escape-html"

"escape-string-regexp@npm:1.0.5":
  locations:
    - "node_modules/ora/node_modules/escape-string-regexp"
    - "node_modules/log-symbols/node_modules/escape-string-regexp"
    - "node_modules/@babel/highlight/node_modules/escape-string-regexp"

"escape-string-regexp@npm:2.0.0":
  locations:
    - "node_modules/stack-utils/node_modules/escape-string-regexp"

"escape-string-regexp@npm:4.0.0":
  locations:
    - "node_modules/escape-string-regexp"

"eslint-config-next@virtual:1e21ed5e542f04ac0725f80f20538bd288a2542912556a71424e0ef86756859fcedbc2507675d93c28d09cc2e230d8793e424a169e257d21ba5cb20fce2d2238#npm:14.2.24":
  locations:
    - "node_modules/eslint-config-next"

"eslint-import-resolver-node@npm:0.3.9":
  locations:
    - "node_modules/eslint-import-resolver-node"

"eslint-import-resolver-typescript@virtual:1d9de0097585fa69946f3652710a369a1f515e2c8d44d399a55a0a95dc2ae4666b0f81f47e1bb1e7278951572bd8f502b1cc29ae46a4111254c1e8df04500c0f#npm:3.8.3":
  locations:
    - "node_modules/eslint-import-resolver-typescript"

"eslint-module-utils@virtual:6da912166d52442d28a20b2dcb865598d1a103cdcd17ce908d7db974355ae5331823875ddb1af3bb057025e5d13add237af15661c4cfa6396dcca54786d895c3#npm:2.12.0":
  locations:
    - "node_modules/eslint-module-utils"

"eslint-plugin-import@virtual:1d9de0097585fa69946f3652710a369a1f515e2c8d44d399a55a0a95dc2ae4666b0f81f47e1bb1e7278951572bd8f502b1cc29ae46a4111254c1e8df04500c0f#npm:2.31.0":
  locations:
    - "node_modules/eslint-plugin-import"

"eslint-plugin-jsx-a11y@virtual:1d9de0097585fa69946f3652710a369a1f515e2c8d44d399a55a0a95dc2ae4666b0f81f47e1bb1e7278951572bd8f502b1cc29ae46a4111254c1e8df04500c0f#npm:6.10.2":
  locations:
    - "node_modules/eslint-plugin-jsx-a11y"

"eslint-plugin-react-hooks@virtual:1d9de0097585fa69946f3652710a369a1f515e2c8d44d399a55a0a95dc2ae4666b0f81f47e1bb1e7278951572bd8f502b1cc29ae46a4111254c1e8df04500c0f#npm:5.0.0-canary-7118f5dd7-20230705":
  locations:
    - "node_modules/eslint-plugin-react-hooks"

"eslint-plugin-react@virtual:1d9de0097585fa69946f3652710a369a1f515e2c8d44d399a55a0a95dc2ae4666b0f81f47e1bb1e7278951572bd8f502b1cc29ae46a4111254c1e8df04500c0f#npm:7.37.4":
  locations:
    - "node_modules/eslint-plugin-react"

"eslint-scope@npm:8.2.0":
  locations:
    - "node_modules/eslint-scope"

"eslint-visitor-keys@npm:3.4.3":
  locations:
    - "node_modules/@eslint-community/eslint-utils/node_modules/eslint-visitor-keys"

"eslint-visitor-keys@npm:4.2.0":
  locations:
    - "node_modules/eslint-visitor-keys"

"eslint@virtual:20ecbb796054cc6e4f48e68b097cec703e7cd794dafc8f491ac8e2b18dfe2cb22abeb342900ed8f7588d49ae0d8ce64030bb9d4bd7fe9e1b720b6bb1ce8962c2#npm:9.21.0":
  locations:
    - "node_modules/eslint"

"esm-resolve@npm:1.0.11":
  locations:
    - "node_modules/esm-resolve"

"espree@npm:10.3.0":
  locations:
    - "node_modules/espree"

"esprima@npm:4.0.1":
  locations:
    - "node_modules/esprima"

"esquery@npm:1.6.0":
  locations:
    - "node_modules/esquery"

"esrecurse@npm:4.3.0":
  locations:
    - "node_modules/esrecurse"

"estraverse@npm:5.3.0":
  locations:
    - "node_modules/estraverse"

"estree-walker@npm:2.0.2":
  locations:
    - "node_modules/estree-walker"

"estree-walker@npm:3.0.3":
  locations:
    - "node_modules/@vitest/mocker/node_modules/estree-walker"

"esutils@npm:2.0.3":
  locations:
    - "node_modules/esutils"

"etag@npm:1.8.1":
  locations:
    - "node_modules/etag"

"event-target-shim@npm:5.0.1":
  locations:
    - "node_modules/event-target-shim"

"events-intercept@npm:2.0.0":
  locations:
    - "node_modules/events-intercept"

"exec-async@npm:2.2.0":
  locations:
    - "node_modules/exec-async"

"execa@npm:5.1.1":
  locations:
    - "node_modules/execa"

"expect-type@npm:1.1.0":
  locations:
    - "node_modules/expect-type"

"expo-app@workspace:apps/expo":
  locations:
    - "node_modules/expo-app"

"expo-asset@virtual:47826c94a6bd2fbcfe47f231c9e9f1c51e34162bc384cfcc2d4d1e0fd4ba4e496b3e6bc2229779ee9366c0dcf91ff9317e25b3f5972d6f01f92d559eb7d769eb#npm:11.1.5":
  locations:
    - "node_modules/expo-asset"

"expo-constants@virtual:4694c8eea71e64a91e080ffb639c502c260673202f73d354c942f7c3474fe3e034b11825c806157ec7a2f922e31726dd19b0da28730a4fa1bb3cc7fcce8322de#npm:17.1.6":
  locations:
    - "node_modules/expo-constants"
  aliases:
    - "virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:17.1.6"
    - "virtual:f87a972e7ee54256c6d8f979d7f3914b32522893226eba595e4efe4ecc641a239c6d88e01eccc6f32db30829d6ac493bfc98cb406a9b0d6059ee4112c0843da9#npm:17.1.6"

"expo-dev-client@virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:5.1.8":
  locations:
    - "node_modules/expo-dev-client"

"expo-dev-launcher@virtual:63e83007fff598d402fcb217b8c98d6061319bc5c737dc2479585a82b1f1e3acbeace639e69b225f417697bfb137f88ea4b3b0feca5b3340e6aaff68652f2a23#npm:5.1.11":
  locations:
    - "node_modules/expo-dev-launcher"

"expo-dev-menu-interface@virtual:63e83007fff598d402fcb217b8c98d6061319bc5c737dc2479585a82b1f1e3acbeace639e69b225f417697bfb137f88ea4b3b0feca5b3340e6aaff68652f2a23#npm:1.10.0":
  locations:
    - "node_modules/expo-dev-menu-interface"

"expo-dev-menu@virtual:63e83007fff598d402fcb217b8c98d6061319bc5c737dc2479585a82b1f1e3acbeace639e69b225f417697bfb137f88ea4b3b0feca5b3340e6aaff68652f2a23#npm:6.1.10":
  locations:
    - "node_modules/expo-dev-menu"

"expo-eas-client@npm:0.14.3":
  locations:
    - "node_modules/expo-eas-client"

"expo-file-system@virtual:47826c94a6bd2fbcfe47f231c9e9f1c51e34162bc384cfcc2d4d1e0fd4ba4e496b3e6bc2229779ee9366c0dcf91ff9317e25b3f5972d6f01f92d559eb7d769eb#npm:18.1.10":
  locations:
    - "node_modules/expo-file-system"

"expo-font@virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:13.3.1":
  locations:
    - "node_modules/expo-font"

"expo-json-utils@npm:0.15.0":
  locations:
    - "node_modules/expo-json-utils"

"expo-keep-awake@virtual:47826c94a6bd2fbcfe47f231c9e9f1c51e34162bc384cfcc2d4d1e0fd4ba4e496b3e6bc2229779ee9366c0dcf91ff9317e25b3f5972d6f01f92d559eb7d769eb#npm:14.1.4":
  locations:
    - "node_modules/expo-keep-awake"

"expo-linear-gradient@virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:14.1.4":
  locations:
    - "node_modules/expo-linear-gradient"

"expo-linking@virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:7.1.5":
  locations:
    - "node_modules/expo-linking"
  aliases:
    - "virtual:f87a972e7ee54256c6d8f979d7f3914b32522893226eba595e4efe4ecc641a239c6d88e01eccc6f32db30829d6ac493bfc98cb406a9b0d6059ee4112c0843da9#npm:7.1.5"

"expo-manifests@virtual:63e83007fff598d402fcb217b8c98d6061319bc5c737dc2479585a82b1f1e3acbeace639e69b225f417697bfb137f88ea4b3b0feca5b3340e6aaff68652f2a23#npm:0.16.5":
  locations:
    - "node_modules/expo-manifests"

"expo-modules-autolinking@npm:2.1.10":
  locations:
    - "node_modules/expo-modules-autolinking"

"expo-modules-core@npm:2.3.13":
  locations:
    - "node_modules/expo-modules-core"

"expo-router@virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:5.0.6":
  locations:
    - "node_modules/expo-router"

"expo-splash-screen@virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:0.30.8":
  locations:
    - "node_modules/expo-splash-screen"

"expo-sqlite@virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:15.2.13":
  locations:
    - "node_modules/expo-sqlite"

"expo-status-bar@virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:2.2.3":
  locations:
    - "node_modules/expo-status-bar"

"expo-structured-headers@npm:4.1.0":
  locations:
    - "node_modules/expo-structured-headers"

"expo-updates-interface@virtual:63e83007fff598d402fcb217b8c98d6061319bc5c737dc2479585a82b1f1e3acbeace639e69b225f417697bfb137f88ea4b3b0feca5b3340e6aaff68652f2a23#npm:1.1.0":
  locations:
    - "node_modules/expo-updates-interface"

"expo-updates@virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:0.28.13":
  locations:
    - "node_modules/expo-updates"

"expo@virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:53.0.9":
  locations:
    - "node_modules/expo"

"exponential-backoff@npm:3.1.2":
  locations:
    - "node_modules/exponential-backoff"

"express@npm:4.21.2":
  locations:
    - "node_modules/express"

"fast-deep-equal@npm:3.1.3":
  locations:
    - "node_modules/fast-deep-equal"

"fast-glob@npm:3.3.3":
  locations:
    - "node_modules/fast-glob"

"fast-json-stable-stringify@npm:2.1.0":
  locations:
    - "node_modules/fast-json-stable-stringify"

"fast-levenshtein@npm:2.0.6":
  locations:
    - "node_modules/fast-levenshtein"

"fast-loops@npm:1.1.4":
  locations:
    - "node_modules/fast-loops"

"fast-uri@npm:3.0.6":
  locations:
    - "node_modules/fast-uri"

"fastq@npm:1.19.1":
  locations:
    - "node_modules/fastq"

"fb-watchman@npm:2.0.2":
  locations:
    - "node_modules/fb-watchman"

"fbjs-css-vars@npm:1.0.2":
  locations:
    - "node_modules/fbjs-css-vars"

"fbjs@npm:3.0.5":
  locations:
    - "node_modules/fbjs"

"fd-slicer@npm:1.1.0":
  locations:
    - "node_modules/fd-slicer"

"fdir@virtual:c8807de691826868d9a774dfe20fb184997709c19896ccda6c1d382d679c7c2453bf893d9e0510372bd84566a8523db06ec721126eb95bb24e97992998e8a625#npm:6.4.3":
  locations:
    - "node_modules/fdir"

"file-entry-cache@npm:8.0.0":
  locations:
    - "node_modules/file-entry-cache"

"file-loader@virtual:2ed9a9ca06dbfcd7161ed29975c6ac3650af7a2e17f09203c7a1718e71ec47203605a304723cc72d6c65795be10ba94c65f3fcc0298a05a4bf42540d3fead0e8#npm:6.2.0":
  locations:
    - "node_modules/file-loader"

"file-uri-to-path@npm:1.0.0":
  locations:
    - "node_modules/file-uri-to-path"

"fill-range@npm:7.1.1":
  locations:
    - "node_modules/fill-range"

"filter-obj@npm:1.1.0":
  locations:
    - "node_modules/filter-obj"

"finalhandler@npm:1.1.2":
  locations:
    - "node_modules/connect/node_modules/finalhandler"

"finalhandler@npm:1.3.1":
  locations:
    - "node_modules/finalhandler"

"find-babel-config@npm:2.1.2":
  locations:
    - "node_modules/find-babel-config"

"find-cache-dir@npm:3.3.2":
  locations:
    - "node_modules/find-cache-dir"

"find-cache-dir@npm:4.0.0":
  locations:
    - "node_modules/babel-loader/node_modules/find-cache-dir"

"find-root@npm:1.1.0":
  locations:
    - "node_modules/find-root"

"find-up@npm:3.0.0":
  locations:
    - "node_modules/pkg-up/node_modules/find-up"

"find-up@npm:4.1.0":
  locations:
    - "node_modules/pkg-dir/node_modules/find-up"
    - "node_modules/@istanbuljs/load-nyc-config/node_modules/find-up"

"find-up@npm:5.0.0":
  locations:
    - "node_modules/find-up"

"find-up@npm:6.3.0":
  locations:
    - "node_modules/babel-loader/node_modules/find-up"

"find-value@npm:1.0.13":
  locations:
    - "node_modules/find-value"

"fishtouch-store@workspace:.":
  locations:
    - ""
  bin:
    "node_modules/@babel/core":
      "semver": "semver/bin/semver.js"
    "node_modules/@babel/plugin-transform-runtime":
      "semver": "semver/bin/semver.js"
    "node_modules/@babel/helper-create-regexp-features-plugin":
      "semver": "semver/bin/semver.js"
    "node_modules/@babel/helper-create-class-features-plugin":
      "semver": "semver/bin/semver.js"
    "node_modules/@babel/helper-compilation-targets":
      "semver": "semver/bin/semver.js"
    "node_modules/@expo/devcert":
      "mkdirp": "mkdirp/bin/cmd.js"
    "node_modules/chromium-edge-launcher":
      "rimraf": "rimraf/bin.js"
    "node_modules/@vercel/redwood":
      "semver": "semver/bin/semver.js"
    "node_modules/@vercel/fun":
      "semver": "semver/bin/semver.js"
      "uuid": "uuid/bin/uuid"
    "node_modules/@vercel/node":
      "esbuild": "esbuild/bin/esbuild"
      "tsc": "typescript/bin/tsc"
      "tsserver": "typescript/bin/tsserver"
      "ts-node": "ts-node/dist/bin.js"
      "ts-node-cwd": "ts-node/dist/bin-cwd.js"
      "ts-node-esm": "ts-node/dist/bin-esm.js"
      "ts-node-script": "ts-node/dist/bin-script.js"
      "ts-node-transpile-only": "ts-node/dist/bin-transpile.js"
      "ts-script": "ts-node/dist/bin-script-deprecated.js"
    "node_modules/@vercel/gatsby-plugin-vercel-builder":
      "esbuild": "esbuild/bin/esbuild"
    "node_modules/vite":
      "esbuild": "esbuild/bin/esbuild"
    "node_modules/make-dir":
      "semver": "semver/bin/semver.js"
    "node_modules/istanbul-lib-instrument":
      "semver": "semver/bin/semver.js"
    "node_modules/eslint-plugin-react":
      "semver": "semver/bin/semver.js"
      "resolve": "resolve/bin/resolve"
    "node_modules/eslint-plugin-import":
      "semver": "semver/bin/semver.js"
    "node_modules/babel-plugin-polyfill-corejs2":
      "semver": "semver/bin/semver.js"
    "node_modules/@next/eslint-plugin-next":
      "glob": "glob/dist/esm/bin.mjs"
    "node_modules/expo-router":
      "semver": "semver/bin/semver.js"
    "node_modules/node-gyp":
      "node-which": "which/bin/which.js"
    "node_modules/@istanbuljs/load-nyc-config":
      "js-yaml": "js-yaml/bin/js-yaml.js"
    "node_modules/tar":
      "mkdirp": "mkdirp/dist/cjs/src/bin.js"
    "node_modules/tsconfig-paths":
      "json5": "json5/lib/cli.js"
    "node_modules/cosmiconfig":
      "js-yaml": "js-yaml/bin/js-yaml.js"
    "node_modules/regjsparser":
      "jsesc": "jsesc/bin/jsesc"
    ".":
      "parser": "@babel/parser/bin/babel-parser.js"
      "biome": "@biomejs/biome/bin/biome"
      "tama": "@tamagui/cli/dist/index.cjs"
      "tamagui": "@tamagui/cli/dist/index.cjs"
      "tamagui-build": "@tamagui/build/tamagui-build.js"
      "teesx": "@tamagui/build/teesx.sh"
      "check-dependency-version-consistency": "check-dependency-version-consistency/dist/bin/check-dependency-version-consistency.js"
      "husky": "husky/bin.js"
      "prettier": "prettier/bin/prettier.cjs"
      "turbo": "turbo/bin/turbo"
      "tsc": "typescript/bin/tsc"
      "tsserver": "typescript/bin/tsserver"
      "ultra": "ultra-runner/bin/ultra.js"
      "eslint": "eslint/bin/eslint.js"
      "vitest": "vitest/vitest.mjs"
      "esbuild": "esbuild/bin/esbuild"
      "expo-internal": "@expo/cli/build/bin/cli"
      "fingerprint": "expo/bin/fingerprint"
      "excpretty": "@expo/xcpretty/build/cli.js"
      "kill-port": "kill-port/cli.js"
      "marked": "marked/bin/marked.js"
      "opener": "opener/bin/opener-bin.js"
      "js-yaml": "js-yaml/bin/js-yaml.js"
      "semver": "semver/bin/semver.js"
      "json5": "json5/lib/cli.js"
      "yaml2json": "yamljs/bin/yaml2json"
      "json2yaml": "yamljs/bin/json2yaml"
      "react-native": "react-native/cli.js"
      "nft": "@vercel/nft/out/cli.js"
      "vite-node": "vite-node/vite-node.mjs"
      "why-is-node-running": "why-is-node-running/cli.js"
      "vite": "vite/bin/vite.js"
      "glob": "glob/dist/esm/bin.mjs"
      "expo": "expo/bin/cli"
      "expo-modules-autolinking": "expo-modules-autolinking/bin/expo-modules-autolinking.js"
      "expo-updates": "expo-updates/bin/cli.js"
      "micro": "micro/bin/micro.js"
      "tree-kill": "tree-kill/cli.js"
      "vc": "vercel/dist/vc.js"
      "vercel": "vercel/dist/vc.js"
      "next": "next/dist/bin/next"
      "nanoid": "nanoid/bin/nanoid.cjs"
      "browserslist": "browserslist/cli.js"
      "cdl": "cardinal/bin/cdl.js"
      "mkdirp": "mkdirp/bin/cmd.js"
      "node-which": "which/bin/node-which"
      "acorn": "acorn/bin/acorn"
      "rollup": "rollup/dist/bin/rollup"
      "resolve": "resolve/bin/resolve"
      "terser": "terser/bin/terser"
      "print-chrome-path": "chrome-launcher/bin/print-chrome-path.js"
      "metro": "metro/src/cli.js"
      "metro-symbolicate": "metro-symbolicate/src/index.js"
      "tsgo": "@typescript/native-preview/bin/tsgo.js"
      "node-gyp": "node-gyp/bin/node-gyp.js"
      "mime": "mime/cli.js"
      "jsesc": "jsesc/bin/jsesc"
      "update-browserslist-db": "update-browserslist-db/cli.js"
      "loose-envify": "loose-envify/cli.js"
      "uuid": "uuid/dist/bin/uuid"
      "sucrase": "sucrase/bin/sucrase"
      "sucrase-node": "sucrase/bin/sucrase-node"
      "detect-libc": "detect-libc/bin/detect-libc.js"
      "lan-network": "lan-network/dist/lan-network-cli.js"
      "qrcode-terminal": "qrcode-terminal/bin/qrcode-terminal.js"
      "ua-parser-js": "ua-parser-js/script/cli.js"
      "edge-runtime": "edge-runtime/dist/cli/index.js"
      "html-minifier-terser": "html-minifier-terser/cli.js"
      "nopt": "nopt/bin/nopt.js"
      "esparse": "esprima/bin/esparse.js"
      "esvalidate": "esprima/bin/esvalidate.js"
      "image-size": "image-size/bin/image-size.js"
      "is-docker": "is-docker/cli.js"
      "rc": "rc/cli.js"
      "node-pre-gyp": "@mapbox/node-pre-gyp/bin/node-pre-gyp"
      "node-gyp-build": "node-gyp-build/bin.js"
      "node-gyp-build-optional": "node-gyp-build/optional.js"
      "node-gyp-build-test": "node-gyp-build/build-test.js"
      "he": "he/bin/he"
      "rimraf": "rimraf/dist/esm/bin.mjs"
      "cssesc": "cssesc/bin/cssesc"
      "regjsparser": "regjsparser/bin/parser"

"flat-cache@npm:4.0.1":
  locations:
    - "node_modules/flat-cache"

"flatted@npm:3.3.3":
  locations:
    - "node_modules/flatted"

"flow-enums-runtime@npm:0.0.6":
  locations:
    - "node_modules/flow-enums-runtime"

"fontfaceobserver@npm:2.3.0":
  locations:
    - "node_modules/fontfaceobserver"

"for-each@npm:0.3.5":
  locations:
    - "node_modules/for-each"

"foreground-child@npm:3.3.1":
  locations:
    - "node_modules/foreground-child"

"forwarded@npm:0.2.0":
  locations:
    - "node_modules/forwarded"

"framer-motion@virtual:353ae37cf5c852056c5762c29f5d0eabbcb1936237adaa7b4ffd334cb4d51283f1ca67801a3f80141db5c4f08bdbc50ab900821916c02405e6fd0731c23b3e1e#npm:6.5.1":
  locations:
    - "node_modules/framer-motion"

"framesync@npm:6.0.1":
  locations:
    - "node_modules/framesync"

"freeport-async@npm:2.0.0":
  locations:
    - "node_modules/freeport-async"

"fresh@npm:0.5.2":
  locations:
    - "node_modules/fresh"

"fs-extra@npm:11.1.0":
  locations:
    - "node_modules/@vercel/gatsby-plugin-vercel-builder/node_modules/fs-extra"

"fs-extra@npm:11.3.0":
  locations:
    - "node_modules/fs-extra"

"fs-minipass@npm:2.1.0":
  locations:
    - "node_modules/@vercel/fun/node_modules/fs-minipass"

"fs-minipass@npm:3.0.3":
  locations:
    - "node_modules/fs-minipass"

"fs.realpath@npm:1.0.0":
  locations:
    - "node_modules/fs.realpath"

"fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1":
  locations:
    - "node_modules/fsevents"

"function-bind@npm:1.1.2":
  locations:
    - "node_modules/function-bind"

"function.prototype.name@npm:1.1.8":
  locations:
    - "node_modules/function.prototype.name"

"functions-have-names@npm:1.2.3":
  locations:
    - "node_modules/functions-have-names"

"generic-pool@npm:3.4.2":
  locations:
    - "node_modules/generic-pool"

"gensync@npm:1.0.0-beta.2":
  locations:
    - "node_modules/gensync"

"get-caller-file@npm:2.0.5":
  locations:
    - "node_modules/get-caller-file"

"get-intrinsic@npm:1.3.0":
  locations:
    - "node_modules/get-intrinsic"

"get-nonce@npm:1.0.1":
  locations:
    - "node_modules/get-nonce"

"get-package-type@npm:0.1.0":
  locations:
    - "node_modules/get-package-type"

"get-port@npm:3.2.0":
  locations:
    - "node_modules/@expo/devcert/node_modules/get-port"

"get-port@npm:7.1.0":
  locations:
    - "node_modules/get-port"

"get-proto@npm:1.0.1":
  locations:
    - "node_modules/get-proto"

"get-stream@npm:6.0.1":
  locations:
    - "node_modules/get-stream"

"get-symbol-description@npm:1.1.0":
  locations:
    - "node_modules/get-symbol-description"

"get-them-args@npm:1.3.2":
  locations:
    - "node_modules/get-them-args"

"get-tsconfig@npm:4.10.0":
  locations:
    - "node_modules/get-tsconfig"

"getenv@npm:1.0.0":
  locations:
    - "node_modules/getenv"

"glob-parent@npm:5.1.2":
  locations:
    - "node_modules/glob-parent"

"glob-parent@npm:6.0.2":
  locations:
    - "node_modules/eslint/node_modules/glob-parent"

"glob@npm:10.3.10":
  locations:
    - "node_modules/@next/eslint-plugin-next/node_modules/glob"

"glob@npm:10.4.5":
  locations:
    - "node_modules/glob"

"glob@npm:7.2.3":
  locations:
    - "node_modules/yamljs/node_modules/glob"
    - "node_modules/test-exclude/node_modules/glob"
    - "node_modules/react-native/node_modules/glob"
    - "node_modules/chromium-edge-launcher/node_modules/glob"
    - "node_modules/@vercel/nft/node_modules/glob"
    - "node_modules/@react-native/codegen/node_modules/glob"

"glob@npm:9.3.5":
  locations:
    - "node_modules/babel-plugin-module-resolver/node_modules/glob"

"globals@npm:11.12.0":
  locations:
    - "node_modules/globals"

"globals@npm:14.0.0":
  locations:
    - "node_modules/@eslint/eslintrc/node_modules/globals"

"globalthis@npm:1.0.4":
  locations:
    - "node_modules/globalthis"

"globby@npm:13.2.2":
  locations:
    - "node_modules/globby"

"globrex@npm:0.1.2":
  locations:
    - "node_modules/globrex"

"gopd@npm:1.2.0":
  locations:
    - "node_modules/gopd"

"graceful-fs@npm:4.2.11":
  locations:
    - "node_modules/graceful-fs"

"graphemer@npm:1.4.0":
  locations:
    - "node_modules/graphemer"

"has-bigints@npm:1.1.0":
  locations:
    - "node_modules/has-bigints"

"has-flag@npm:3.0.0":
  locations:
    - "node_modules/ora/node_modules/has-flag"
    - "node_modules/log-symbols/node_modules/has-flag"
    - "node_modules/@babel/highlight/node_modules/has-flag"

"has-flag@npm:4.0.0":
  locations:
    - "node_modules/has-flag"

"has-property-descriptors@npm:1.0.2":
  locations:
    - "node_modules/has-property-descriptors"

"has-proto@npm:1.2.0":
  locations:
    - "node_modules/has-proto"

"has-symbols@npm:1.1.0":
  locations:
    - "node_modules/has-symbols"

"has-tostringtag@npm:1.0.2":
  locations:
    - "node_modules/has-tostringtag"

"hasown@npm:2.0.2":
  locations:
    - "node_modules/hasown"

"he@npm:1.2.0":
  locations:
    - "node_modules/he"

"header-case@npm:2.0.4":
  locations:
    - "node_modules/header-case"

"hermes-estree@npm:0.25.1":
  locations:
    - "node_modules/hermes-estree"

"hermes-estree@npm:0.28.1":
  locations:
    - "node_modules/metro/node_modules/hermes-estree"
    - "node_modules/metro-babel-transformer/node_modules/hermes-estree"

"hermes-parser@npm:0.25.1":
  locations:
    - "node_modules/hermes-parser"

"hermes-parser@npm:0.28.1":
  locations:
    - "node_modules/metro/node_modules/hermes-parser"
    - "node_modules/metro-babel-transformer/node_modules/hermes-parser"

"hey-listen@npm:1.0.8":
  locations:
    - "node_modules/hey-listen"

"hoist-non-react-statics@npm:3.3.2":
  locations:
    - "node_modules/hoist-non-react-statics"

"hosted-git-info@npm:7.0.2":
  locations:
    - "node_modules/hosted-git-info"

"html-minifier-terser@npm:6.1.0":
  locations:
    - "node_modules/html-minifier-terser"

"html-webpack-plugin@virtual:2ed9a9ca06dbfcd7161ed29975c6ac3650af7a2e17f09203c7a1718e71ec47203605a304723cc72d6c65795be10ba94c65f3fcc0298a05a4bf42540d3fead0e8#npm:5.6.3":
  locations:
    - "node_modules/html-webpack-plugin"

"htmlparser2@npm:6.1.0":
  locations:
    - "node_modules/htmlparser2"

"http-cache-semantics@npm:4.1.1":
  locations:
    - "node_modules/http-cache-semantics"

"http-errors@npm:1.4.0":
  locations:
    - "node_modules/path-match/node_modules/http-errors"

"http-errors@npm:1.7.3":
  locations:
    - "node_modules/micro/node_modules/http-errors"

"http-errors@npm:2.0.0":
  locations:
    - "node_modules/http-errors"

"http-proxy-agent@npm:7.0.2":
  locations:
    - "node_modules/http-proxy-agent"

"https-proxy-agent@npm:7.0.6":
  locations:
    - "node_modules/https-proxy-agent"

"human-signals@npm:2.1.0":
  locations:
    - "node_modules/human-signals"

"husky@npm:9.1.7":
  locations:
    - "node_modules/husky"

"hyphenate-style-name@npm:1.1.0":
  locations:
    - "node_modules/hyphenate-style-name"

"iconv-lite@npm:0.4.24":
  locations:
    - "node_modules/iconv-lite"

"iconv-lite@npm:0.6.3":
  locations:
    - "node_modules/encoding/node_modules/iconv-lite"

"icss-utils@virtual:c6e64c7d2c17acf36982e31e871fb3b8cd4ec62ce14f34a4d8a635923f4adc081b59ba9b8da1c90a0a13cf95dcc72c1190636a999deade31c7819261fae76a01#npm:5.1.0":
  locations:
    - "node_modules/icss-utils"

"ieee754@npm:1.2.1":
  locations:
    - "node_modules/ieee754"

"ignore@npm:5.3.2":
  locations:
    - "node_modules/ignore"

"image-size@npm:1.2.0":
  locations:
    - "node_modules/image-size"

"import-fresh@npm:2.0.0":
  locations:
    - "node_modules/cosmiconfig/node_modules/import-fresh"

"import-fresh@npm:3.3.1":
  locations:
    - "node_modules/import-fresh"

"imurmurhash@npm:0.1.4":
  locations:
    - "node_modules/imurmurhash"

"inflight@npm:1.0.6":
  locations:
    - "node_modules/inflight"

"inherits@npm:2.0.1":
  locations:
    - "node_modules/path-match/node_modules/inherits"

"inherits@npm:2.0.4":
  locations:
    - "node_modules/inherits"

"ini@npm:1.3.8":
  locations:
    - "node_modules/ini"

"inline-style-prefixer@npm:6.0.4":
  locations:
    - "node_modules/inline-style-prefixer"

"internal-slot@npm:1.1.0":
  locations:
    - "node_modules/internal-slot"

"invariant@npm:2.2.4":
  locations:
    - "node_modules/invariant"

"ip-address@npm:9.0.5":
  locations:
    - "node_modules/ip-address"

"ipaddr.js@npm:1.9.1":
  locations:
    - "node_modules/ipaddr.js"

"is-array-buffer@npm:3.0.5":
  locations:
    - "node_modules/is-array-buffer"

"is-arrayish@npm:0.2.1":
  locations:
    - "node_modules/error-ex/node_modules/is-arrayish"

"is-arrayish@npm:0.3.2":
  locations:
    - "node_modules/is-arrayish"

"is-async-function@npm:2.1.1":
  locations:
    - "node_modules/is-async-function"

"is-bigint@npm:1.1.0":
  locations:
    - "node_modules/is-bigint"

"is-binary-path@npm:2.1.0":
  locations:
    - "node_modules/is-binary-path"

"is-boolean-object@npm:1.2.2":
  locations:
    - "node_modules/is-boolean-object"

"is-bun-module@npm:1.3.0":
  locations:
    - "node_modules/is-bun-module"

"is-callable@npm:1.2.7":
  locations:
    - "node_modules/is-callable"

"is-core-module@npm:2.16.1":
  locations:
    - "node_modules/is-core-module"

"is-data-view@npm:1.0.2":
  locations:
    - "node_modules/is-data-view"

"is-date-object@npm:1.1.0":
  locations:
    - "node_modules/is-date-object"

"is-directory@npm:0.3.1":
  locations:
    - "node_modules/is-directory"

"is-docker@npm:2.2.1":
  locations:
    - "node_modules/is-docker"

"is-extglob@npm:2.1.1":
  locations:
    - "node_modules/is-extglob"

"is-finalizationregistry@npm:1.1.1":
  locations:
    - "node_modules/is-finalizationregistry"

"is-fullwidth-code-point@npm:3.0.0":
  locations:
    - "node_modules/is-fullwidth-code-point"

"is-generator-function@npm:1.1.0":
  locations:
    - "node_modules/is-generator-function"

"is-glob@npm:4.0.3":
  locations:
    - "node_modules/is-glob"

"is-map@npm:2.0.3":
  locations:
    - "node_modules/is-map"

"is-number-object@npm:1.1.1":
  locations:
    - "node_modules/is-number-object"

"is-number@npm:7.0.0":
  locations:
    - "node_modules/is-number"

"is-plain-object@npm:2.0.4":
  locations:
    - "node_modules/is-plain-object"

"is-primitive@npm:3.0.1":
  locations:
    - "node_modules/is-primitive"

"is-regex@npm:1.2.1":
  locations:
    - "node_modules/is-regex"

"is-set@npm:2.0.3":
  locations:
    - "node_modules/is-set"

"is-shared-array-buffer@npm:1.0.4":
  locations:
    - "node_modules/is-shared-array-buffer"

"is-stream@npm:2.0.1":
  locations:
    - "node_modules/is-stream"

"is-string@npm:1.1.1":
  locations:
    - "node_modules/is-string"

"is-symbol@npm:1.1.1":
  locations:
    - "node_modules/is-symbol"

"is-typed-array@npm:1.1.15":
  locations:
    - "node_modules/is-typed-array"

"is-weakmap@npm:2.0.2":
  locations:
    - "node_modules/is-weakmap"

"is-weakref@npm:1.1.1":
  locations:
    - "node_modules/is-weakref"

"is-weakset@npm:2.0.4":
  locations:
    - "node_modules/is-weakset"

"is-wsl@npm:2.2.0":
  locations:
    - "node_modules/is-wsl"

"isarray@npm:0.0.1":
  locations:
    - "node_modules/path-match/node_modules/isarray"

"isarray@npm:2.0.5":
  locations:
    - "node_modules/isarray"

"isexe@npm:2.0.0":
  locations:
    - "node_modules/isexe"

"isexe@npm:3.1.1":
  locations:
    - "node_modules/node-gyp/node_modules/isexe"

"isobject@npm:3.0.1":
  locations:
    - "node_modules/isobject"

"istanbul-lib-coverage@npm:3.2.2":
  locations:
    - "node_modules/istanbul-lib-coverage"

"istanbul-lib-instrument@npm:5.2.1":
  locations:
    - "node_modules/istanbul-lib-instrument"

"iterate-object@npm:1.3.5":
  locations:
    - "node_modules/iterate-object"

"iterator.prototype@npm:1.1.5":
  locations:
    - "node_modules/iterator.prototype"

"jackspeak@npm:2.3.6":
  locations:
    - "node_modules/@next/eslint-plugin-next/node_modules/jackspeak"

"jackspeak@npm:3.4.3":
  locations:
    - "node_modules/jackspeak"

"jest-environment-node@npm:29.7.0":
  locations:
    - "node_modules/jest-environment-node"

"jest-get-type@npm:29.6.3":
  locations:
    - "node_modules/jest-get-type"

"jest-haste-map@npm:29.7.0":
  locations:
    - "node_modules/jest-haste-map"

"jest-message-util@npm:29.7.0":
  locations:
    - "node_modules/jest-message-util"

"jest-mock@npm:29.7.0":
  locations:
    - "node_modules/jest-mock"

"jest-regex-util@npm:29.6.3":
  locations:
    - "node_modules/jest-regex-util"

"jest-util@npm:29.7.0":
  locations:
    - "node_modules/jest-util"

"jest-validate@npm:29.7.0":
  locations:
    - "node_modules/jest-validate"

"jest-worker@npm:29.7.0":
  locations:
    - "node_modules/jest-worker"

"jimp-compact@npm:0.16.1":
  locations:
    - "node_modules/jimp-compact"

"js-tokens@npm:4.0.0":
  locations:
    - "node_modules/js-tokens"

"js-yaml@npm:3.14.1":
  locations:
    - "node_modules/cosmiconfig/node_modules/js-yaml"
    - "node_modules/@istanbuljs/load-nyc-config/node_modules/js-yaml"

"js-yaml@npm:4.1.0":
  locations:
    - "node_modules/js-yaml"

"jsbn@npm:1.1.0":
  locations:
    - "node_modules/jsbn"

"jsc-safe-url@npm:0.2.4":
  locations:
    - "node_modules/jsc-safe-url"

"jsesc@npm:3.0.2":
  locations:
    - "node_modules/regjsparser/node_modules/jsesc"

"jsesc@npm:3.1.0":
  locations:
    - "node_modules/jsesc"

"json-buffer@npm:3.0.1":
  locations:
    - "node_modules/json-buffer"

"json-parse-better-errors@npm:1.0.2":
  locations:
    - "node_modules/json-parse-better-errors"

"json-schema-to-ts@npm:1.6.4":
  locations:
    - "node_modules/json-schema-to-ts"

"json-schema-traverse@npm:0.4.1":
  locations:
    - "node_modules/url-loader/node_modules/json-schema-traverse"
    - "node_modules/file-loader/node_modules/json-schema-traverse"
    - "node_modules/eslint/node_modules/json-schema-traverse"
    - "node_modules/@eslint/eslintrc/node_modules/json-schema-traverse"

"json-schema-traverse@npm:1.0.0":
  locations:
    - "node_modules/json-schema-traverse"

"json-stable-stringify-without-jsonify@npm:1.0.1":
  locations:
    - "node_modules/json-stable-stringify-without-jsonify"

"json5@npm:1.0.2":
  locations:
    - "node_modules/tsconfig-paths/node_modules/json5"

"json5@npm:2.2.3":
  locations:
    - "node_modules/json5"

"jsonfile@npm:6.1.0":
  locations:
    - "node_modules/jsonfile"

"jsx-ast-utils@npm:3.3.5":
  locations:
    - "node_modules/jsx-ast-utils"

"keyv@npm:4.5.4":
  locations:
    - "node_modules/keyv"

"kill-port@npm:2.0.1":
  locations:
    - "node_modules/kill-port"

"kleur@npm:3.0.3":
  locations:
    - "node_modules/kleur"

"lan-network@npm:0.1.6":
  locations:
    - "node_modules/lan-network"

"language-subtag-registry@npm:0.3.23":
  locations:
    - "node_modules/language-subtag-registry"

"language-tags@npm:1.0.9":
  locations:
    - "node_modules/language-tags"

"leven@npm:3.1.0":
  locations:
    - "node_modules/leven"

"levn@npm:0.4.1":
  locations:
    - "node_modules/levn"

"lighthouse-logger@npm:1.4.2":
  locations:
    - "node_modules/lighthouse-logger"

"lightningcss-darwin-arm64@npm:1.27.0":
  locations:
    - "node_modules/lightningcss-darwin-arm64"

"lightningcss@npm:1.27.0":
  locations:
    - "node_modules/lightningcss"

"lines-and-columns@npm:1.2.4":
  locations:
    - "node_modules/lines-and-columns"

"loader-utils@npm:2.0.4":
  locations:
    - "node_modules/loader-utils"

"loader-utils@npm:3.3.1":
  locations:
    - "node_modules/tamagui-loader/node_modules/loader-utils"

"locate-path@npm:3.0.0":
  locations:
    - "node_modules/pkg-up/node_modules/locate-path"

"locate-path@npm:5.0.0":
  locations:
    - "node_modules/locate-path"

"locate-path@npm:6.0.0":
  locations:
    - "node_modules/find-up/node_modules/locate-path"

"locate-path@npm:7.2.0":
  locations:
    - "node_modules/babel-loader/node_modules/locate-path"

"lodash.debounce@npm:4.0.8":
  locations:
    - "node_modules/lodash.debounce"

"lodash.merge@npm:4.6.2":
  locations:
    - "node_modules/lodash.merge"

"lodash.throttle@npm:4.1.1":
  locations:
    - "node_modules/lodash.throttle"

"lodash.truncate@npm:4.4.2":
  locations:
    - "node_modules/lodash.truncate"

"lodash@npm:4.17.21":
  locations:
    - "node_modules/lodash"

"log-symbols@npm:2.2.0":
  locations:
    - "node_modules/log-symbols"

"loose-envify@npm:1.4.0":
  locations:
    - "node_modules/loose-envify"

"loupe@npm:3.1.3":
  locations:
    - "node_modules/loupe"

"lower-case@npm:2.0.2":
  locations:
    - "node_modules/lower-case"

"lru-cache@npm:10.4.3":
  locations:
    - "node_modules/lru-cache"

"lru-cache@npm:5.1.1":
  locations:
    - "node_modules/@babel/helper-compilation-targets/node_modules/lru-cache"

"lru-cache@npm:6.0.0":
  locations:
    - "node_modules/@vercel/fun/node_modules/lru-cache"

"magic-string@npm:0.30.17":
  locations:
    - "node_modules/magic-string"

"make-dir@npm:3.1.0":
  locations:
    - "node_modules/make-dir"

"make-error@npm:1.3.6":
  locations:
    - "node_modules/make-error"

"make-fetch-happen@npm:14.0.3":
  locations:
    - "node_modules/make-fetch-happen"

"makeerror@npm:1.0.12":
  locations:
    - "node_modules/makeerror"

"marked-terminal@virtual:2b362252813ee0568c9f504c4a15e653428554df3f5dcbc679c51f64b5ec1ae40f91d1c4376ca2b32eab8915cab5e421a677511bbbbecd577495c7adf3499f5c#npm:5.2.0":
  locations:
    - "node_modules/marked-terminal"

"marked@npm:14.1.4":
  locations:
    - "node_modules/marked"

"marky@npm:1.2.5":
  locations:
    - "node_modules/marky"

"math-intrinsics@npm:1.1.0":
  locations:
    - "node_modules/math-intrinsics"

"mdn-data@npm:2.0.14":
  locations:
    - "node_modules/mdn-data"

"media-typer@npm:0.3.0":
  locations:
    - "node_modules/media-typer"

"memoize-one@npm:5.2.1":
  locations:
    - "node_modules/react-native/node_modules/memoize-one"

"memoize-one@npm:6.0.0":
  locations:
    - "node_modules/memoize-one"

"merge-descriptors@npm:1.0.3":
  locations:
    - "node_modules/merge-descriptors"

"merge-stream@npm:2.0.0":
  locations:
    - "node_modules/merge-stream"

"merge2@npm:1.4.1":
  locations:
    - "node_modules/merge2"

"methods@npm:1.1.2":
  locations:
    - "node_modules/methods"

"metro-babel-transformer@npm:0.82.3":
  locations:
    - "node_modules/metro-babel-transformer"

"metro-cache-key@npm:0.82.3":
  locations:
    - "node_modules/metro-cache-key"

"metro-cache@npm:0.82.3":
  locations:
    - "node_modules/metro-cache"

"metro-config@npm:0.82.3":
  locations:
    - "node_modules/metro-config"

"metro-core@npm:0.82.3":
  locations:
    - "node_modules/metro-core"

"metro-file-map@npm:0.82.3":
  locations:
    - "node_modules/metro-file-map"

"metro-minify-terser@npm:0.81.2":
  locations:
    - "node_modules/metro-minify-terser"

"metro-minify-terser@npm:0.82.3":
  locations:
    - "node_modules/metro-transform-worker/node_modules/metro-minify-terser"

"metro-resolver@npm:0.82.3":
  locations:
    - "node_modules/metro-resolver"

"metro-runtime@npm:0.82.3":
  locations:
    - "node_modules/metro-runtime"

"metro-source-map@npm:0.82.3":
  locations:
    - "node_modules/metro-source-map"

"metro-symbolicate@npm:0.82.3":
  locations:
    - "node_modules/metro-symbolicate"

"metro-transform-plugins@npm:0.82.3":
  locations:
    - "node_modules/metro-transform-plugins"

"metro-transform-worker@npm:0.82.3":
  locations:
    - "node_modules/metro-transform-worker"

"metro@npm:0.82.3":
  locations:
    - "node_modules/metro"

"micro-memoize@npm:4.1.3":
  locations:
    - "node_modules/micro-memoize"

"micro@npm:9.3.5-canary.3":
  locations:
    - "node_modules/micro"

"micromatch@npm:4.0.8":
  locations:
    - "node_modules/micromatch"

"mime-db@npm:1.52.0":
  locations:
    - "node_modules/mime-db"

"mime-db@npm:1.53.0":
  locations:
    - "node_modules/compressible/node_modules/mime-db"

"mime-types@npm:2.1.35":
  locations:
    - "node_modules/mime-types"

"mime@npm:1.6.0":
  locations:
    - "node_modules/mime"

"mimic-fn@npm:1.2.0":
  locations:
    - "node_modules/mimic-fn"

"mimic-fn@npm:2.1.0":
  locations:
    - "node_modules/execa/node_modules/mimic-fn"

"minimatch@npm:3.1.2":
  locations:
    - "node_modules/minimatch"

"minimatch@npm:5.1.6":
  locations:
    - "node_modules/@ts-morph/common/node_modules/minimatch"

"minimatch@npm:8.0.4":
  locations:
    - "node_modules/babel-plugin-module-resolver/node_modules/minimatch"

"minimatch@npm:9.0.5":
  locations:
    - "node_modules/glob/node_modules/minimatch"
    - "node_modules/@typescript-eslint/typescript-estree/node_modules/minimatch"
    - "node_modules/@next/eslint-plugin-next/node_modules/minimatch"
    - "node_modules/@expo/metro-config/node_modules/minimatch"
    - "node_modules/@expo/fingerprint/node_modules/minimatch"
    - "node_modules/@expo/cli/node_modules/minimatch"

"minimist@npm:1.2.8":
  locations:
    - "node_modules/minimist"

"minipass-collect@npm:2.0.1":
  locations:
    - "node_modules/minipass-collect"

"minipass-fetch@npm:4.0.1":
  locations:
    - "node_modules/minipass-fetch"

"minipass-flush@npm:1.0.5":
  locations:
    - "node_modules/minipass-flush"

"minipass-pipeline@npm:1.2.4":
  locations:
    - "node_modules/minipass-pipeline"

"minipass-sized@npm:1.0.3":
  locations:
    - "node_modules/minipass-sized"

"minipass@npm:3.3.6":
  locations:
    - "node_modules/minipass-sized/node_modules/minipass"
    - "node_modules/minipass-pipeline/node_modules/minipass"
    - "node_modules/minipass-flush/node_modules/minipass"
    - "node_modules/@vercel/fun/node_modules/minipass"

"minipass@npm:4.2.8":
  locations:
    - "node_modules/babel-plugin-module-resolver/node_modules/minipass"

"minipass@npm:5.0.0":
  locations:
    - "node_modules/@vercel/fun/node_modules/tar/node_modules/minipass"

"minipass@npm:7.1.2":
  locations:
    - "node_modules/minipass"

"minizlib@npm:2.1.2":
  locations:
    - "node_modules/@vercel/fun/node_modules/minizlib"

"minizlib@npm:3.0.1":
  locations:
    - "node_modules/minizlib"

"mkdirp@npm:0.5.6":
  locations:
    - "node_modules/@expo/devcert/node_modules/mkdirp"

"mkdirp@npm:1.0.4":
  locations:
    - "node_modules/mkdirp"

"mkdirp@npm:3.0.1":
  locations:
    - "node_modules/tar/node_modules/mkdirp"

"moti@virtual:eae622d80052ef57ed377ffa9cde1e1bc48b85690a1515a40c30d9be900a801c89b5da651fd6896e8e7f4849de771632c93a6e48e0f39d92a5a549ef3d7fb20e#npm:0.30.0":
  locations:
    - "node_modules/moti"

"mri@npm:1.2.0":
  locations:
    - "node_modules/mri"

"ms@npm:2.0.0":
  locations:
    - "node_modules/send/node_modules/debug/node_modules/ms"
    - "node_modules/lighthouse-logger/node_modules/ms"
    - "node_modules/finalhandler/node_modules/ms"
    - "node_modules/express/node_modules/ms"
    - "node_modules/connect/node_modules/ms"
    - "node_modules/compression/node_modules/ms"
    - "node_modules/body-parser/node_modules/ms"
    - "node_modules/@react-native/dev-middleware/node_modules/ms"
    - "node_modules/@react-native/community-cli-plugin/node_modules/ms"
    - "node_modules/@expo/cli/node_modules/send/node_modules/debug/node_modules/ms"

"ms@npm:2.1.1":
  locations:
    - "node_modules/@vercel/fun/node_modules/ms"

"ms@npm:2.1.2":
  locations:
    - "node_modules/@vercel/fun/node_modules/debug/node_modules/ms"

"ms@npm:2.1.3":
  locations:
    - "node_modules/ms"

"mz@npm:2.7.0":
  locations:
    - "node_modules/mz"

"nanoid@npm:3.3.11":
  locations:
    - "node_modules/nanoid"

"natural-compare@npm:1.4.0":
  locations:
    - "node_modules/natural-compare"

"negotiator@npm:0.6.3":
  locations:
    - "node_modules/accepts/node_modules/negotiator"

"negotiator@npm:0.6.4":
  locations:
    - "node_modules/compression/node_modules/negotiator"

"negotiator@npm:1.0.0":
  locations:
    - "node_modules/negotiator"

"nested-error-stacks@npm:2.0.1":
  locations:
    - "node_modules/nested-error-stacks"

"next-app@workspace:apps/next":
  locations:
    - "node_modules/next-app"

"next-compose-plugins@npm:2.2.1":
  locations:
    - "node_modules/next-compose-plugins"

"next-transpile-modules@npm:10.0.1":
  locations:
    - "node_modules/next-transpile-modules"

"next@virtual:1e21ed5e542f04ac0725f80f20538bd288a2542912556a71424e0ef86756859fcedbc2507675d93c28d09cc2e230d8793e424a169e257d21ba5cb20fce2d2238#npm:14.2.14":
  locations:
    - "node_modules/next"

"no-case@npm:3.0.4":
  locations:
    - "node_modules/no-case"

"node-emoji@npm:1.11.0":
  locations:
    - "node_modules/node-emoji"

"node-fetch@virtual:267029ff2f85c65479d29496cc50976fb522ed8beda97f97e23b49194e83bd9867cfd15af96b05ad656edae653eb3a95a72ba015ac1556bfdf9591902b5e5ecc#npm:2.7.0":
  locations:
    - "node_modules/node-fetch"

"node-fetch@virtual:930f7e38f02586080a23d47794f474516dbd6e592ae78b8b1c1a09855658a4a9d2cbc03309d7f03307af737fa32a71f8874d9d7a2c42e0fa6b06659f13878935#npm:2.6.7":
  locations:
    - "node_modules/@vercel/fun/node_modules/node-fetch"

"node-fetch@virtual:b4304646f8ce99c4dcc649f59d588ddca9df5ca3308166a45d622f1ef18bd9b3ae397d4e25873de136dca2f32111483c07998816f2c4559d015efdda3fcb27a0#npm:2.6.9":
  locations:
    - "node_modules/@vercel/node/node_modules/node-fetch"

"node-forge@npm:1.3.1":
  locations:
    - "node_modules/node-forge"

"node-gyp-build@npm:4.8.4":
  locations:
    - "node_modules/node-gyp-build"

"node-gyp@npm:11.1.0":
  locations:
    - "node_modules/node-gyp"

"node-int64@npm:0.4.0":
  locations:
    - "node_modules/node-int64"

"node-releases@npm:2.0.19":
  locations:
    - "node_modules/node-releases"

"nopt@npm:8.1.0":
  locations:
    - "node_modules/nopt"

"normalize-path@npm:3.0.0":
  locations:
    - "node_modules/normalize-path"

"npm-package-arg@npm:11.0.3":
  locations:
    - "node_modules/npm-package-arg"

"npm-run-path@npm:4.0.1":
  locations:
    - "node_modules/npm-run-path"

"nth-check@npm:2.1.1":
  locations:
    - "node_modules/nth-check"

"nullthrows@npm:1.1.1":
  locations:
    - "node_modules/nullthrows"

"ob1@npm:0.82.3":
  locations:
    - "node_modules/ob1"

"object-assign@npm:4.1.1":
  locations:
    - "node_modules/object-assign"

"object-inspect@npm:1.13.4":
  locations:
    - "node_modules/object-inspect"

"object-keys@npm:1.1.1":
  locations:
    - "node_modules/object-keys"

"object.assign@npm:4.1.7":
  locations:
    - "node_modules/object.assign"

"object.entries@npm:1.1.8":
  locations:
    - "node_modules/object.entries"

"object.fromentries@npm:2.0.8":
  locations:
    - "node_modules/object.fromentries"

"object.groupby@npm:1.0.3":
  locations:
    - "node_modules/object.groupby"

"object.values@npm:1.2.1":
  locations:
    - "node_modules/object.values"

"on-finished@npm:2.3.0":
  locations:
    - "node_modules/connect/node_modules/on-finished"

"on-finished@npm:2.4.1":
  locations:
    - "node_modules/on-finished"

"on-headers@npm:1.0.2":
  locations:
    - "node_modules/on-headers"

"once@npm:1.3.3":
  locations:
    - "node_modules/end-of-stream/node_modules/once"

"once@npm:1.4.0":
  locations:
    - "node_modules/once"

"onetime@npm:2.0.1":
  locations:
    - "node_modules/onetime"

"onetime@npm:5.1.2":
  locations:
    - "node_modules/execa/node_modules/onetime"

"open@npm:7.4.2":
  locations:
    - "node_modules/open"

"open@npm:8.4.2":
  locations:
    - "node_modules/better-opn/node_modules/open"

"opener@npm:1.5.2":
  locations:
    - "node_modules/opener"

"optionator@npm:0.9.4":
  locations:
    - "node_modules/optionator"

"ora@npm:3.4.0":
  locations:
    - "node_modules/ora"

"os-paths@npm:4.4.0":
  locations:
    - "node_modules/os-paths"

"os-tmpdir@npm:1.0.2":
  locations:
    - "node_modules/os-tmpdir"

"outdent@npm:0.8.0":
  locations:
    - "node_modules/outdent"

"own-keys@npm:1.0.1":
  locations:
    - "node_modules/own-keys"

"oxc-transform@npm:0.74.0":
  locations:
    - "node_modules/oxc-transform"

"p-limit@npm:2.3.0":
  locations:
    - "node_modules/p-limit"

"p-limit@npm:3.1.0":
  locations:
    - "node_modules/find-up/node_modules/p-limit"
    - "node_modules/@expo/fingerprint/node_modules/p-limit"

"p-limit@npm:4.0.0":
  locations:
    - "node_modules/babel-loader/node_modules/p-limit"

"p-locate@npm:3.0.0":
  locations:
    - "node_modules/pkg-up/node_modules/p-locate"

"p-locate@npm:4.1.0":
  locations:
    - "node_modules/p-locate"

"p-locate@npm:5.0.0":
  locations:
    - "node_modules/find-up/node_modules/p-locate"

"p-locate@npm:6.0.0":
  locations:
    - "node_modules/babel-loader/node_modules/p-locate"

"p-map@npm:7.0.3":
  locations:
    - "node_modules/p-map"

"p-try@npm:2.2.0":
  locations:
    - "node_modules/p-try"

"package-json-from-dist@npm:1.0.1":
  locations:
    - "node_modules/package-json-from-dist"

"param-case@npm:3.0.4":
  locations:
    - "node_modules/param-case"

"parent-module@npm:1.0.1":
  locations:
    - "node_modules/parent-module"

"parse-json@npm:4.0.0":
  locations:
    - "node_modules/parse-json"

"parse-ms@npm:2.1.0":
  locations:
    - "node_modules/parse-ms"

"parse-png@npm:2.1.0":
  locations:
    - "node_modules/parse-png"

"parseurl@npm:1.3.3":
  locations:
    - "node_modules/parseurl"

"pascal-case@npm:3.1.2":
  locations:
    - "node_modules/pascal-case"

"password-prompt@npm:1.1.3":
  locations:
    - "node_modules/password-prompt"

"path-browserify@npm:1.0.1":
  locations:
    - "node_modules/path-browserify"

"path-case@npm:3.0.4":
  locations:
    - "node_modules/path-case"

"path-exists@npm:3.0.0":
  locations:
    - "node_modules/pkg-up/node_modules/path-exists"

"path-exists@npm:4.0.0":
  locations:
    - "node_modules/path-exists"

"path-exists@npm:5.0.0":
  locations:
    - "node_modules/babel-loader/node_modules/path-exists"

"path-is-absolute@npm:1.0.1":
  locations:
    - "node_modules/path-is-absolute"

"path-key@npm:3.1.1":
  locations:
    - "node_modules/path-key"

"path-match@npm:1.2.4":
  locations:
    - "node_modules/path-match"

"path-parse@npm:1.0.7":
  locations:
    - "node_modules/path-parse"

"path-scurry@npm:1.11.1":
  locations:
    - "node_modules/path-scurry"

"path-to-regexp@npm:0.1.12":
  locations:
    - "node_modules/express/node_modules/path-to-regexp"

"path-to-regexp@npm:1.9.0":
  locations:
    - "node_modules/path-match/node_modules/path-to-regexp"

"path-to-regexp@npm:6.1.0":
  locations:
    - "node_modules/path-to-regexp"

"path-to-regexp@npm:6.3.0":
  locations:
    - "node_modules/path-to-regexp-updated"

"path-type@npm:4.0.0":
  locations:
    - "node_modules/path-type"

"pathe@npm:1.1.2":
  locations:
    - "node_modules/pathe"

"pathval@npm:2.0.0":
  locations:
    - "node_modules/pathval"

"pend@npm:1.2.0":
  locations:
    - "node_modules/pend"

"performance-now@npm:2.1.0":
  locations:
    - "node_modules/performance-now"

"picocolors@npm:1.0.0":
  locations:
    - "node_modules/edge-runtime/node_modules/picocolors"

"picocolors@npm:1.1.1":
  locations:
    - "node_modules/picocolors"

"picomatch@npm:2.3.1":
  locations:
    - "node_modules/readdirp/node_modules/picomatch"
    - "node_modules/micromatch/node_modules/picomatch"
    - "node_modules/jest-util/node_modules/picomatch"
    - "node_modules/anymatch/node_modules/picomatch"

"picomatch@npm:3.0.1":
  locations:
    - "node_modules/@expo/cli/node_modules/picomatch"

"picomatch@npm:4.0.2":
  locations:
    - "node_modules/picomatch"

"pid-cwd@npm:1.2.0":
  locations:
    - "node_modules/pid-cwd"

"pirates@npm:4.0.6":
  locations:
    - "node_modules/pirates"

"pkg-dir@npm:4.2.0":
  locations:
    - "node_modules/pkg-dir"

"pkg-dir@npm:7.0.0":
  locations:
    - "node_modules/babel-loader/node_modules/pkg-dir"

"pkg-up@npm:3.1.0":
  locations:
    - "node_modules/pkg-up"

"plist@npm:3.1.0":
  locations:
    - "node_modules/plist"

"pngjs@npm:3.4.0":
  locations:
    - "node_modules/pngjs"

"popmotion@npm:11.0.3":
  locations:
    - "node_modules/popmotion"

"possible-typed-array-names@npm:1.1.0":
  locations:
    - "node_modules/possible-typed-array-names"

"postcss-modules-extract-imports@virtual:c6e64c7d2c17acf36982e31e871fb3b8cd4ec62ce14f34a4d8a635923f4adc081b59ba9b8da1c90a0a13cf95dcc72c1190636a999deade31c7819261fae76a01#npm:3.1.0":
  locations:
    - "node_modules/postcss-modules-extract-imports"

"postcss-modules-local-by-default@virtual:c6e64c7d2c17acf36982e31e871fb3b8cd4ec62ce14f34a4d8a635923f4adc081b59ba9b8da1c90a0a13cf95dcc72c1190636a999deade31c7819261fae76a01#npm:4.2.0":
  locations:
    - "node_modules/postcss-modules-local-by-default"

"postcss-modules-scope@virtual:c6e64c7d2c17acf36982e31e871fb3b8cd4ec62ce14f34a4d8a635923f4adc081b59ba9b8da1c90a0a13cf95dcc72c1190636a999deade31c7819261fae76a01#npm:3.2.1":
  locations:
    - "node_modules/postcss-modules-scope"

"postcss-modules-values@virtual:c6e64c7d2c17acf36982e31e871fb3b8cd4ec62ce14f34a4d8a635923f4adc081b59ba9b8da1c90a0a13cf95dcc72c1190636a999deade31c7819261fae76a01#npm:4.0.0":
  locations:
    - "node_modules/postcss-modules-values"

"postcss-selector-parser@npm:7.1.0":
  locations:
    - "node_modules/postcss-selector-parser"

"postcss-value-parser@npm:4.2.0":
  locations:
    - "node_modules/postcss-value-parser"

"postcss@npm:8.4.31":
  locations:
    - "node_modules/next/node_modules/postcss"

"postcss@npm:8.4.49":
  locations:
    - "node_modules/@expo/metro-config/node_modules/postcss"

"postcss@npm:8.5.3":
  locations:
    - "node_modules/postcss"

"prelude-ls@npm:1.2.1":
  locations:
    - "node_modules/prelude-ls"

"prettier@npm:3.5.2":
  locations:
    - "node_modules/prettier"

"pretty-bytes@npm:5.6.0":
  locations:
    - "node_modules/pretty-bytes"

"pretty-error@npm:4.0.0":
  locations:
    - "node_modules/pretty-error"

"pretty-format@npm:29.7.0":
  locations:
    - "node_modules/pretty-format"

"pretty-ms@npm:7.0.1":
  locations:
    - "node_modules/pretty-ms"

"proc-log@npm:4.2.0":
  locations:
    - "node_modules/npm-package-arg/node_modules/proc-log"

"proc-log@npm:5.0.0":
  locations:
    - "node_modules/proc-log"

"progress@npm:2.0.3":
  locations:
    - "node_modules/progress"

"promise-retry@npm:2.0.1":
  locations:
    - "node_modules/promise-retry"

"promise@npm:7.3.1":
  locations:
    - "node_modules/promise"

"promise@npm:8.3.0":
  locations:
    - "node_modules/react-native/node_modules/promise"

"promisepipe@npm:3.0.0":
  locations:
    - "node_modules/promisepipe"

"prompts@npm:2.1.0":
  locations:
    - "node_modules/prompts"

"prompts@npm:2.4.2":
  locations:
    - "node_modules/@expo/cli/node_modules/prompts"

"prop-types@npm:15.8.1":
  locations:
    - "node_modules/prop-types"

"proxy-addr@npm:2.0.7":
  locations:
    - "node_modules/proxy-addr"

"ps-list@npm:7.2.0":
  locations:
    - "node_modules/ps-list"

"punycode@npm:1.4.1":
  locations:
    - "node_modules/url/node_modules/punycode"

"punycode@npm:2.3.1":
  locations:
    - "node_modules/punycode"

"qrcode-terminal@npm:0.11.0":
  locations:
    - "node_modules/qrcode-terminal"

"qs@npm:6.13.0":
  locations:
    - "node_modules/qs"

"qs@npm:6.14.0":
  locations:
    - "node_modules/url/node_modules/qs"

"query-string@npm:7.1.3":
  locations:
    - "node_modules/query-string"

"queue-microtask@npm:1.2.3":
  locations:
    - "node_modules/queue-microtask"

"queue@npm:6.0.2":
  locations:
    - "node_modules/queue"

"r-json@npm:1.3.1":
  locations:
    - "node_modules/r-json"

"raf@npm:3.4.1":
  locations:
    - "node_modules/raf"

"range-parser@npm:1.2.1":
  locations:
    - "node_modules/range-parser"

"raw-body@npm:2.4.1":
  locations:
    - "node_modules/micro/node_modules/raw-body"

"raw-body@npm:2.5.2":
  locations:
    - "node_modules/raw-body"

"rc@npm:1.2.8":
  locations:
    - "node_modules/rc"

"react-devtools-core@npm:6.1.2":
  locations:
    - "node_modules/react-devtools-core"

"react-dom@virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:19.0.0":
  locations:
    - "node_modules/react-dom"

"react-fast-compare@npm:3.2.2":
  locations:
    - "node_modules/react-fast-compare"

"react-freeze@virtual:50956a6ac05b5d2f8636d3c7b158c8f30b6913cf8a3cb32febc9685bfd2e0946c0d360bd1e93b3c67ff31b11402b44f16cf3fec89faab0af5daceb1dce3cc855#npm:1.0.4":
  locations:
    - "node_modules/react-freeze"

"react-is@npm:16.13.1":
  locations:
    - "node_modules/react-is"

"react-is@npm:18.3.1":
  locations:
    - "node_modules/pretty-format/node_modules/react-is"

"react-is@npm:19.1.0":
  locations:
    - "node_modules/@react-navigation/core/node_modules/react-is"

"react-native-edge-to-edge@virtual:868ad08120deca101759759eb69060075f6f61c0e8abfa8ee4e8829dfc18c2f18e5cb96b0bc326a67f75ba28d69b7258f92eead00b843b468e08db710f95c708#npm:1.6.0":
  locations:
    - "node_modules/react-native-edge-to-edge"

"react-native-gesture-handler@virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:2.24.0":
  locations:
    - "node_modules/react-native-gesture-handler"

"react-native-is-edge-to-edge@virtual:62d7ccfbe42a92b0c86ed78a084acf28912cb76833266654a0599e8b0f666fa079f7834495bcc7acdd02770b85d65cd77f70aaa34d4ebc8234c988a9bb2fc053#npm:1.1.6":
  locations:
    - "node_modules/react-native-is-edge-to-edge"
  aliases:
    - "virtual:868ad08120deca101759759eb69060075f6f61c0e8abfa8ee4e8829dfc18c2f18e5cb96b0bc326a67f75ba28d69b7258f92eead00b843b468e08db710f95c708#npm:1.1.6"

"react-native-safe-area-context@virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:5.4.0":
  locations:
    - "node_modules/react-native-safe-area-context"
  aliases:
    - "virtual:f87a972e7ee54256c6d8f979d7f3914b32522893226eba595e4efe4ecc641a239c6d88e01eccc6f32db30829d6ac493bfc98cb406a9b0d6059ee4112c0843da9#npm:5.4.0"

"react-native-screens@virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:4.10.0":
  locations:
    - "node_modules/react-native-screens"

"react-native-svg@virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:15.11.2":
  locations:
    - "node_modules/react-native-svg"

"react-native-web@virtual:4b5540d0755d71b727722f75d0998f7d1a1c397790df8d4b89af91a08e73b120351ed29e6760f8b370b0d967b4e5f3cf413adca2bebeef4a08c5e37c08d75738#npm:0.19.13":
  locations:
    - "node_modules/react-native-web"
  aliases:
    - "virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:0.19.13"

"react-native@virtual:7220851f1ab1664d8708526c8844e73623305f1d4ea4e0e39e1c152b1f6ec946be0f0328eea81acc1ce7331e6c724a3a750891a61836faa17f45271f14fc1192#npm:0.79.2":
  locations:
    - "node_modules/react-native"
  aliases:
    - "virtual:bd0c3e9520eadcf369dde10e7d6c40b2d2b5ac95adb12dc0617b69b7ec1357c347c76776a4a01f276785e9a221952580a452e396182ba12c132beab8771e6eab#npm:0.79.2"

"react-refresh@npm:0.14.2":
  locations:
    - "node_modules/react-refresh"

"react-remove-scroll-bar@virtual:9d87ce7e63d3c317afddb57a85e01dbf9e4da9b7c22cb85d9cb0dce74245e45c7a1819e661e07f90330cc58f0964f316eca0d79ed735580599e4588a2230673d#npm:2.3.8":
  locations:
    - "node_modules/react-remove-scroll-bar"

"react-remove-scroll@virtual:19dd8d67b181c1dc1093f6a39255796ef3ea868c4053350d4dd4ced66f77fc77a246f16d4fa9c3182630e800ccf67ac4689b449dfe42d373613556be266839d0#npm:2.6.3":
  locations:
    - "node_modules/react-remove-scroll"

"react-style-singleton@virtual:9d87ce7e63d3c317afddb57a85e01dbf9e4da9b7c22cb85d9cb0dce74245e45c7a1819e661e07f90330cc58f0964f316eca0d79ed735580599e4588a2230673d#npm:2.2.3":
  locations:
    - "node_modules/react-style-singleton"

"react@npm:19.0.0":
  locations:
    - "node_modules/react"

"readdirp@npm:3.6.0":
  locations:
    - "node_modules/readdirp"

"readdirp@npm:4.1.2":
  locations:
    - "node_modules/vercel/node_modules/readdirp"

"redeyed@npm:2.1.1":
  locations:
    - "node_modules/redeyed"

"reflect.getprototypeof@npm:1.0.10":
  locations:
    - "node_modules/reflect.getprototypeof"

"regenerate-unicode-properties@npm:10.2.0":
  locations:
    - "node_modules/regenerate-unicode-properties"

"regenerate@npm:1.4.2":
  locations:
    - "node_modules/regenerate"

"regenerator-runtime@npm:0.13.11":
  locations:
    - "node_modules/react-native/node_modules/regenerator-runtime"

"regenerator-runtime@npm:0.14.1":
  locations:
    - "node_modules/regenerator-runtime"

"regenerator-transform@npm:0.15.2":
  locations:
    - "node_modules/regenerator-transform"

"regexp.prototype.flags@npm:1.5.4":
  locations:
    - "node_modules/regexp.prototype.flags"

"regexpu-core@npm:6.2.0":
  locations:
    - "node_modules/regexpu-core"

"regjsgen@npm:0.8.0":
  locations:
    - "node_modules/regjsgen"

"regjsparser@npm:0.12.0":
  locations:
    - "node_modules/regjsparser"

"relateurl@npm:0.2.7":
  locations:
    - "node_modules/relateurl"

"renderkid@npm:3.0.0":
  locations:
    - "node_modules/renderkid"

"require-directory@npm:2.1.1":
  locations:
    - "node_modules/require-directory"

"require-from-string@npm:2.0.2":
  locations:
    - "node_modules/require-from-string"

"requireg@npm:0.2.2":
  locations:
    - "node_modules/requireg"

"reselect@npm:4.1.8":
  locations:
    - "node_modules/reselect"

"resolve-from@npm:3.0.0":
  locations:
    - "node_modules/cosmiconfig/node_modules/resolve-from"

"resolve-from@npm:4.0.0":
  locations:
    - "node_modules/import-fresh/node_modules/resolve-from"

"resolve-from@npm:5.0.0":
  locations:
    - "node_modules/resolve-from"

"resolve-pkg-maps@npm:1.0.0":
  locations:
    - "node_modules/resolve-pkg-maps"

"resolve-workspace-root@npm:2.0.0":
  locations:
    - "node_modules/resolve-workspace-root"

"resolve.exports@npm:2.0.3":
  locations:
    - "node_modules/resolve.exports"

"resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d":
  locations:
    - "node_modules/resolve"

"resolve@patch:resolve@npm%3A1.7.1#optional!builtin<compat/resolve>::version=1.7.1&hash=3bafbf":
  locations:
    - "node_modules/requireg/node_modules/resolve"

"resolve@patch:resolve@npm%3A2.0.0-next.5#optional!builtin<compat/resolve>::version=2.0.0-next.5&hash=c3c19d":
  locations:
    - "node_modules/eslint-plugin-react/node_modules/resolve"

"restore-cursor@npm:2.0.0":
  locations:
    - "node_modules/restore-cursor"

"retry@npm:0.12.0":
  locations:
    - "node_modules/retry"

"reusify@npm:1.1.0":
  locations:
    - "node_modules/reusify"

"rimraf@npm:3.0.2":
  locations:
    - "node_modules/chromium-edge-launcher/node_modules/rimraf"

"rimraf@npm:5.0.10":
  locations:
    - "node_modules/rimraf"

"rollup@npm:4.34.8":
  locations:
    - "node_modules/rollup"

"run-parallel@npm:1.2.0":
  locations:
    - "node_modules/run-parallel"

"safe-array-concat@npm:1.1.3":
  locations:
    - "node_modules/safe-array-concat"

"safe-buffer@npm:5.2.1":
  locations:
    - "node_modules/safe-buffer"

"safe-push-apply@npm:1.0.0":
  locations:
    - "node_modules/safe-push-apply"

"safe-regex-test@npm:1.1.0":
  locations:
    - "node_modules/safe-regex-test"

"safer-buffer@npm:2.1.2":
  locations:
    - "node_modules/safer-buffer"

"sax@npm:1.4.1":
  locations:
    - "node_modules/sax"

"scheduler@npm:0.25.0":
  locations:
    - "node_modules/scheduler"

"schema-utils@npm:3.3.0":
  locations:
    - "node_modules/url-loader/node_modules/schema-utils"
    - "node_modules/file-loader/node_modules/schema-utils"

"schema-utils@npm:4.3.0":
  locations:
    - "node_modules/schema-utils"

"semver@npm:6.3.1":
  locations:
    - "node_modules/make-dir/node_modules/semver"
    - "node_modules/istanbul-lib-instrument/node_modules/semver"
    - "node_modules/eslint-plugin-react/node_modules/semver"
    - "node_modules/eslint-plugin-import/node_modules/semver"
    - "node_modules/babel-plugin-polyfill-corejs2/node_modules/semver"
    - "node_modules/@vercel/redwood/node_modules/semver"
    - "node_modules/@babel/plugin-transform-runtime/node_modules/semver"
    - "node_modules/@babel/helper-create-regexp-features-plugin/node_modules/semver"
    - "node_modules/@babel/helper-create-class-features-plugin/node_modules/semver"
    - "node_modules/@babel/helper-compilation-targets/node_modules/semver"
    - "node_modules/@babel/core/node_modules/semver"

"semver@npm:7.5.4":
  locations:
    - "node_modules/@vercel/fun/node_modules/semver"

"semver@npm:7.6.3":
  locations:
    - "node_modules/expo-router/node_modules/semver"

"semver@npm:7.7.1":
  locations:
    - "node_modules/semver"

"send@npm:0.19.0":
  locations:
    - "node_modules/send"

"send@npm:0.19.1":
  locations:
    - "node_modules/@expo/cli/node_modules/send"

"sentence-case@npm:3.0.4":
  locations:
    - "node_modules/sentence-case"

"serialize-error@npm:2.1.0":
  locations:
    - "node_modules/serialize-error"

"serve-static@npm:1.16.2":
  locations:
    - "node_modules/serve-static"

"server-only@npm:0.0.1":
  locations:
    - "node_modules/server-only"

"set-function-length@npm:1.2.2":
  locations:
    - "node_modules/set-function-length"

"set-function-name@npm:2.0.2":
  locations:
    - "node_modules/set-function-name"

"set-proto@npm:1.0.0":
  locations:
    - "node_modules/set-proto"

"set-value@npm:4.1.0":
  locations:
    - "node_modules/set-value"

"setimmediate@npm:1.0.5":
  locations:
    - "node_modules/setimmediate"

"setprototypeof@npm:1.1.1":
  locations:
    - "node_modules/micro/node_modules/setprototypeof"

"setprototypeof@npm:1.2.0":
  locations:
    - "node_modules/setprototypeof"

"sf-symbols-typescript@npm:1.0.0":
  locations:
    - "node_modules/sf-symbols-typescript"

"shallowequal@npm:1.1.0":
  locations:
    - "node_modules/shallowequal"

"shebang-command@npm:2.0.0":
  locations:
    - "node_modules/shebang-command"

"shebang-regex@npm:3.0.0":
  locations:
    - "node_modules/shebang-regex"

"shell-exec@npm:1.0.2":
  locations:
    - "node_modules/shell-exec"

"shell-quote@npm:1.8.2":
  locations:
    - "node_modules/shell-quote"

"shellwords-ts@npm:3.0.1":
  locations:
    - "node_modules/shellwords-ts"

"side-channel-list@npm:1.0.0":
  locations:
    - "node_modules/side-channel-list"

"side-channel-map@npm:1.0.1":
  locations:
    - "node_modules/side-channel-map"

"side-channel-weakmap@npm:1.0.2":
  locations:
    - "node_modules/side-channel-weakmap"

"side-channel@npm:1.1.0":
  locations:
    - "node_modules/side-channel"

"siginfo@npm:2.0.0":
  locations:
    - "node_modules/siginfo"

"signal-exit@npm:3.0.7":
  locations:
    - "node_modules/signal-exit"

"signal-exit@npm:4.0.2":
  locations:
    - "node_modules/edge-runtime/node_modules/signal-exit"

"signal-exit@npm:4.1.0":
  locations:
    - "node_modules/foreground-child/node_modules/signal-exit"

"simple-plist@npm:1.4.0":
  locations:
    - "node_modules/simple-plist"

"simple-swizzle@npm:0.2.2":
  locations:
    - "node_modules/simple-swizzle"

"sisteransi@npm:1.0.5":
  locations:
    - "node_modules/sisteransi"

"slash@npm:3.0.0":
  locations:
    - "node_modules/slash"

"slash@npm:4.0.0":
  locations:
    - "node_modules/globby/node_modules/slash"

"slice-ansi@npm:4.0.0":
  locations:
    - "node_modules/slice-ansi"

"slugify@npm:1.6.6":
  locations:
    - "node_modules/slugify"

"smart-buffer@npm:4.2.0":
  locations:
    - "node_modules/smart-buffer"

"snake-case@npm:3.0.4":
  locations:
    - "node_modules/snake-case"

"socks-proxy-agent@npm:8.0.5":
  locations:
    - "node_modules/socks-proxy-agent"

"socks@npm:2.8.4":
  locations:
    - "node_modules/socks"

"solito@npm:4.3.0":
  locations:
    - "node_modules/solito"

"sonner@virtual:eb36e83e6f21c6a151bc2851f3786cddf766b6b2ffac6d039a47275a1b8b18863389944c896b6fe28baa1a101384baab23b577b9a3070f971b79865498bbd1b7#npm:0.3.5":
  locations:
    - "node_modules/sonner"

"source-list-map@npm:2.0.1":
  locations:
    - "node_modules/source-list-map"

"source-map-js@npm:1.2.1":
  locations:
    - "node_modules/source-map-js"

"source-map-support@npm:0.5.21":
  locations:
    - "node_modules/source-map-support"

"source-map@npm:0.5.7":
  locations:
    - "node_modules/metro/node_modules/source-map"
    - "node_modules/metro-symbolicate/node_modules/source-map"
    - "node_modules/metro-source-map/node_modules/source-map"

"source-map@npm:0.6.1":
  locations:
    - "node_modules/source-map"

"split-on-first@npm:1.1.0":
  locations:
    - "node_modules/split-on-first"

"sprintf-js@npm:1.0.3":
  locations:
    - "node_modules/argparse/node_modules/sprintf-js"

"sprintf-js@npm:1.1.3":
  locations:
    - "node_modules/sprintf-js"

"ssri@npm:12.0.0":
  locations:
    - "node_modules/ssri"

"stable-hash@npm:0.0.4":
  locations:
    - "node_modules/stable-hash"

"stack-utils@npm:2.0.6":
  locations:
    - "node_modules/stack-utils"

"stackback@npm:0.0.2":
  locations:
    - "node_modules/stackback"

"stackframe@npm:1.3.4":
  locations:
    - "node_modules/stackframe"

"stacktrace-parser@npm:0.1.11":
  locations:
    - "node_modules/stacktrace-parser"

"stat-mode@npm:0.3.0":
  locations:
    - "node_modules/stat-mode"

"statuses@npm:1.5.0":
  locations:
    - "node_modules/path-match/node_modules/statuses"
    - "node_modules/micro/node_modules/statuses"
    - "node_modules/connect/node_modules/statuses"

"statuses@npm:2.0.1":
  locations:
    - "node_modules/statuses"

"std-env@npm:3.8.0":
  locations:
    - "node_modules/std-env"

"stream-buffers@npm:2.2.0":
  locations:
    - "node_modules/stream-buffers"

"stream-to-array@npm:2.3.0":
  locations:
    - "node_modules/stream-to-array"

"stream-to-promise@npm:2.2.0":
  locations:
    - "node_modules/stream-to-promise"

"streamsearch@npm:1.1.0":
  locations:
    - "node_modules/streamsearch"

"strict-uri-encode@npm:2.0.0":
  locations:
    - "node_modules/strict-uri-encode"

"string-width@npm:4.2.3":
  locations:
    - "node_modules/string-width-cjs"
    - "node_modules/string-width"

"string-width@npm:5.1.2":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/string-width"

"string.prototype.includes@npm:2.0.1":
  locations:
    - "node_modules/string.prototype.includes"

"string.prototype.matchall@npm:4.0.12":
  locations:
    - "node_modules/string.prototype.matchall"

"string.prototype.repeat@npm:1.0.0":
  locations:
    - "node_modules/string.prototype.repeat"

"string.prototype.trim@npm:1.2.10":
  locations:
    - "node_modules/string.prototype.trim"

"string.prototype.trimend@npm:1.0.9":
  locations:
    - "node_modules/string.prototype.trimend"

"string.prototype.trimstart@npm:1.0.8":
  locations:
    - "node_modules/string.prototype.trimstart"

"strip-ansi@npm:5.2.0":
  locations:
    - "node_modules/ora/node_modules/strip-ansi"

"strip-ansi@npm:6.0.1":
  locations:
    - "node_modules/strip-ansi-cjs"
    - "node_modules/strip-ansi"

"strip-ansi@npm:7.1.0":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/strip-ansi"

"strip-bom@npm:3.0.0":
  locations:
    - "node_modules/strip-bom"

"strip-final-newline@npm:2.0.0":
  locations:
    - "node_modules/strip-final-newline"

"strip-json-comments@npm:2.0.1":
  locations:
    - "node_modules/rc/node_modules/strip-json-comments"

"strip-json-comments@npm:3.1.1":
  locations:
    - "node_modules/strip-json-comments"

"structured-headers@npm:0.4.1":
  locations:
    - "node_modules/structured-headers"

"style-value-types@npm:5.0.0":
  locations:
    - "node_modules/style-value-types"

"styled-jsx@virtual:5a3ce1508b47d138f00be59a26fb74a773b31e99b1b3c77a8944b81f8495c5454f5f42107b0a02802e8f8b7a71a25d9467cc2f3873de7903c48e96276cec40f7#npm:5.1.1":
  locations:
    - "node_modules/styled-jsx"

"styleq@npm:0.1.3":
  locations:
    - "node_modules/styleq"

"sucrase@npm:3.35.0":
  locations:
    - "node_modules/sucrase"

"sudo-prompt@npm:8.2.5":
  locations:
    - "node_modules/sudo-prompt"

"supports-color@npm:5.5.0":
  locations:
    - "node_modules/ora/node_modules/supports-color"
    - "node_modules/log-symbols/node_modules/supports-color"
    - "node_modules/@babel/highlight/node_modules/supports-color"

"supports-color@npm:7.2.0":
  locations:
    - "node_modules/supports-color"

"supports-color@npm:8.1.1":
  locations:
    - "node_modules/jest-worker/node_modules/supports-color"

"supports-hyperlinks@npm:2.3.0":
  locations:
    - "node_modules/supports-hyperlinks"

"supports-preserve-symlinks-flag@npm:1.0.0":
  locations:
    - "node_modules/supports-preserve-symlinks-flag"

"tabbable@npm:6.2.0":
  locations:
    - "node_modules/tabbable"

"table@npm:6.9.0":
  locations:
    - "node_modules/table"

"tamagui-loader@npm:1.129.19":
  locations:
    - "node_modules/tamagui-loader"

"tamagui@virtual:1e21ed5e542f04ac0725f80f20538bd288a2542912556a71424e0ef86756859fcedbc2507675d93c28d09cc2e230d8793e424a169e257d21ba5cb20fce2d2238#npm:1.129.19":
  locations:
    - "node_modules/tamagui"
  aliases:
    - "virtual:e34c95b43a8ca7eaa9ebdcf181cba5465fec9c487fa9fd68d2b50037ff2e2cda0e67c5ca42fbc7f05f80a6acd09921a4d9c1adba2c2e683eb34459dac32822c4#npm:1.129.19"

"tapable@npm:2.2.1":
  locations:
    - "node_modules/tapable"

"tar@npm:6.2.1":
  locations:
    - "node_modules/@vercel/fun/node_modules/tar"

"tar@npm:7.4.3":
  locations:
    - "node_modules/tar"

"temp-dir@npm:2.0.0":
  locations:
    - "node_modules/temp-dir"

"terminal-link@npm:2.1.1":
  locations:
    - "node_modules/terminal-link"

"terser@npm:5.39.0":
  locations:
    - "node_modules/terser"

"test-exclude@npm:6.0.0":
  locations:
    - "node_modules/test-exclude"

"thenify-all@npm:1.6.0":
  locations:
    - "node_modules/thenify-all"

"thenify@npm:3.3.1":
  locations:
    - "node_modules/thenify"

"throat@npm:5.0.0":
  locations:
    - "node_modules/throat"

"time-span@npm:4.0.0":
  locations:
    - "node_modules/time-span"

"tinybench@npm:2.9.0":
  locations:
    - "node_modules/tinybench"

"tinyexec@npm:0.3.2":
  locations:
    - "node_modules/tinyexec"

"tinyglobby@npm:0.2.12":
  locations:
    - "node_modules/tinyglobby"

"tinypool@npm:1.0.2":
  locations:
    - "node_modules/tinypool"

"tinyrainbow@npm:1.2.0":
  locations:
    - "node_modules/tinyrainbow"

"tinyspy@npm:3.0.2":
  locations:
    - "node_modules/tinyspy"

"tmp@npm:0.0.33":
  locations:
    - "node_modules/tmp"

"tmpl@npm:1.0.5":
  locations:
    - "node_modules/tmpl"

"to-regex-range@npm:5.0.1":
  locations:
    - "node_modules/to-regex-range"

"toidentifier@npm:1.0.0":
  locations:
    - "node_modules/micro/node_modules/toidentifier"

"toidentifier@npm:1.0.1":
  locations:
    - "node_modules/toidentifier"

"tr46@npm:0.0.3":
  locations:
    - "node_modules/tr46"

"tree-kill@npm:1.2.2":
  locations:
    - "node_modules/tree-kill"

"ts-api-utils@virtual:bc79daf1d3272c492f68be83afd8f7dbca94a15f8f28596d929a5eb50faf4ec7c766f11823e41d7bf7b9ea9f511e0525990e4564ffe502487be9bce4a7414e58#npm:2.0.1":
  locations:
    - "node_modules/ts-api-utils"

"ts-interface-checker@npm:0.1.13":
  locations:
    - "node_modules/ts-interface-checker"

"ts-morph@npm:12.0.0":
  locations:
    - "node_modules/ts-morph"

"ts-morph@npm:15.1.0":
  locations:
    - "node_modules/@tamagui/cli/node_modules/ts-morph"

"ts-node@virtual:b4304646f8ce99c4dcc649f59d588ddca9df5ca3308166a45d622f1ef18bd9b3ae397d4e25873de136dca2f32111483c07998816f2c4559d015efdda3fcb27a0#npm:10.9.1":
  locations:
    - "node_modules/@vercel/node/node_modules/ts-node"

"ts-toolbelt@npm:6.15.5":
  locations:
    - "node_modules/ts-toolbelt"

"tsconfig-paths@npm:3.15.0":
  locations:
    - "node_modules/tsconfig-paths"

"tslib@npm:2.1.0":
  locations:
    - "node_modules/ultra-runner/node_modules/tslib"

"tslib@npm:2.8.1":
  locations:
    - "node_modules/tslib"

"turbo-darwin-arm64@npm:1.13.4":
  locations:
    - "node_modules/turbo-darwin-arm64"

"turbo@npm:1.13.4":
  locations:
    - "node_modules/turbo"

"type-check@npm:0.4.0":
  locations:
    - "node_modules/type-check"

"type-detect@npm:4.0.8":
  locations:
    - "node_modules/type-detect"

"type-fest@npm:0.21.3":
  locations:
    - "node_modules/type-fest"

"type-fest@npm:0.7.1":
  locations:
    - "node_modules/stacktrace-parser/node_modules/type-fest"

"type-fest@npm:4.35.0":
  locations:
    - "node_modules/check-dependency-version-consistency/node_modules/type-fest"

"type-is@npm:1.6.18":
  locations:
    - "node_modules/type-is"

"typed-array-buffer@npm:1.0.3":
  locations:
    - "node_modules/typed-array-buffer"

"typed-array-byte-length@npm:1.0.3":
  locations:
    - "node_modules/typed-array-byte-length"

"typed-array-byte-offset@npm:1.0.4":
  locations:
    - "node_modules/typed-array-byte-offset"

"typed-array-length@npm:1.0.7":
  locations:
    - "node_modules/typed-array-length"

"typescript@patch:typescript@npm%3A4.9.5#optional!builtin<compat/typescript>::version=4.9.5&hash=289587":
  locations:
    - "node_modules/@vercel/node/node_modules/typescript"

"typescript@patch:typescript@npm%3A5.8.3#optional!builtin<compat/typescript>::version=5.8.3&hash=8c6c40":
  locations:
    - "node_modules/typescript"

"ua-parser-js@npm:1.0.40":
  locations:
    - "node_modules/ua-parser-js"

"uid-promise@npm:1.0.0":
  locations:
    - "node_modules/uid-promise"

"ultra-runner@npm:3.10.5":
  locations:
    - "node_modules/ultra-runner"

"unbox-primitive@npm:1.1.0":
  locations:
    - "node_modules/unbox-primitive"

"undici-types@npm:6.19.8":
  locations:
    - "node_modules/@types/node/node_modules/undici-types"

"undici-types@npm:6.20.0":
  locations:
    - "node_modules/undici-types"

"undici@npm:5.28.4":
  locations:
    - "node_modules/@vercel/node/node_modules/undici"

"undici@npm:6.21.1":
  locations:
    - "node_modules/@expo/cli/node_modules/undici"

"undici@npm:7.8.0":
  locations:
    - "node_modules/undici"

"unicode-canonical-property-names-ecmascript@npm:2.0.1":
  locations:
    - "node_modules/unicode-canonical-property-names-ecmascript"

"unicode-match-property-ecmascript@npm:2.0.0":
  locations:
    - "node_modules/unicode-match-property-ecmascript"

"unicode-match-property-value-ecmascript@npm:2.2.0":
  locations:
    - "node_modules/unicode-match-property-value-ecmascript"

"unicode-property-aliases-ecmascript@npm:2.1.0":
  locations:
    - "node_modules/unicode-property-aliases-ecmascript"

"unique-filename@npm:4.0.0":
  locations:
    - "node_modules/unique-filename"

"unique-slug@npm:5.0.0":
  locations:
    - "node_modules/unique-slug"

"unique-string@npm:2.0.0":
  locations:
    - "node_modules/unique-string"

"universalify@npm:2.0.1":
  locations:
    - "node_modules/universalify"

"unpipe@npm:1.0.0":
  locations:
    - "node_modules/unpipe"

"update-browserslist-db@virtual:2fdeb5face9914bb5fd94c70f084d153c80d2f09e5aabee010e4220b248dc23fca8f73c7beed0195e45ae6e2b1cb25388f709d7bfc4f00e473d573887faf4e5c#npm:1.1.3":
  locations:
    - "node_modules/update-browserslist-db"

"upper-case-first@npm:2.0.2":
  locations:
    - "node_modules/upper-case-first"

"upper-case@npm:2.0.2":
  locations:
    - "node_modules/upper-case"

"uri-js@npm:4.4.1":
  locations:
    - "node_modules/uri-js"

"url-loader@virtual:2ed9a9ca06dbfcd7161ed29975c6ac3650af7a2e17f09203c7a1718e71ec47203605a304723cc72d6c65795be10ba94c65f3fcc0298a05a4bf42540d3fead0e8#npm:4.1.1":
  locations:
    - "node_modules/url-loader"

"url@npm:0.11.4":
  locations:
    - "node_modules/url"

"use-callback-ref@virtual:9d87ce7e63d3c317afddb57a85e01dbf9e4da9b7c22cb85d9cb0dce74245e45c7a1819e661e07f90330cc58f0964f316eca0d79ed735580599e4588a2230673d#npm:1.3.3":
  locations:
    - "node_modules/use-callback-ref"

"use-latest-callback@virtual:2f053aea9f3043d48dd272bd800d4824c4c0702c6ffc0b269e7dfde1dad9051de87e4d03b0ce445e15543e9417affb2349337e6ed38a92bdd2073a441354be9d#npm:0.2.3":
  locations:
    - "node_modules/use-latest-callback"
  aliases:
    - "virtual:66fe7c09a5b721f1dd87e49b838d226b04a666b22191a377206f1c74503da20fde0da1a2a7942319ff620563c2e9c33deaa5c2451c966009e008791b84b9a706#npm:0.2.3"

"use-sidecar@virtual:9d87ce7e63d3c317afddb57a85e01dbf9e4da9b7c22cb85d9cb0dce74245e45c7a1819e661e07f90330cc58f0964f316eca0d79ed735580599e4588a2230673d#npm:1.1.3":
  locations:
    - "node_modules/use-sidecar"

"use-sync-external-store@virtual:70bb8896aea101e3a10c25a4e0935a00ee82c9eb07174fca265ff4474421e25978a173bea60cb5d32bd2c03588c70adebc3d7a0049e0ef0d479bb314acd2ade0#npm:1.5.0":
  locations:
    - "node_modules/use-sync-external-store"

"util-deprecate@npm:1.0.2":
  locations:
    - "node_modules/util-deprecate"

"utila@npm:0.4.0":
  locations:
    - "node_modules/utila"

"utils-merge@npm:1.0.1":
  locations:
    - "node_modules/utils-merge"

"uuid@npm:3.3.2":
  locations:
    - "node_modules/@vercel/fun/node_modules/uuid"

"uuid@npm:7.0.3":
  locations:
    - "node_modules/uuid"

"v8-compile-cache-lib@npm:3.0.1":
  locations:
    - "node_modules/v8-compile-cache-lib"

"validate-npm-package-name@npm:5.0.1":
  locations:
    - "node_modules/validate-npm-package-name"

"vary@npm:1.1.2":
  locations:
    - "node_modules/vary"

"vercel@npm:41.2.2":
  locations:
    - "node_modules/vercel"

"vite-node@npm:2.1.9":
  locations:
    - "node_modules/vite-node"

"vite@virtual:84dcff71db8be9cbe950d0756a4f7772695095a485baf88f1cc98436fdd0ea49e9c6ac7f535ec4b7b26fd24d60bd4323cc6ed6d8629d5b2015f92d4613c7ffb6#npm:5.4.14":
  locations:
    - "node_modules/vite"

"vitest@virtual:1e21ed5e542f04ac0725f80f20538bd288a2542912556a71424e0ef86756859fcedbc2507675d93c28d09cc2e230d8793e424a169e257d21ba5cb20fce2d2238#npm:2.1.9":
  locations:
    - "node_modules/vitest"
  aliases:
    - "virtual:20ecbb796054cc6e4f48e68b097cec703e7cd794dafc8f491ac8e2b18dfe2cb22abeb342900ed8f7588d49ae0d8ce64030bb9d4bd7fe9e1b720b6bb1ce8962c2#npm:2.1.9"

"vlq@npm:1.0.1":
  locations:
    - "node_modules/vlq"

"w-json@npm:1.3.10":
  locations:
    - "node_modules/w-json"

"w-json@npm:1.3.11":
  locations:
    - "node_modules/edit-json-file/node_modules/w-json"

"walker@npm:1.0.8":
  locations:
    - "node_modules/walker"

"warn-once@npm:0.1.1":
  locations:
    - "node_modules/warn-once"

"wcwidth@npm:1.0.1":
  locations:
    - "node_modules/wcwidth"

"web-vitals@npm:0.2.4":
  locations:
    - "node_modules/web-vitals"

"webidl-conversions@npm:3.0.1":
  locations:
    - "node_modules/webidl-conversions"

"webidl-conversions@npm:5.0.0":
  locations:
    - "node_modules/whatwg-url-without-unicode/node_modules/webidl-conversions"

"webpack-sources@npm:1.4.3":
  locations:
    - "node_modules/webpack-sources"

"whatwg-fetch@npm:3.6.20":
  locations:
    - "node_modules/whatwg-fetch"

"whatwg-url-without-unicode@npm:8.0.0-3":
  locations:
    - "node_modules/whatwg-url-without-unicode"

"whatwg-url@npm:5.0.0":
  locations:
    - "node_modules/whatwg-url"

"which-boxed-primitive@npm:1.1.1":
  locations:
    - "node_modules/which-boxed-primitive"

"which-builtin-type@npm:1.2.1":
  locations:
    - "node_modules/which-builtin-type"

"which-collection@npm:1.0.2":
  locations:
    - "node_modules/which-collection"

"which-typed-array@npm:1.1.18":
  locations:
    - "node_modules/which-typed-array"

"which@npm:2.0.2":
  locations:
    - "node_modules/which"

"which@npm:5.0.0":
  locations:
    - "node_modules/node-gyp/node_modules/which"

"why-is-node-running@npm:2.3.0":
  locations:
    - "node_modules/why-is-node-running"

"wonka@npm:6.3.4":
  locations:
    - "node_modules/wonka"

"word-wrap@npm:1.2.5":
  locations:
    - "node_modules/word-wrap"

"wrap-ansi@npm:7.0.0":
  locations:
    - "node_modules/wrap-ansi-cjs"
    - "node_modules/wrap-ansi"

"wrap-ansi@npm:8.1.0":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/wrap-ansi"

"wrappy@npm:1.0.2":
  locations:
    - "node_modules/wrappy"

"write-file-atomic@npm:4.0.2":
  locations:
    - "node_modules/write-file-atomic"

"ws@virtual:3eaed14f8cfef4cf5ce0a4ed9c21f123038d7e8e73723d113f9ac819ce8950d81ce2de8ca91196885a3b0fce447449c659854ed30136df8412260fca6e4ce8e4#npm:7.5.10":
  locations:
    - "node_modules/react-devtools-core/node_modules/ws"
    - "node_modules/metro/node_modules/ws"

"ws@virtual:64b0d24e78fb24a4d23b5fdd0f59007fc38be9e523e31719c695c39d5279857d4201572683e08da89dea76f2678d593d9b2025b691a1da465e3fb941150dddce#npm:6.2.3":
  locations:
    - "node_modules/ws"

"ws@virtual:fb70ee0167e50d6506d79efa4d1b799350b9c189d900f3023580451de2767a7b28b4bcf28e6f8b5a51ae7e7f2c251f876e30b49aef9f644aa67b6b121c09e0b4#npm:8.18.1":
  locations:
    - "node_modules/@expo/cli/node_modules/ws"

"xcode@npm:3.0.1":
  locations:
    - "node_modules/xcode"

"xdg-app-paths@npm:5.1.0":
  locations:
    - "node_modules/xdg-app-paths"

"xdg-portable@npm:7.3.0":
  locations:
    - "node_modules/xdg-portable"

"xml2js@npm:0.6.0":
  locations:
    - "node_modules/xml2js"

"xmlbuilder@npm:11.0.1":
  locations:
    - "node_modules/xml2js/node_modules/xmlbuilder"

"xmlbuilder@npm:15.1.1":
  locations:
    - "node_modules/xmlbuilder"

"y18n@npm:5.0.8":
  locations:
    - "node_modules/y18n"

"yallist@npm:3.1.1":
  locations:
    - "node_modules/@babel/helper-compilation-targets/node_modules/yallist"

"yallist@npm:4.0.0":
  locations:
    - "node_modules/yallist"

"yallist@npm:5.0.0":
  locations:
    - "node_modules/tar/node_modules/yallist"

"yamljs@npm:0.3.0":
  locations:
    - "node_modules/yamljs"

"yargs-parser@npm:20.2.9":
  locations:
    - "node_modules/yargs-parser"

"yargs-parser@npm:21.1.1":
  locations:
    - "node_modules/yargs/node_modules/yargs-parser"

"yargs@npm:16.2.0":
  locations:
    - "node_modules/ultra-runner/node_modules/yargs"

"yargs@npm:17.7.2":
  locations:
    - "node_modules/yargs"

"yauzl-clone@npm:1.0.4":
  locations:
    - "node_modules/yauzl-clone"

"yauzl-promise@npm:2.1.3":
  locations:
    - "node_modules/yauzl-promise"

"yauzl@npm:2.10.0":
  locations:
    - "node_modules/yauzl"

"yn@npm:3.1.1":
  locations:
    - "node_modules/yn"

"yocto-queue@npm:0.1.0":
  locations:
    - "node_modules/yocto-queue"

"yocto-queue@npm:1.1.1":
  locations:
    - "node_modules/babel-loader/node_modules/yocto-queue"
