{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,YAAY", "names": ["propsIn: IconProps", "TextSearch: IconComponent"], "sources": ["src/icons/TextSearch.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const TextSearch: IconComponent = themed(\n  memo(function TextSearch(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M21 6H3\" stroke={color} />\n        <Path d=\"M10 12H3\" stroke={color} />\n        <Path d=\"M10 18H3\" stroke={color} />\n        <_Circle cx=\"17\" cy=\"15\" r=\"3\" stroke={color} />\n        <Path d=\"m21 19-1.9-1.9\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}