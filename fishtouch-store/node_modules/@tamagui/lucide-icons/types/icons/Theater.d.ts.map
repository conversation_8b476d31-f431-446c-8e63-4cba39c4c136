{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,SAAS", "names": ["propsIn: IconProps", "Theater: IconComponent"], "sources": ["src/icons/Theater.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Theater: IconComponent = themed(\n  memo(function Theater(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M2 10s3-3 3-8\" stroke={color} />\n        <Path d=\"M22 10s-3-3-3-8\" stroke={color} />\n        <Path d=\"M10 2c0 4.4-3.6 8-8 8\" stroke={color} />\n        <Path d=\"M14 2c0 4.4 3.6 8 8 8\" stroke={color} />\n        <Path d=\"M2 10s2 2 2 5\" stroke={color} />\n        <Path d=\"M22 10s-2 2-2 5\" stroke={color} />\n        <Path d=\"M8 15h8\" stroke={color} />\n        <Path d=\"M2 22v-1a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v1\" stroke={color} />\n        <Path d=\"M14 22v-1a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v1\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}