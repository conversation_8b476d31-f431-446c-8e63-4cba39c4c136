{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,YAAY", "names": ["propsIn: IconProps", "University: IconComponent"], "sources": ["src/icons/University.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const University: IconComponent = themed(\n  memo(function University(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <_Circle cx=\"12\" cy=\"10\" r=\"1\" stroke={color} />\n        <Path\n          d=\"M22 20V8h-4l-6-4-6 4H2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2\"\n          stroke={color}\n        />\n        <Path d=\"M6 17v.01\" stroke={color} />\n        <Path d=\"M6 13v.01\" stroke={color} />\n        <Path d=\"M18 17v.01\" stroke={color} />\n        <Path d=\"M18 13v.01\" stroke={color} />\n        <Path d=\"M14 22v-5a2 2 0 0 0-2-2a2 2 0 0 0-2 2v5\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}