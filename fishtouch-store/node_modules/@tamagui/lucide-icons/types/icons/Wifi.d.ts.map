{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,MAAM", "names": ["propsIn: IconProps", "Wifi: IconComponent"], "sources": ["src/icons/Wifi.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Wifi: IconComponent = themed(\n  memo(function Wifi(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M12 20h.01\" stroke={color} />\n        <Path d=\"M2 8.82a15 15 0 0 1 20 0\" stroke={color} />\n        <Path d=\"M5 12.859a10 10 0 0 1 14 0\" stroke={color} />\n        <Path d=\"M8.5 16.429a5 5 0 0 1 7 0\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}