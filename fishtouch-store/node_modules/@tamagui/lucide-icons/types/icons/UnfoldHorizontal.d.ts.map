{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,kBAAkB", "names": ["propsIn: IconProps", "UnfoldHorizontal: IconComponent"], "sources": ["src/icons/UnfoldHorizontal.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const UnfoldHorizontal: IconComponent = themed(\n  memo(function UnfoldHorizontal(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M16 12h6\" stroke={color} />\n        <Path d=\"M8 12H2\" stroke={color} />\n        <Path d=\"M12 2v2\" stroke={color} />\n        <Path d=\"M12 8v2\" stroke={color} />\n        <Path d=\"M12 14v2\" stroke={color} />\n        <Path d=\"M12 20v2\" stroke={color} />\n        <Path d=\"m19 15 3-3-3-3\" stroke={color} />\n        <Path d=\"m5 9-3 3 3 3\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}