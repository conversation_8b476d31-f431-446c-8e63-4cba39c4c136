{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,UAAU", "names": ["propsIn: IconProps", "UserLock: IconComponent"], "sources": ["src/icons/UserLock.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path, Rect } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const UserLock: IconComponent = themed(\n  memo(function UserLock(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <_Circle cx=\"10\" cy=\"7\" r=\"4\" stroke={color} />\n        <Path d=\"M10.3 15H7a4 4 0 0 0-4 4v2\" stroke={color} />\n        <Path d=\"M15 15.5V14a2 2 0 0 1 4 0v1.5\" stroke={color} />\n        <Rect width=\"8\" height=\"5\" x=\"13\" y=\"16\" rx=\".899\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}