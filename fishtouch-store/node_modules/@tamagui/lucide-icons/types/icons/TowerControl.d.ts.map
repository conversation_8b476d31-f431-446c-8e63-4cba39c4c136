{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,cAAc", "names": ["propsIn: IconProps", "TowerControl: IconComponent"], "sources": ["src/icons/TowerControl.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const TowerControl: IconComponent = themed(\n  memo(function TowerControl(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path\n          d=\"M18.2 12.27 20 6H4l1.8 6.27a1 1 0 0 0 .95.73h10.5a1 1 0 0 0 .96-.73Z\"\n          stroke={color}\n        />\n        <Path d=\"M8 13v9\" stroke={color} />\n        <Path d=\"M16 22v-9\" stroke={color} />\n        <Path d=\"m9 6 1 7\" stroke={color} />\n        <Path d=\"m15 6-1 7\" stroke={color} />\n        <Path d=\"M12 6V2\" stroke={color} />\n        <Path d=\"M13 2h-2\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}