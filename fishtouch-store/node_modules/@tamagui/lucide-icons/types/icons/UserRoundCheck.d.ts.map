{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,gBAAgB", "names": ["propsIn: IconProps", "UserRoundCheck: IconComponent"], "sources": ["src/icons/UserRoundCheck.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const UserRoundCheck: IconComponent = themed(\n  memo(function UserRoundCheck(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M2 21a8 8 0 0 1 13.292-6\" stroke={color} />\n        <_Circle cx=\"10\" cy=\"8\" r=\"5\" stroke={color} />\n        <Path d=\"m16 19 2 2 4-4\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}