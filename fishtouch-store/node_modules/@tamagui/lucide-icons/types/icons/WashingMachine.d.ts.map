{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,gBAAgB", "names": ["propsIn: IconProps", "WashingMachine: IconComponent"], "sources": ["src/icons/WashingMachine.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path, Rect } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const WashingMachine: IconComponent = themed(\n  memo(function WashingMachine(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M3 6h3\" stroke={color} />\n        <Path d=\"M17 6h.01\" stroke={color} />\n        <Rect width=\"18\" height=\"20\" x=\"3\" y=\"2\" rx=\"2\" stroke={color} />\n        <_Circle cx=\"12\" cy=\"13\" r=\"5\" stroke={color} />\n        <Path d=\"M12 18a2.5 2.5 0 0 0 0-5 2.5 2.5 0 0 1 0-5\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}