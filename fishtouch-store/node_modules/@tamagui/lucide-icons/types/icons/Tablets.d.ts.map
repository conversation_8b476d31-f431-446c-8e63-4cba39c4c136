{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,SAAS", "names": ["propsIn: IconProps", "Tablets: IconComponent"], "sources": ["src/icons/Tablets.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Tablets: IconComponent = themed(\n  memo(function Tablets(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <_Circle cx=\"7\" cy=\"7\" r=\"5\" stroke={color} />\n        <_Circle cx=\"17\" cy=\"17\" r=\"5\" stroke={color} />\n        <Path d=\"M12 17h10\" stroke={color} />\n        <Path d=\"m3.46 10.54 7.08-7.08\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}