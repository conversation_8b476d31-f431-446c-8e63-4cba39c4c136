{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,OAAO", "names": ["propsIn: IconProps", "Vault: IconComponent"], "sources": ["src/icons/Vault.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path, Rect } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Vault: IconComponent = themed(\n  memo(function Vault(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Rect width=\"18\" height=\"18\" x=\"3\" y=\"3\" rx=\"2\" stroke={color} />\n        <_Circle cx=\"7.5\" cy=\"7.5\" r=\".5\" fill=\"currentColor\" stroke={color} />\n        <Path d=\"m7.9 7.9 2.7 2.7\" stroke={color} />\n        <_Circle cx=\"16.5\" cy=\"7.5\" r=\".5\" fill=\"currentColor\" stroke={color} />\n        <Path d=\"m13.4 10.6 2.7-2.7\" stroke={color} />\n        <_Circle cx=\"7.5\" cy=\"16.5\" r=\".5\" fill=\"currentColor\" stroke={color} />\n        <Path d=\"m7.9 16.1 2.7-2.7\" stroke={color} />\n        <_Circle cx=\"16.5\" cy=\"16.5\" r=\".5\" fill=\"currentColor\" stroke={color} />\n        <Path d=\"m13.4 13.4 2.7 2.7\" stroke={color} />\n        <_Circle cx=\"12\" cy=\"12\" r=\"2\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}