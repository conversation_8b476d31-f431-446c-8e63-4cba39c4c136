{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,SAAS", "names": ["propsIn: IconProps", "Tractor: IconComponent"], "sources": ["src/icons/Tractor.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Tractor: IconComponent = themed(\n  memo(function Tractor(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path\n          d=\"m10 11 11 .9a1 1 0 0 1 .8 1.1l-.665 4.158a1 1 0 0 1-.988.842H20\"\n          stroke={color}\n        />\n        <Path d=\"M16 18h-5\" stroke={color} />\n        <Path d=\"M18 5a1 1 0 0 0-1 1v5.573\" stroke={color} />\n        <Path d=\"M3 4h8.129a1 1 0 0 1 .99.863L13 11.246\" stroke={color} />\n        <Path d=\"M4 11V4\" stroke={color} />\n        <Path d=\"M7 15h.01\" stroke={color} />\n        <Path d=\"M8 10.1V4\" stroke={color} />\n        <_Circle cx=\"18\" cy=\"18\" r=\"2\" stroke={color} />\n        <_Circle cx=\"7\" cy=\"15\" r=\"5\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}