{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,QAAQ", "names": ["propsIn: IconProps", "Unlink: IconComponent"], "sources": ["src/icons/Unlink.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Line, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Unlink: IconComponent = themed(\n  memo(function Unlink(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path\n          d=\"m18.84 12.25 1.72-1.71h-.02a5.004 5.004 0 0 0-.12-7.07 5.006 5.006 0 0 0-6.95 0l-1.72 1.71\"\n          stroke={color}\n        />\n        <Path\n          d=\"m5.17 11.75-1.71 1.71a5.004 5.004 0 0 0 .12 7.07 5.006 5.006 0 0 0 6.95 0l1.71-1.71\"\n          stroke={color}\n        />\n        <Line x1=\"8\" x2=\"8\" y1=\"2\" y2=\"5\" stroke={color} />\n        <Line x1=\"2\" x2=\"5\" y1=\"8\" y2=\"8\" stroke={color} />\n        <Line x1=\"16\" x2=\"16\" y1=\"19\" y2=\"22\" stroke={color} />\n        <Line x1=\"19\" x2=\"22\" y1=\"16\" y2=\"16\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}