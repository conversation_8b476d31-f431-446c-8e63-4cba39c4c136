{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,WAAW", "names": ["propsIn: IconProps", "Telescope: IconComponent"], "sources": ["src/icons/Telescope.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Telescope: IconComponent = themed(\n  memo(function Telescope(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path\n          d=\"m10.065 12.493-6.18 1.318a.934.934 0 0 1-1.108-.702l-.537-2.15a1.07 1.07 0 0 1 .691-1.265l13.504-4.44\"\n          stroke={color}\n        />\n        <Path d=\"m13.56 11.747 4.332-.924\" stroke={color} />\n        <Path d=\"m16 21-3.105-6.21\" stroke={color} />\n        <Path\n          d=\"M16.485 5.94a2 2 0 0 1 1.455-2.425l1.09-.272a1 1 0 0 1 1.212.727l1.515 6.06a1 1 0 0 1-.727 1.213l-1.09.272a2 2 0 0 1-2.425-1.455z\"\n          stroke={color}\n        />\n        <Path d=\"m6.158 8.633 1.114 4.456\" stroke={color} />\n        <Path d=\"m8 21 3.105-6.21\" stroke={color} />\n        <_Circle cx=\"12\" cy=\"13\" r=\"2\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}