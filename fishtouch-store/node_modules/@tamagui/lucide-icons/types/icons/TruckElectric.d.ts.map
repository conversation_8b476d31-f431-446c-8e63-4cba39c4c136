{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,eAAe", "names": ["propsIn: IconProps", "TruckElectric: IconComponent"], "sources": ["src/icons/TruckElectric.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const TruckElectric: IconComponent = themed(\n  memo(function TruckElectric(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M14 19V7a2 2 0 0 0-2-2H9\" stroke={color} />\n        <Path d=\"M15 19H9\" stroke={color} />\n        <Path\n          d=\"M19 19h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.62L18.3 9.38a1 1 0 0 0-.78-.38H14\"\n          stroke={color}\n        />\n        <Path d=\"M2 13v5a1 1 0 0 0 1 1h2\" stroke={color} />\n        <Path\n          d=\"M4 3 2.15 5.15a.495.495 0 0 0 .35.86h2.15a.47.47 0 0 1 .35.86L3 9.02\"\n          stroke={color}\n        />\n        <_Circle cx=\"17\" cy=\"19\" r=\"2\" stroke={color} />\n        <_Circle cx=\"7\" cy=\"19\" r=\"2\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}