{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,aAAa", "names": ["propsIn: IconProps", "Transgender: IconComponent"], "sources": ["src/icons/Transgender.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Transgender: IconComponent = themed(\n  memo(function Transgender(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M12 16v6\" stroke={color} />\n        <Path d=\"M14 20h-4\" stroke={color} />\n        <Path d=\"M18 2h4v4\" stroke={color} />\n        <Path d=\"m2 2 7.17 7.17\" stroke={color} />\n        <Path d=\"M2 5.355V2h3.357\" stroke={color} />\n        <Path d=\"m22 2-7.17 7.17\" stroke={color} />\n        <Path d=\"M8 5 5 8\" stroke={color} />\n        <_Circle cx=\"12\" cy=\"12\" r=\"4\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}