{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,cAAc", "names": ["propsIn: IconProps", "SwitchCamera: IconComponent"], "sources": ["src/icons/SwitchCamera.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const SwitchCamera: IconComponent = themed(\n  memo(function SwitchCamera(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M11 19H4a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h5\" stroke={color} />\n        <Path d=\"M13 5h7a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-5\" stroke={color} />\n        <_Circle cx=\"12\" cy=\"12\" r=\"3\" stroke={color} />\n        <Path d=\"m18 22-3-3 3-3\" stroke={color} />\n        <Path d=\"m6 2 3 3-3 3\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}