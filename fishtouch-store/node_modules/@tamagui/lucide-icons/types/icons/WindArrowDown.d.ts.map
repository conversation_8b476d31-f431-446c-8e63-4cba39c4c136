{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,eAAe", "names": ["propsIn: IconProps", "WindArrowDown: IconComponent"], "sources": ["src/icons/WindArrowDown.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const WindArrowDown: IconComponent = themed(\n  memo(function WindArrowDown(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M10 2v8\" stroke={color} />\n        <Path d=\"M12.8 21.6A2 2 0 1 0 14 18H2\" stroke={color} />\n        <Path d=\"M17.5 10a2.5 2.5 0 1 1 2 4H2\" stroke={color} />\n        <Path d=\"m6 6 4 4 4-4\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}