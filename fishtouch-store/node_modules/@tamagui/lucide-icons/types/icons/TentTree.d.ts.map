{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,UAAU", "names": ["propsIn: IconProps", "TentTree: IconComponent"], "sources": ["src/icons/TentTree.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const TentTree: IconComponent = themed(\n  memo(function TentTree(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <_Circle cx=\"4\" cy=\"4\" r=\"2\" stroke={color} />\n        <Path d=\"m14 5 3-3 3 3\" stroke={color} />\n        <Path d=\"m14 10 3-3 3 3\" stroke={color} />\n        <Path d=\"M17 14V2\" stroke={color} />\n        <Path d=\"M17 14H7l-5 8h20Z\" stroke={color} />\n        <Path d=\"M8 14v8\" stroke={color} />\n        <Path d=\"m9 14 5 8\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}