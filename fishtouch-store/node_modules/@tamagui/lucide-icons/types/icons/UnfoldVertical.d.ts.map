{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,gBAAgB", "names": ["propsIn: IconProps", "UnfoldVertical: IconComponent"], "sources": ["src/icons/UnfoldVertical.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const UnfoldVertical: IconComponent = themed(\n  memo(function UnfoldVertical(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M12 22v-6\" stroke={color} />\n        <Path d=\"M12 8V2\" stroke={color} />\n        <Path d=\"M4 12H2\" stroke={color} />\n        <Path d=\"M10 12H8\" stroke={color} />\n        <Path d=\"M16 12h-2\" stroke={color} />\n        <Path d=\"M22 12h-2\" stroke={color} />\n        <Path d=\"m15 19-3 3-3-3\" stroke={color} />\n        <Path d=\"m15 5-3-3-3 3\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}