{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,OAAO", "names": ["propsIn: IconProps", "Timer: <PERSON><PERSON>Component"], "sources": ["src/icons/Timer.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Line } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Timer: IconComponent = themed(\n  memo(function Timer(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Line x1=\"10\" x2=\"14\" y1=\"2\" y2=\"2\" stroke={color} />\n        <Line x1=\"12\" x2=\"15\" y1=\"14\" y2=\"11\" stroke={color} />\n        <_Circle cx=\"12\" cy=\"14\" r=\"8\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}