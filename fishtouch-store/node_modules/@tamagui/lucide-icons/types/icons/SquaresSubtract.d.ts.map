{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,iBAAiB", "names": ["propsIn: IconProps", "SquaresSubtract: IconComponent"], "sources": ["src/icons/SquaresSubtract.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const SquaresSubtract: IconComponent = themed(\n  memo(function SquaresSubtract(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M10 22a2 2 0 0 1-2-2\" stroke={color} />\n        <Path d=\"M16 22h-2\" stroke={color} />\n        <Path\n          d=\"M16 4a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-5a2 2 0 0 1 2-2h5a1 1 0 0 0 1-1z\"\n          stroke={color}\n        />\n        <Path d=\"M20 8a2 2 0 0 1 2 2\" stroke={color} />\n        <Path d=\"M22 14v2\" stroke={color} />\n        <Path d=\"M22 20a2 2 0 0 1-2 2\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}