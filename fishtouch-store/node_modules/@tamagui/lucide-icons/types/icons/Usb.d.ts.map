{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,KAAK", "names": ["propsIn: IconProps", "Usb: IconComponent"], "sources": ["src/icons/Usb.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Usb: IconComponent = themed(\n  memo(function Usb(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <_Circle cx=\"10\" cy=\"7\" r=\"1\" stroke={color} />\n        <_Circle cx=\"4\" cy=\"20\" r=\"1\" stroke={color} />\n        <Path d=\"M4.7 19.3 19 5\" stroke={color} />\n        <Path d=\"m21 3-3 1 2 2Z\" stroke={color} />\n        <Path d=\"M9.26 7.68 5 12l2 5\" stroke={color} />\n        <Path d=\"m10 14 5 2 3.5-3.5\" stroke={color} />\n        <Path d=\"m18 12 1-1 1 1-1 1Z\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}