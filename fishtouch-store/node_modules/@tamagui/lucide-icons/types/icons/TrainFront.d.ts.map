{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,YAAY", "names": ["propsIn: IconProps", "TrainFront: IconComponent"], "sources": ["src/icons/TrainFront.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const TrainFront: IconComponent = themed(\n  memo(function TrainFront(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M8 3.1V7a4 4 0 0 0 8 0V3.1\" stroke={color} />\n        <Path d=\"m9 15-1-1\" stroke={color} />\n        <Path d=\"m15 15 1-1\" stroke={color} />\n        <Path\n          d=\"M9 19c-2.8 0-5-2.2-5-5v-4a8 8 0 0 1 16 0v4c0 2.8-2.2 5-5 5Z\"\n          stroke={color}\n        />\n        <Path d=\"m8 19-2 3\" stroke={color} />\n        <Path d=\"m16 19 2 3\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}