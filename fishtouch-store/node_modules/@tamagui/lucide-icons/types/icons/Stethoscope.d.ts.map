{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,aAAa", "names": ["propsIn: IconProps", "Stethoscope: IconComponent"], "sources": ["src/icons/Stethoscope.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Stethoscope: IconComponent = themed(\n  memo(function Stethoscope(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M11 2v2\" stroke={color} />\n        <Path d=\"M5 2v2\" stroke={color} />\n        <Path\n          d=\"M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1\"\n          stroke={color}\n        />\n        <Path d=\"M8 15a6 6 0 0 0 12 0v-3\" stroke={color} />\n        <_Circle cx=\"20\" cy=\"10\" r=\"2\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}