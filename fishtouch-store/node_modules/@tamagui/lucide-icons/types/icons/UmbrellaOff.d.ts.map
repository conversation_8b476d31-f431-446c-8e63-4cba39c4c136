{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,aAAa", "names": ["propsIn: IconProps", "UmbrellaOff: IconComponent"], "sources": ["src/icons/UmbrellaOff.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const UmbrellaOff: IconComponent = themed(\n  memo(function UmbrellaOff(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M12 2v1\" stroke={color} />\n        <Path\n          d=\"M15.5 21a1.85 1.85 0 0 1-3.5-1v-8H2a10 10 0 0 1 3.428-6.575\"\n          stroke={color}\n        />\n        <Path d=\"M17.5 12H22A10 10 0 0 0 9.004 3.455\" stroke={color} />\n        <Path d=\"m2 2 20 20\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}