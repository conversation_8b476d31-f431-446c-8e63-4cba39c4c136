{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,SAAS", "names": ["propsIn: IconProps", "Ungroup: IconComponent"], "sources": ["src/icons/Ungroup.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Rect } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Ungroup: IconComponent = themed(\n  memo(function Ungroup(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Rect width=\"8\" height=\"6\" x=\"5\" y=\"4\" rx=\"1\" stroke={color} />\n        <Rect width=\"8\" height=\"6\" x=\"11\" y=\"14\" rx=\"1\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}