{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,MAAM", "names": ["propsIn: IconProps", "Type: IconComponent"], "sources": ["src/icons/Type.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Line, Polyline } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Type: IconComponent = themed(\n  memo(function Type(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Polyline points=\"4 7 4 4 20 4 20 7\" stroke={color} />\n        <Line x1=\"9\" x2=\"15\" y1=\"20\" y2=\"20\" stroke={color} />\n        <Line x1=\"12\" x2=\"12\" y1=\"4\" y2=\"20\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}