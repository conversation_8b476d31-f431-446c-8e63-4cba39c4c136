{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,UAAU", "names": ["propsIn: IconProps", "Variable: IconComponent"], "sources": ["src/icons/Variable.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Line, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Variable: IconComponent = themed(\n  memo(function Variable(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M8 21s-4-3-4-9 4-9 4-9\" stroke={color} />\n        <Path d=\"M16 3s4 3 4 9-4 9-4 9\" stroke={color} />\n        <Line x1=\"15\" x2=\"9\" y1=\"9\" y2=\"15\" stroke={color} />\n        <Line x1=\"9\" x2=\"15\" y1=\"9\" y2=\"15\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}