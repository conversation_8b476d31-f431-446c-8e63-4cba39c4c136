{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,UAAU", "names": ["propsIn: IconProps", "Workflow: IconComponent"], "sources": ["src/icons/Workflow.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path, Rect } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Workflow: IconComponent = themed(\n  memo(function Workflow(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Rect width=\"8\" height=\"8\" x=\"3\" y=\"3\" rx=\"2\" stroke={color} />\n        <Path d=\"M7 11v4a2 2 0 0 0 2 2h4\" stroke={color} />\n        <Rect width=\"8\" height=\"8\" x=\"13\" y=\"13\" rx=\"2\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}