{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,QAAQ", "names": ["propsIn: IconProps", "Swords: IconComponent"], "sources": ["src/icons/Swords.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Line, Polyline } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Swords: IconComponent = themed(\n  memo(function Swords(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Polyline points=\"14.5 17.5 3 6 3 3 6 3 17.5 14.5\" stroke={color} />\n        <Line x1=\"13\" x2=\"19\" y1=\"19\" y2=\"13\" stroke={color} />\n        <Line x1=\"16\" x2=\"20\" y1=\"16\" y2=\"20\" stroke={color} />\n        <Line x1=\"19\" x2=\"21\" y1=\"21\" y2=\"19\" stroke={color} />\n        <Polyline points=\"14.5 6.5 18 3 21 3 21 6 17.5 9.5\" stroke={color} />\n        <Line x1=\"5\" x2=\"9\" y1=\"14\" y2=\"18\" stroke={color} />\n        <Line x1=\"7\" x2=\"4\" y1=\"17\" y2=\"20\" stroke={color} />\n        <Line x1=\"3\" x2=\"5\" y1=\"19\" y2=\"21\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}