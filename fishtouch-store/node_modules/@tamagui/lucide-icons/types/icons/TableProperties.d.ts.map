{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,iBAAiB", "names": ["propsIn: IconProps", "TableProperties: IconComponent"], "sources": ["src/icons/TableProperties.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path, Rect } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const TableProperties: IconComponent = themed(\n  memo(function TableProperties(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M15 3v18\" stroke={color} />\n        <Rect width=\"18\" height=\"18\" x=\"3\" y=\"3\" rx=\"2\" stroke={color} />\n        <Path d=\"M21 9H3\" stroke={color} />\n        <Path d=\"M21 15H3\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}