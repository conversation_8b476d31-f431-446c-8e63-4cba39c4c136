{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,WAAW", "names": ["propsIn: IconProps", "Videotape: IconComponent"], "sources": ["src/icons/Videotape.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path, Rect } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Videotape: IconComponent = themed(\n  memo(function Videotape(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Rect width=\"20\" height=\"16\" x=\"2\" y=\"4\" rx=\"2\" stroke={color} />\n        <Path d=\"M2 8h20\" stroke={color} />\n        <_Circle cx=\"8\" cy=\"14\" r=\"2\" stroke={color} />\n        <Path d=\"M8 12h8\" stroke={color} />\n        <_Circle cx=\"16\" cy=\"14\" r=\"2\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}