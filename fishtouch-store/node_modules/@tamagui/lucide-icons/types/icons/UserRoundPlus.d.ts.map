{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,eAAe", "names": ["propsIn: IconProps", "UserRoundPlus: IconComponent"], "sources": ["src/icons/UserRoundPlus.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const UserRoundPlus: IconComponent = themed(\n  memo(function UserRoundPlus(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M2 21a8 8 0 0 1 13.292-6\" stroke={color} />\n        <_Circle cx=\"10\" cy=\"8\" r=\"5\" stroke={color} />\n        <Path d=\"M19 16v6\" stroke={color} />\n        <Path d=\"M22 19h-6\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}