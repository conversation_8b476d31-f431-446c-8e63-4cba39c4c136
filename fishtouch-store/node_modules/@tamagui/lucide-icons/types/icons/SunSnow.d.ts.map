{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,SAAS", "names": ["propsIn: IconProps", "SunSnow: IconComponent"], "sources": ["src/icons/SunSnow.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const SunSnow: IconComponent = themed(\n  memo(function SunSnow(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M10 21v-1\" stroke={color} />\n        <Path d=\"M10 4V3\" stroke={color} />\n        <Path d=\"M10 9a3 3 0 0 0 0 6\" stroke={color} />\n        <Path d=\"m14 20 1.25-2.5L18 18\" stroke={color} />\n        <Path d=\"m14 4 1.25 2.5L18 6\" stroke={color} />\n        <Path d=\"m17 21-3-6 1.5-3H22\" stroke={color} />\n        <Path d=\"m17 3-3 6 1.5 3\" stroke={color} />\n        <Path d=\"M2 12h1\" stroke={color} />\n        <Path d=\"m20 10-1.5 2 1.5 2\" stroke={color} />\n        <Path d=\"m3.64 18.36.7-.7\" stroke={color} />\n        <Path d=\"m4.34 6.34-.7-.7\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}