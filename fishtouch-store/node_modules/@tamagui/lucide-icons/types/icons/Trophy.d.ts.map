{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,QAAQ", "names": ["propsIn: IconProps", "Trophy: IconComponent"], "sources": ["src/icons/Trophy.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Trophy: IconComponent = themed(\n  memo(function Trophy(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M6 9H4.5a2.5 2.5 0 0 1 0-5H6\" stroke={color} />\n        <Path d=\"M18 9h1.5a2.5 2.5 0 0 0 0-5H18\" stroke={color} />\n        <Path d=\"M4 22h16\" stroke={color} />\n        <Path\n          d=\"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22\"\n          stroke={color}\n        />\n        <Path\n          d=\"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22\"\n          stroke={color}\n        />\n        <Path d=\"M18 2H6v7a6 6 0 0 0 12 0V2Z\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}