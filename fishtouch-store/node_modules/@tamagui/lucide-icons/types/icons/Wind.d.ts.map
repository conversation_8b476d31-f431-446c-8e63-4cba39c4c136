{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,MAAM", "names": ["propsIn: IconProps", "Wind: IconComponent"], "sources": ["src/icons/Wind.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Wind: IconComponent = themed(\n  memo(function Wind(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M12.8 19.6A2 2 0 1 0 14 16H2\" stroke={color} />\n        <Path d=\"M17.5 8a2.5 2.5 0 1 1 2 4H2\" stroke={color} />\n        <Path d=\"M9.8 4.4A2 2 0 1 1 11 8H2\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}