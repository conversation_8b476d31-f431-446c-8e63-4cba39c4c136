{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,eAAe", "names": ["propsIn: IconProps", "Strikethrough: IconComponent"], "sources": ["src/icons/Strikethrough.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Line, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Strikethrough: IconComponent = themed(\n  memo(function Strikethrough(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M16 4H9a3 3 0 0 0-2.83 4\" stroke={color} />\n        <Path d=\"M14 12a4 4 0 0 1 0 8H6\" stroke={color} />\n        <Line x1=\"4\" x2=\"20\" y1=\"12\" y2=\"12\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}