{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,UAAU", "names": ["propsIn: IconProps", "Umbrella: IconComponent"], "sources": ["src/icons/Umbrella.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Umbrella: IconComponent = themed(\n  memo(function Umbrella(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M22 12a10.06 10.06 1 0 0-20 0Z\" stroke={color} />\n        <Path d=\"M12 12v8a2 2 0 0 0 4 0\" stroke={color} />\n        <Path d=\"M12 2v1\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}