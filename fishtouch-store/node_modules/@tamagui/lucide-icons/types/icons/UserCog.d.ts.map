{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,SAAS", "names": ["propsIn: IconProps", "UserCog: IconComponent"], "sources": ["src/icons/UserCog.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const UserCog: IconComponent = themed(\n  memo(function UserCog(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M10 15H6a4 4 0 0 0-4 4v2\" stroke={color} />\n        <Path d=\"m14.305 16.53.923-.382\" stroke={color} />\n        <Path d=\"m15.228 13.852-.923-.383\" stroke={color} />\n        <Path d=\"m16.852 12.228-.383-.923\" stroke={color} />\n        <Path d=\"m16.852 17.772-.383.924\" stroke={color} />\n        <Path d=\"m19.148 12.228.383-.923\" stroke={color} />\n        <Path d=\"m19.53 18.696-.382-.924\" stroke={color} />\n        <Path d=\"m20.772 13.852.924-.383\" stroke={color} />\n        <Path d=\"m20.772 16.148.924.383\" stroke={color} />\n        <_Circle cx=\"18\" cy=\"15\" r=\"3\" stroke={color} />\n        <_Circle cx=\"9\" cy=\"7\" r=\"4\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}