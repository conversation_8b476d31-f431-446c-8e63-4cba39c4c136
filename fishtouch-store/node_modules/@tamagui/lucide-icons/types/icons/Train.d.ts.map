{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,OAAO", "names": ["propsIn: IconProps", "Train: IconComponent"], "sources": ["src/icons/Train.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path, Rect } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Train: IconComponent = themed(\n  memo(function Train(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Rect width=\"16\" height=\"16\" x=\"4\" y=\"3\" rx=\"2\" stroke={color} />\n        <Path d=\"M4 11h16\" stroke={color} />\n        <Path d=\"M12 3v8\" stroke={color} />\n        <Path d=\"m8 19-2 3\" stroke={color} />\n        <Path d=\"m18 22-2-3\" stroke={color} />\n        <Path d=\"M8 15h0\" stroke={color} />\n        <Path d=\"M16 15h0\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}