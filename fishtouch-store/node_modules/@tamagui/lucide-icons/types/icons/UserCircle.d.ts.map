{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,YAAY", "names": ["propsIn: IconProps", "UserCircle: IconComponent"], "sources": ["src/icons/UserCircle.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const UserCircle: IconComponent = themed(\n  memo(function UserCircle(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <_Circle cx=\"12\" cy=\"12\" r=\"10\" stroke={color} />\n        <_Circle cx=\"12\" cy=\"10\" r=\"3\" stroke={color} />\n        <Path d=\"M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}