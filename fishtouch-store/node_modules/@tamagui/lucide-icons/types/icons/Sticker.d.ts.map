{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,SAAS", "names": ["propsIn: IconProps", "Sticker: IconComponent"], "sources": ["src/icons/Sticker.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Sticker: IconComponent = themed(\n  memo(function Sticker(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path\n          d=\"M15.5 3H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h14a2 2 0 0 0 2-2V8.5L15.5 3Z\"\n          stroke={color}\n        />\n        <Path d=\"M14 3v4a2 2 0 0 0 2 2h4\" stroke={color} />\n        <Path d=\"M8 13h.01\" stroke={color} />\n        <Path d=\"M16 13h.01\" stroke={color} />\n        <Path d=\"M10 16s.8 1 2 1c1.3 0 2-1 2-1\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}