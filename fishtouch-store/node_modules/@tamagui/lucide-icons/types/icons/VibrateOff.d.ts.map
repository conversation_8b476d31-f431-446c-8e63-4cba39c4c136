{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,YAAY", "names": ["propsIn: IconProps", "VibrateOff: IconComponent"], "sources": ["src/icons/VibrateOff.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Line, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const VibrateOff: IconComponent = themed(\n  memo(function VibrateOff(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"m2 8 2 2-2 2 2 2-2 2\" stroke={color} />\n        <Path d=\"m22 8-2 2 2 2-2 2 2 2\" stroke={color} />\n        <Path d=\"M8 8v10c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2\" stroke={color} />\n        <Path d=\"M16 10.34V6c0-.55-.45-1-1-1h-4.34\" stroke={color} />\n        <Line x1=\"2\" x2=\"22\" y1=\"2\" y2=\"22\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}