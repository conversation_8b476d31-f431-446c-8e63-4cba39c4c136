{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,aAAa", "names": ["propsIn: IconProps", "UtilityPole: IconComponent"], "sources": ["src/icons/UtilityPole.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const UtilityPole: IconComponent = themed(\n  memo(function UtilityPole(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M12 2v20\" stroke={color} />\n        <Path d=\"M2 5h20\" stroke={color} />\n        <Path d=\"M3 3v2\" stroke={color} />\n        <Path d=\"M7 3v2\" stroke={color} />\n        <Path d=\"M17 3v2\" stroke={color} />\n        <Path d=\"M21 3v2\" stroke={color} />\n        <Path d=\"m19 5-7 7-7-7\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}