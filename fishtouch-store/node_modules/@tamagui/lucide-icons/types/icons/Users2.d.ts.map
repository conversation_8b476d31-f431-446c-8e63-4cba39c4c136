{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,QAAQ", "names": ["propsIn: IconProps", "Users2: IconComponent"], "sources": ["src/icons/Users2.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Users2: IconComponent = themed(\n  memo(function Users2(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M14 19a6 6 0 0 0-12 0\" stroke={color} />\n        <_Circle cx=\"8\" cy=\"9\" r=\"4\" stroke={color} />\n        <Path d=\"M22 19a6 6 0 0 0-6-6 4 4 0 1 0 0-8\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}