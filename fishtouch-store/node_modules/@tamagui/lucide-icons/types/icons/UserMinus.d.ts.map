{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,WAAW", "names": ["propsIn: IconProps", "UserMinus: IconComponent"], "sources": ["src/icons/UserMinus.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Line, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const UserMinus: IconComponent = themed(\n  memo(function UserMinus(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\" stroke={color} />\n        <_Circle cx=\"9\" cy=\"7\" r=\"4\" stroke={color} />\n        <Line x1=\"22\" x2=\"16\" y1=\"11\" y2=\"11\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}