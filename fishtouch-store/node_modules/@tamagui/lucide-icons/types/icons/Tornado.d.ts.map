{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,SAAS", "names": ["propsIn: IconProps", "Tornado: IconComponent"], "sources": ["src/icons/Tornado.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Tornado: IconComponent = themed(\n  memo(function Tornado(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M21 4H3\" stroke={color} />\n        <Path d=\"M18 8H6\" stroke={color} />\n        <Path d=\"M19 12H9\" stroke={color} />\n        <Path d=\"M16 16h-6\" stroke={color} />\n        <Path d=\"M11 20H9\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}