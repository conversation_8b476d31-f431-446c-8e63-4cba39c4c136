{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,iBAAiB", "names": ["propsIn: IconProps", "TableCellsSplit: IconComponent"], "sources": ["src/icons/TableCellsSplit.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path, Rect } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const TableCellsSplit: IconComponent = themed(\n  memo(function TableCellsSplit(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M12 15V9\" stroke={color} />\n        <Path d=\"M3 15h18\" stroke={color} />\n        <Path d=\"M3 9h18\" stroke={color} />\n        <Rect width=\"18\" height=\"18\" x=\"3\" y=\"3\" rx=\"2\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}