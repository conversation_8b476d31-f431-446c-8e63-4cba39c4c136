{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,SAAS", "names": ["propsIn: IconProps", "WineOff: IconComponent"], "sources": ["src/icons/WineOff.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Line, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const WineOff: IconComponent = themed(\n  memo(function WineOff(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M8 22h8\" stroke={color} />\n        <Path d=\"M7 10h3m7 0h-1.343\" stroke={color} />\n        <Path d=\"M12 15v7\" stroke={color} />\n        <Path\n          d=\"M7.307 7.307A12.33 12.33 0 0 0 7 10a5 5 0 0 0 7.391 4.391M8.638 2.981C8.75 2.668 8.872 2.34 9 2h6c1.5 4 2 6 2 8 0 .407-.05.809-.145 1.198\"\n          stroke={color}\n        />\n        <Line x1=\"2\" x2=\"22\" y1=\"2\" y2=\"22\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}