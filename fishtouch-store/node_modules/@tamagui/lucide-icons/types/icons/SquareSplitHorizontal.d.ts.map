{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,uBAAuB", "names": ["propsIn: IconProps", "SquareSplitHorizontal: IconComponent"], "sources": ["src/icons/SquareSplitHorizontal.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Line, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const SquareSplitHorizontal: IconComponent = themed(\n  memo(function SquareSplitHorizontal(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M8 19H5c-1 0-2-1-2-2V7c0-1 1-2 2-2h3\" stroke={color} />\n        <Path d=\"M16 5h3c1 0 2 1 2 2v10c0 1-1 2-2 2h-3\" stroke={color} />\n        <Line x1=\"12\" x2=\"12\" y1=\"4\" y2=\"20\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}