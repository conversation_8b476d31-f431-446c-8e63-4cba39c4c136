{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,YAAY", "names": ["propsIn: IconProps", "TextSelect: IconComponent"], "sources": ["src/icons/TextSelect.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const TextSelect: IconComponent = themed(\n  memo(function TextSelect(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M14 21h1\" stroke={color} />\n        <Path d=\"M14 3h1\" stroke={color} />\n        <Path d=\"M19 3a2 2 0 0 1 2 2\" stroke={color} />\n        <Path d=\"M21 14v1\" stroke={color} />\n        <Path d=\"M21 19a2 2 0 0 1-2 2\" stroke={color} />\n        <Path d=\"M21 9v1\" stroke={color} />\n        <Path d=\"M3 14v1\" stroke={color} />\n        <Path d=\"M3 9v1\" stroke={color} />\n        <Path d=\"M5 21a2 2 0 0 1-2-2\" stroke={color} />\n        <Path d=\"M5 3a2 2 0 0 0-2 2\" stroke={color} />\n        <Path d=\"M7 12h10\" stroke={color} />\n        <Path d=\"M7 16h6\" stroke={color} />\n        <Path d=\"M7 8h8\" stroke={color} />\n        <Path d=\"M9 21h1\" stroke={color} />\n        <Path d=\"M9 3h1\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}