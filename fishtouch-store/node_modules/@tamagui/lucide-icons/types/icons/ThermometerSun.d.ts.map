{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,gBAAgB", "names": ["propsIn: IconProps", "ThermometerSun: IconComponent"], "sources": ["src/icons/ThermometerSun.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const ThermometerSun: IconComponent = themed(\n  memo(function ThermometerSun(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M12 9a4 4 0 0 0-2 7.5\" stroke={color} />\n        <Path d=\"M12 3v2\" stroke={color} />\n        <Path d=\"m6.6 18.4-1.4 1.4\" stroke={color} />\n        <Path d=\"M20 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z\" stroke={color} />\n        <Path d=\"M4 13H2\" stroke={color} />\n        <Path d=\"M6.34 7.34 4.93 5.93\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}