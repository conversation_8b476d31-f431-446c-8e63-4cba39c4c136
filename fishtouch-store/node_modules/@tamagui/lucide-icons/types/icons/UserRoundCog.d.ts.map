{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,cAAc", "names": ["propsIn: IconProps", "UserRoundCog: IconComponent"], "sources": ["src/icons/UserRoundCog.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const UserRoundCog: IconComponent = themed(\n  memo(function UserRoundCog(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"m14.305 19.53.923-.382\" stroke={color} />\n        <Path d=\"m15.228 16.852-.923-.383\" stroke={color} />\n        <Path d=\"m16.852 15.228-.383-.923\" stroke={color} />\n        <Path d=\"m16.852 20.772-.383.924\" stroke={color} />\n        <Path d=\"m19.148 15.228.383-.923\" stroke={color} />\n        <Path d=\"m19.53 21.696-.382-.924\" stroke={color} />\n        <Path d=\"M2 21a8 8 0 0 1 10.434-7.62\" stroke={color} />\n        <Path d=\"m20.772 16.852.924-.383\" stroke={color} />\n        <Path d=\"m20.772 19.148.924.383\" stroke={color} />\n        <_Circle cx=\"10\" cy=\"8\" r=\"5\" stroke={color} />\n        <_Circle cx=\"18\" cy=\"18\" r=\"3\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}