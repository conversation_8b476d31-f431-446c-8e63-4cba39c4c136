{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,WAAW", "names": ["propsIn: IconProps", "VolumeOff: IconComponent"], "sources": ["src/icons/VolumeOff.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const VolumeOff: IconComponent = themed(\n  memo(function VolumeOff(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M16 9a5 5 0 0 1 .95 2.293\" stroke={color} />\n        <Path d=\"M19.364 5.636a9 9 0 0 1 1.889 9.96\" stroke={color} />\n        <Path d=\"m2 2 20 20\" stroke={color} />\n        <Path\n          d=\"m7 7-.587.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298V11\"\n          stroke={color}\n        />\n        <Path d=\"M9.828 4.172A.686.686 0 0 1 11 4.657v.686\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}