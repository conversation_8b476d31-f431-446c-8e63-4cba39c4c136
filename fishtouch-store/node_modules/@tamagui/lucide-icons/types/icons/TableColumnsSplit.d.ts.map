{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,mBAAmB", "names": ["propsIn: IconProps", "TableColumnsSplit: IconComponent"], "sources": ["src/icons/TableColumnsSplit.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const TableColumnsSplit: IconComponent = themed(\n  memo(function TableColumnsSplit(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M14 14v2\" stroke={color} />\n        <Path d=\"M14 20v2\" stroke={color} />\n        <Path d=\"M14 2v2\" stroke={color} />\n        <Path d=\"M14 8v2\" stroke={color} />\n        <Path d=\"M2 15h8\" stroke={color} />\n        <Path d=\"M2 3h6a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H2\" stroke={color} />\n        <Path d=\"M2 9h8\" stroke={color} />\n        <Path d=\"M22 15h-4\" stroke={color} />\n        <Path d=\"M22 3h-2a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h2\" stroke={color} />\n        <Path d=\"M22 9h-4\" stroke={color} />\n        <Path d=\"M5 3v18\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}