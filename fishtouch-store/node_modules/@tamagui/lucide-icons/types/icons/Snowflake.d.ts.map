{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,WAAW", "names": ["propsIn: IconProps", "Snowflake: IconComponent"], "sources": ["src/icons/Snowflake.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Snowflake: IconComponent = themed(\n  memo(function Snowflake(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"m10 20-1.25-2.5L6 18\" stroke={color} />\n        <Path d=\"M10 4 8.75 6.5 6 6\" stroke={color} />\n        <Path d=\"m14 20 1.25-2.5L18 18\" stroke={color} />\n        <Path d=\"m14 4 1.25 2.5L18 6\" stroke={color} />\n        <Path d=\"m17 21-3-6h-4\" stroke={color} />\n        <Path d=\"m17 3-3 6 1.5 3\" stroke={color} />\n        <Path d=\"M2 12h6.5L10 9\" stroke={color} />\n        <Path d=\"m20 10-1.5 2 1.5 2\" stroke={color} />\n        <Path d=\"M22 12h-6.5L14 15\" stroke={color} />\n        <Path d=\"m4 10 1.5 2L4 14\" stroke={color} />\n        <Path d=\"m7 21 3-6-1.5-3\" stroke={color} />\n        <Path d=\"m7 3 3 6h4\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}