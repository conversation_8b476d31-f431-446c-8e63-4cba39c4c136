{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,YAAY", "names": ["propsIn: IconProps", "Volleyball: IconComponent"], "sources": ["src/icons/Volleyball.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Volleyball: IconComponent = themed(\n  memo(function Volleyball(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M11.1 7.1a16.55 16.55 0 0 1 10.9 4\" stroke={color} />\n        <Path d=\"M12 12a12.6 12.6 0 0 1-8.7 5\" stroke={color} />\n        <Path d=\"M16.8 13.6a16.55 16.55 0 0 1-9 7.5\" stroke={color} />\n        <Path d=\"M20.7 17a12.8 12.8 0 0 0-8.7-5 13.3 13.3 0 0 1 0-10\" stroke={color} />\n        <Path d=\"M6.3 3.8a16.55 16.55 0 0 0 1.9 11.5\" stroke={color} />\n        <_Circle cx=\"12\" cy=\"12\" r=\"10\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}