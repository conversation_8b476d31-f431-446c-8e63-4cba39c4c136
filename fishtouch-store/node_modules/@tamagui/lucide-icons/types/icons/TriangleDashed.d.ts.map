{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,gBAAgB", "names": ["propsIn: IconProps", "TriangleDashed: IconComponent"], "sources": ["src/icons/TriangleDashed.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const TriangleDashed: IconComponent = themed(\n  memo(function TriangleDashed(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M10.17 4.193a2 2 0 0 1 3.666.013\" stroke={color} />\n        <Path d=\"M14 21h2\" stroke={color} />\n        <Path d=\"m15.874 7.743 1 1.732\" stroke={color} />\n        <Path d=\"m18.849 12.952 1 1.732\" stroke={color} />\n        <Path d=\"M21.824 18.18a2 2 0 0 1-1.835 2.824\" stroke={color} />\n        <Path d=\"M4.024 21a2 2 0 0 1-1.839-2.839\" stroke={color} />\n        <Path d=\"m5.136 12.952-1 1.732\" stroke={color} />\n        <Path d=\"M8 21h2\" stroke={color} />\n        <Path d=\"m8.102 7.743-1 1.732\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}