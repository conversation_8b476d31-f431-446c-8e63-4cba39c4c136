{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,OAAO", "names": ["propsIn: IconProps", "Trash: IconComponent"], "sources": ["src/icons/Trash.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Trash: IconComponent = themed(\n  memo(function Trash(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M3 6h18\" stroke={color} />\n        <Path d=\"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\" stroke={color} />\n        <Path d=\"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}