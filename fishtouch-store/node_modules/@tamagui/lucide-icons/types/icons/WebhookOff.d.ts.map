{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,YAAY", "names": ["propsIn: IconProps", "WebhookOff: IconComponent"], "sources": ["src/icons/WebhookOff.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const WebhookOff: IconComponent = themed(\n  memo(function WebhookOff(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M17 17h-5c-1.09-.02-1.94.92-2.5 1.9A3 3 0 1 1 2.57 15\" stroke={color} />\n        <Path d=\"M9 3.4a4 4 0 0 1 6.52.66\" stroke={color} />\n        <Path d=\"m6 17 3.1-5.8a2.5 2.5 0 0 0 .057-2.05\" stroke={color} />\n        <Path d=\"M20.3 20.3a4 4 0 0 1-2.3.7\" stroke={color} />\n        <Path d=\"M18.6 13a4 4 0 0 1 3.357 3.414\" stroke={color} />\n        <Path d=\"m12 6 .6 1\" stroke={color} />\n        <Path d=\"m2 2 20 20\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}