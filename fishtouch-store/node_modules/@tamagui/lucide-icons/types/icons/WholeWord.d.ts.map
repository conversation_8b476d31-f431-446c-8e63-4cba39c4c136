{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,WAAW", "names": ["propsIn: IconProps", "WholeWord: IconComponent"], "sources": ["src/icons/WholeWord.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const WholeWord: IconComponent = themed(\n  memo(function WholeWord(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <_Circle cx=\"7\" cy=\"12\" r=\"3\" stroke={color} />\n        <Path d=\"M10 9v6\" stroke={color} />\n        <_Circle cx=\"17\" cy=\"12\" r=\"3\" stroke={color} />\n        <Path d=\"M14 7v8\" stroke={color} />\n        <Path d=\"M22 17v1c0 .5-.5 1-1 1H3c-.5 0-1-.5-1-1v-1\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}