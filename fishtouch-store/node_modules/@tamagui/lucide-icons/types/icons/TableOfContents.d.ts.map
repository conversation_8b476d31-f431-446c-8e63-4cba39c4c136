{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,iBAAiB", "names": ["propsIn: IconProps", "TableOfContents: IconComponent"], "sources": ["src/icons/TableOfContents.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const TableOfContents: IconComponent = themed(\n  memo(function TableOfContents(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M16 12H3\" stroke={color} />\n        <Path d=\"M16 18H3\" stroke={color} />\n        <Path d=\"M16 6H3\" stroke={color} />\n        <Path d=\"M21 12h.01\" stroke={color} />\n        <Path d=\"M21 18h.01\" stroke={color} />\n        <Path d=\"M21 6h.01\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}