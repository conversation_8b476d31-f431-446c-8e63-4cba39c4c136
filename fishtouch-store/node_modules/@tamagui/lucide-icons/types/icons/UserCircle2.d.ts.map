{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,aAAa", "names": ["propsIn: IconProps", "UserCircle2: IconComponent"], "sources": ["src/icons/UserCircle2.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const UserCircle2: IconComponent = themed(\n  memo(function UserCircle2(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M18 20a6 6 0 0 0-12 0\" stroke={color} />\n        <_Circle cx=\"12\" cy=\"10\" r=\"4\" stroke={color} />\n        <_Circle cx=\"12\" cy=\"12\" r=\"10\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}