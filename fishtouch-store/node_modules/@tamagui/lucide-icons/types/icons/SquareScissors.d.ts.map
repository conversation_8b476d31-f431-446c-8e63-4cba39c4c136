{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,gBAAgB", "names": ["propsIn: IconProps", "SquareScissors: IconComponent"], "sources": ["src/icons/SquareScissors.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path, Rect } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const SquareScissors: IconComponent = themed(\n  memo(function SquareScissors(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Rect width=\"20\" height=\"20\" x=\"2\" y=\"2\" rx=\"2\" stroke={color} />\n        <_Circle cx=\"8\" cy=\"8\" r=\"2\" stroke={color} />\n        <Path d=\"M9.414 9.414 12 12\" stroke={color} />\n        <Path d=\"M14.8 14.8 18 18\" stroke={color} />\n        <_Circle cx=\"8\" cy=\"16\" r=\"2\" stroke={color} />\n        <Path d=\"m18 6-8.586 8.586\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}