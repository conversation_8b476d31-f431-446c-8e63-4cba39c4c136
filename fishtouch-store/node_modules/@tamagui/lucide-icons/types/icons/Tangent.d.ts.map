{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,SAAS", "names": ["propsIn: IconProps", "Tangent: IconComponent"], "sources": ["src/icons/Tangent.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Tangent: IconComponent = themed(\n  memo(function Tangent(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <_Circle cx=\"17\" cy=\"4\" r=\"2\" stroke={color} />\n        <Path d=\"M15.59 5.41 5.41 15.59\" stroke={color} />\n        <_Circle cx=\"4\" cy=\"17\" r=\"2\" stroke={color} />\n        <Path d=\"M12 22s-4-9-1.5-11.5S22 12 22 12\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}