{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,UAAU", "names": ["propsIn: IconProps", "WrapText: IconComponent"], "sources": ["src/icons/WrapText.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Line, Path, Polyline } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const WrapText: IconComponent = themed(\n  memo(function WrapText(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Line x1=\"3\" x2=\"21\" y1=\"6\" y2=\"6\" stroke={color} />\n        <Path d=\"M3 12h15a3 3 0 1 1 0 6h-4\" stroke={color} />\n        <Polyline points=\"16 16 14 18 16 20\" stroke={color} />\n        <Line x1=\"3\" x2=\"10\" y1=\"18\" y2=\"18\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}