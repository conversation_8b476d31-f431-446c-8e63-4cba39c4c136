{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,iBAAiB", "names": ["propsIn: IconProps", "TextCursorInput: IconComponent"], "sources": ["src/icons/TextCursorInput.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const TextCursorInput: IconComponent = themed(\n  memo(function TextCursorInput(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M12 20h-1a2 2 0 0 1-2-2 2 2 0 0 1-2 2H6\" stroke={color} />\n        <Path d=\"M13 8h7a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-7\" stroke={color} />\n        <Path d=\"M5 16H4a2 2 0 0 1-2-2v-4a2 2 0 0 1 2-2h1\" stroke={color} />\n        <Path d=\"M6 4h1a2 2 0 0 1 2 2 2 2 0 0 1 2-2h1\" stroke={color} />\n        <Path d=\"M9 6v12\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}