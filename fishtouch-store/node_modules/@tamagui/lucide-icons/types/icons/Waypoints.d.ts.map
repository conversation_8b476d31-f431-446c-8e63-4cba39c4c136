{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,WAAW", "names": ["propsIn: IconProps", "Waypoints: IconComponent"], "sources": ["src/icons/Waypoints.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Waypoints: IconComponent = themed(\n  memo(function Waypoints(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <_Circle cx=\"12\" cy=\"4.5\" r=\"2.5\" stroke={color} />\n        <Path d=\"m10.2 6.3-3.9 3.9\" stroke={color} />\n        <_Circle cx=\"4.5\" cy=\"12\" r=\"2.5\" stroke={color} />\n        <Path d=\"M7 12h10\" stroke={color} />\n        <_Circle cx=\"19.5\" cy=\"12\" r=\"2.5\" stroke={color} />\n        <Path d=\"m13.8 17.7 3.9-3.9\" stroke={color} />\n        <_Circle cx=\"12\" cy=\"19.5\" r=\"2.5\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}