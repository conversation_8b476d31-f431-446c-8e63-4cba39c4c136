{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,OAAO", "names": ["propsIn: IconProps", "Wand2: IconComponent"], "sources": ["src/icons/Wand2.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Wand2: IconComponent = themed(\n  memo(function Wand2(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path\n          d=\"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72Z\"\n          stroke={color}\n        />\n        <Path d=\"m14 7 3 3\" stroke={color} />\n        <Path d=\"M5 6v4\" stroke={color} />\n        <Path d=\"M19 14v4\" stroke={color} />\n        <Path d=\"M10 2v2\" stroke={color} />\n        <Path d=\"M7 8H3\" stroke={color} />\n        <Path d=\"M21 16h-4\" stroke={color} />\n        <Path d=\"M11 3H9\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}