{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,SAAS", "names": ["propsIn: IconProps", "Tickets: IconComponent"], "sources": ["src/icons/Tickets.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path, Rect } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Tickets: IconComponent = themed(\n  memo(function Tickets(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"m4.5 8 10.58-5.06a1 1 0 0 1 1.342.488L18.5 8\" stroke={color} />\n        <Path d=\"M6 10V8\" stroke={color} />\n        <Path d=\"M6 14v1\" stroke={color} />\n        <Path d=\"M6 19v2\" stroke={color} />\n        <Rect x=\"2\" y=\"8\" width=\"20\" height=\"13\" rx=\"2\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}