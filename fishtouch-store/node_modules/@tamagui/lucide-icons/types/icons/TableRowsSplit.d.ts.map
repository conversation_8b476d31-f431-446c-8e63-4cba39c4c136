{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,gBAAgB", "names": ["propsIn: IconProps", "TableRowsSplit: IconComponent"], "sources": ["src/icons/TableRowsSplit.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const TableRowsSplit: IconComponent = themed(\n  memo(function TableRowsSplit(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M14 10h2\" stroke={color} />\n        <Path d=\"M15 22v-8\" stroke={color} />\n        <Path d=\"M15 2v4\" stroke={color} />\n        <Path d=\"M2 10h2\" stroke={color} />\n        <Path d=\"M20 10h2\" stroke={color} />\n        <Path d=\"M3 19h18\" stroke={color} />\n        <Path d=\"M3 22v-6a2 2 135 0 1 2-2h14a2 2 45 0 1 2 2v6\" stroke={color} />\n        <Path d=\"M3 2v2a2 2 45 0 0 2 2h14a2 2 135 0 0 2-2V2\" stroke={color} />\n        <Path d=\"M8 10h2\" stroke={color} />\n        <Path d=\"M9 22v-8\" stroke={color} />\n        <Path d=\"M9 2v4\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}