{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,QAAQ", "names": ["propsIn: IconProps", "Webcam: IconComponent"], "sources": ["src/icons/Webcam.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Webcam: IconComponent = themed(\n  memo(function Webcam(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <_Circle cx=\"12\" cy=\"10\" r=\"8\" stroke={color} />\n        <_Circle cx=\"12\" cy=\"10\" r=\"3\" stroke={color} />\n        <Path d=\"M7 22h10\" stroke={color} />\n        <Path d=\"M12 22v-4\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}