{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,MAAM", "names": ["propsIn: IconProps", "Tent: IconComponent"], "sources": ["src/icons/Tent.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Tent: IconComponent = themed(\n  memo(function Tent(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M3.5 21 14 3\" stroke={color} />\n        <Path d=\"M20.5 21 10 3\" stroke={color} />\n        <Path d=\"M15.5 21 12 15l-3.5 6\" stroke={color} />\n        <Path d=\"M2 21h20\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}