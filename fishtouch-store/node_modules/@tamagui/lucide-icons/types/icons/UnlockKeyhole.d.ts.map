{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,eAAe", "names": ["propsIn: IconProps", "UnlockKeyhole: IconComponent"], "sources": ["src/icons/UnlockKeyhole.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path, Rect } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const UnlockKeyhole: IconComponent = themed(\n  memo(function UnlockKeyhole(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <_Circle cx=\"12\" cy=\"16\" r=\"1\" stroke={color} />\n        <Rect x=\"3\" y=\"10\" width=\"18\" height=\"12\" rx=\"2\" stroke={color} />\n        <Path d=\"M7 10V7a5 5 0 0 1 9.33-2.5\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}