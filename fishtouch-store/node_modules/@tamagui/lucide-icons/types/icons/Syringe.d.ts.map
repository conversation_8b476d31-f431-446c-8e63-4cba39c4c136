{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,SAAS", "names": ["propsIn: IconProps", "Syringe: IconComponent"], "sources": ["src/icons/Syringe.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Syringe: IconComponent = themed(\n  memo(function Syringe(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"m18 2 4 4\" stroke={color} />\n        <Path d=\"m17 7 3-3\" stroke={color} />\n        <Path\n          d=\"M19 9 8.7 19.3c-1 1-2.5 1-3.4 0l-.6-.6c-1-1-1-2.5 0-3.4L15 5\"\n          stroke={color}\n        />\n        <Path d=\"m9 11 4 4\" stroke={color} />\n        <Path d=\"m5 19-3 3\" stroke={color} />\n        <Path d=\"m14 4 6 6\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}