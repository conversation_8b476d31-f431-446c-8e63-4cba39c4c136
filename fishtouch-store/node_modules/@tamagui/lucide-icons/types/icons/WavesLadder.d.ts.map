{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,aAAa", "names": ["propsIn: IconProps", "WavesLadder: IconComponent"], "sources": ["src/icons/WavesLadder.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const WavesLadder: IconComponent = themed(\n  memo(function WavesLadder(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M19 5a2 2 0 0 0-2 2v11\" stroke={color} />\n        <Path\n          d=\"M2 18c.6.5 1.2 1 2.5 1 2.5 0 2.5-2 5-2 2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1\"\n          stroke={color}\n        />\n        <Path d=\"M7 13h10\" stroke={color} />\n        <Path d=\"M7 9h10\" stroke={color} />\n        <Path d=\"M9 5a2 2 0 0 0-2 2v11\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}