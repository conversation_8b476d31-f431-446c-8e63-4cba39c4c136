{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,QAAQ", "names": ["propsIn: IconProps", "ZoomIn: IconComponent"], "sources": ["src/icons/ZoomIn.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Line } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const ZoomIn: IconComponent = themed(\n  memo(function ZoomIn(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <_Circle cx=\"11\" cy=\"11\" r=\"8\" stroke={color} />\n        <Line x1=\"21\" x2=\"16.65\" y1=\"21\" y2=\"16.65\" stroke={color} />\n        <Line x1=\"11\" x2=\"11\" y1=\"8\" y2=\"14\" stroke={color} />\n        <Line x1=\"8\" x2=\"14\" y1=\"11\" y2=\"11\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}