{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,cAAc", "names": ["propsIn: IconProps", "TrendingDown: IconComponent"], "sources": ["src/icons/TrendingDown.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Polyline } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const TrendingDown: IconComponent = themed(\n  memo(function TrendingDown(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Polyline points=\"22 17 13.5 8.5 8.5 13.5 2 7\" stroke={color} />\n        <Polyline points=\"16 17 22 17 22 11\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}