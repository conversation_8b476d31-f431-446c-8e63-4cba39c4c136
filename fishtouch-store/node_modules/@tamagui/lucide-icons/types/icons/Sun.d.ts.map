{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,KAAK", "names": ["propsIn: IconProps", "Sun: IconComponent"], "sources": ["src/icons/Sun.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Sun: IconComponent = themed(\n  memo(function Sun(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <_Circle cx=\"12\" cy=\"12\" r=\"4\" stroke={color} />\n        <Path d=\"M12 2v2\" stroke={color} />\n        <Path d=\"M12 20v2\" stroke={color} />\n        <Path d=\"m4.93 4.93 1.41 1.41\" stroke={color} />\n        <Path d=\"m17.66 17.66 1.41 1.41\" stroke={color} />\n        <Path d=\"M2 12h2\" stroke={color} />\n        <Path d=\"M20 12h2\" stroke={color} />\n        <Path d=\"m6.34 17.66-1.41 1.41\" stroke={color} />\n        <Path d=\"m19.07 4.93-1.41 1.41\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}