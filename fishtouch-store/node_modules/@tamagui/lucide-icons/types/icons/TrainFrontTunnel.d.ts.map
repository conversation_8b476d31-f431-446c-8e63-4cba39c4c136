{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,kBAAkB", "names": ["propsIn: IconProps", "TrainFrontTunnel: IconComponent"], "sources": ["src/icons/TrainFrontTunnel.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const TrainFrontTunnel: IconComponent = themed(\n  memo(function TrainFrontTunnel(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M2 22V12a10 10 0 1 1 20 0v10\" stroke={color} />\n        <Path d=\"M15 6.8v1.4a3 2.8 0 1 1-6 0V6.8\" stroke={color} />\n        <Path d=\"M10 15h.01\" stroke={color} />\n        <Path d=\"M14 15h.01\" stroke={color} />\n        <Path\n          d=\"M10 19a4 4 0 0 1-4-4v-3a6 6 0 1 1 12 0v3a4 4 0 0 1-4 4Z\"\n          stroke={color}\n        />\n        <Path d=\"m9 19-2 3\" stroke={color} />\n        <Path d=\"m15 19 2 3\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}