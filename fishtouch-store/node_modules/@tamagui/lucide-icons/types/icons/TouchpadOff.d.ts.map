{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,aAAa", "names": ["propsIn: IconProps", "TouchpadOff: IconComponent"], "sources": ["src/icons/TouchpadOff.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const TouchpadOff: IconComponent = themed(\n  memo(function TouchpadOff(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M12 20v-6\" stroke={color} />\n        <Path d=\"M19.656 14H22\" stroke={color} />\n        <Path d=\"M2 14h12\" stroke={color} />\n        <Path d=\"m2 2 20 20\" stroke={color} />\n        <Path d=\"M20 20H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2\" stroke={color} />\n        <Path d=\"M9.656 4H20a2 2 0 0 1 2 2v10.344\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}