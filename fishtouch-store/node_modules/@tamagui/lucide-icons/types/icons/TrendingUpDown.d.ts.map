{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,gBAAgB", "names": ["propsIn: IconProps", "TrendingUpDown: IconComponent"], "sources": ["src/icons/TrendingUpDown.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const TrendingUpDown: IconComponent = themed(\n  memo(function TrendingUpDown(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M14.828 14.828 21 21\" stroke={color} />\n        <Path d=\"M21 16v5h-5\" stroke={color} />\n        <Path d=\"m21 3-9 9-4-4-6 6\" stroke={color} />\n        <Path d=\"M21 8V3h-5\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}