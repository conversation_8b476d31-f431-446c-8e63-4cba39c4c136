{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,sBAAsB", "names": ["propsIn: IconProps", "ThermometerSnowflake: IconComponent"], "sources": ["src/icons/ThermometerSnowflake.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const ThermometerSnowflake: IconComponent = themed(\n  memo(function ThermometerSnowflake(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"m10 20-1.25-2.5L6 18\" stroke={color} />\n        <Path d=\"M10 4 8.75 6.5 6 6\" stroke={color} />\n        <Path d=\"M10.585 15H10\" stroke={color} />\n        <Path d=\"M2 12h6.5L10 9\" stroke={color} />\n        <Path d=\"M20 14.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0z\" stroke={color} />\n        <Path d=\"m4 10 1.5 2L4 14\" stroke={color} />\n        <Path d=\"m7 21 3-6-1.5-3\" stroke={color} />\n        <Path d=\"m7 3 3 6h2\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}