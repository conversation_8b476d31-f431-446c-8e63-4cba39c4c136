{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,YAAY", "names": ["propsIn: IconProps", "TrainTrack: IconComponent"], "sources": ["src/icons/TrainTrack.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const TrainTrack: IconComponent = themed(\n  memo(function TrainTrack(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M2 17 17 2\" stroke={color} />\n        <Path d=\"m2 14 8 8\" stroke={color} />\n        <Path d=\"m5 11 8 8\" stroke={color} />\n        <Path d=\"m8 8 8 8\" stroke={color} />\n        <Path d=\"m11 5 8 8\" stroke={color} />\n        <Path d=\"m14 2 8 8\" stroke={color} />\n        <Path d=\"M7 22 22 7\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}