{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,OAAO", "names": ["propsIn: IconProps", "Snail: IconComponent"], "sources": ["src/icons/Snail.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Snail: IconComponent = themed(\n  memo(function Snail(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M2 13a6 6 0 1 0 12 0 4 4 0 1 0-8 0 2 2 0 0 0 4 0\" stroke={color} />\n        <_Circle cx=\"10\" cy=\"13\" r=\"8\" stroke={color} />\n        <Path d=\"M2 21h12c4.4 0 8-3.6 8-8V7a2 2 0 1 0-4 0v6\" stroke={color} />\n        <Path d=\"M18 3 19.1 5.2\" stroke={color} />\n        <Path d=\"M22 3 20.9 5.2\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}