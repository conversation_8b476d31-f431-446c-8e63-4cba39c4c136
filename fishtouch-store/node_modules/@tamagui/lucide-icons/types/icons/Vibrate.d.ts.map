{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,SAAS", "names": ["propsIn: IconProps", "Vibrate: IconComponent"], "sources": ["src/icons/Vibrate.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path, Rect } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Vibrate: IconComponent = themed(\n  memo(function Vibrate(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"m2 8 2 2-2 2 2 2-2 2\" stroke={color} />\n        <Path d=\"m22 8-2 2 2 2-2 2 2 2\" stroke={color} />\n        <Rect width=\"8\" height=\"14\" x=\"8\" y=\"5\" rx=\"1\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}