{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,SAAS", "names": ["propsIn: IconProps", "Sunrise: IconComponent"], "sources": ["src/icons/Sunrise.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Sunrise: IconComponent = themed(\n  memo(function Sunrise(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M12 2v8\" stroke={color} />\n        <Path d=\"m4.93 10.93 1.41 1.41\" stroke={color} />\n        <Path d=\"M2 18h2\" stroke={color} />\n        <Path d=\"M20 18h2\" stroke={color} />\n        <Path d=\"m19.07 10.93-1.41 1.41\" stroke={color} />\n        <Path d=\"M22 22H2\" stroke={color} />\n        <Path d=\"m8 6 4-4 4 4\" stroke={color} />\n        <Path d=\"M16 18a4 4 0 0 0-8 0\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}