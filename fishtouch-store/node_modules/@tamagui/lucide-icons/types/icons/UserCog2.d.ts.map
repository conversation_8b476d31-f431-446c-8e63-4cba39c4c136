{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,UAAU", "names": ["propsIn: IconProps", "UserCog2: IconComponent"], "sources": ["src/icons/UserCog2.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const UserCog2: IconComponent = themed(\n  memo(function UserCog2(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <_Circle cx=\"18\" cy=\"15\" r=\"3\" stroke={color} />\n        <_Circle cx=\"8\" cy=\"9\" r=\"4\" stroke={color} />\n        <Path d=\"M10.5 13.5A6 6 0 0 0 2 19\" stroke={color} />\n        <Path d=\"m21.7 16.4-.9-.3\" stroke={color} />\n        <Path d=\"m15.2 13.9-.9-.3\" stroke={color} />\n        <Path d=\"m16.6 18.7.3-.9\" stroke={color} />\n        <Path d=\"m19.1 12.2.3-.9\" stroke={color} />\n        <Path d=\"m19.6 18.7-.4-1\" stroke={color} />\n        <Path d=\"m16.8 12.3-.4-1\" stroke={color} />\n        <Path d=\"m14.3 16.6 1-.4\" stroke={color} />\n        <Path d=\"m20.7 13.8 1-.4\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}