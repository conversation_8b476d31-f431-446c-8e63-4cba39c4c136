{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,cAAc", "names": ["propsIn: IconProps", "VenusAndMars: IconComponent"], "sources": ["src/icons/VenusAndMars.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Circle as _Circle, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const VenusAndMars: IconComponent = themed(\n  memo(function VenusAndMars(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M10 20h4\" stroke={color} />\n        <Path d=\"M12 16v6\" stroke={color} />\n        <Path d=\"M17 2h4v4\" stroke={color} />\n        <Path d=\"m21 2-5.46 5.46\" stroke={color} />\n        <_Circle cx=\"12\" cy=\"11\" r=\"5\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}