{"mappings": "AAEA,cAAc,iBAAiB,uBAAuB;KAIjD,iBAAiBA,SAAS,cAAc,IAAI;AAEjD,OAAO,cAAMC,MAAM", "names": ["propsIn: IconProps", "Wand: IconComponent"], "sources": ["src/icons/Wand.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { memo } from 'react'\nimport type { IconProps } from '@tamagui/helpers-icon'\nimport { Svg, Path } from 'react-native-svg'\nimport { themed } from '@tamagui/helpers-icon'\n\ntype IconComponent = (propsIn: IconProps) => JSX.Element\n\nexport const Wand: IconComponent = themed(\n  memo(function Wand(props: IconProps) {\n    const { color = 'black', size = 24, ...otherProps } = props\n    return (\n      <Svg\n        width={size}\n        height={size}\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke={color}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        {...otherProps}\n      >\n        <Path d=\"M15 4V2\" stroke={color} />\n        <Path d=\"M15 16v-2\" stroke={color} />\n        <Path d=\"M8 9h2\" stroke={color} />\n        <Path d=\"M20 9h2\" stroke={color} />\n        <Path d=\"M17.8 11.8 19 13\" stroke={color} />\n        <Path d=\"M15 9h.01\" stroke={color} />\n        <Path d=\"M17.8 6.2 19 5\" stroke={color} />\n        <Path d=\"m3 21 9-9\" stroke={color} />\n        <Path d=\"M12.2 6.2 11 5\" stroke={color} />\n      </Svg>\n    )\n  })\n)\n"], "version": 3}