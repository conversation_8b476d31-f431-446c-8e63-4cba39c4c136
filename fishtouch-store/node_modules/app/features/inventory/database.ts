// Cross-platform database implementation
// Delegates to platform-specific implementations
import { Platform } from 'react-native';

// Re-export all functions with platform-specific delegation
export const initializeDatabase = async () => {
  if (Platform.OS === 'web') {
    const webDb = await import('./database.web');
    return await webDb.initializeDatabase();
  } else {
    const nativeDb = await import('./database.native');
    return await nativeDb.initializeDatabase();
  }
};

export const getDatabase = async () => {
  if (Platform.OS === 'web') {
    const webDb = await import('./database.web');
    return await webDb.getDatabase();
  } else {
    const nativeDb = await import('./database.native');
    return await nativeDb.getDatabase();
  }
};

export const closeDatabase = async () => {
  if (Platform.OS === 'web') {
    const webDb = await import('./database.web');
    return await webDb.closeDatabase();
  } else {
    const nativeDb = await import('./database.native');
    return await nativeDb.closeDatabase();
  }
};

export const executeQuery = async <T>(query: string, params: any[] = []): Promise<T[]> => {
  if (Platform.OS === 'web') {
    const webDb = await import('./database.web');
    return await webDb.executeQuery<T>(query, params);
  } else {
    const nativeDb = await import('./database.native');
    return await nativeDb.executeQuery<T>(query, params);
  }
};

export const executeQueryFirst = async <T>(query: string, params: any[] = []): Promise<T | null> => {
  if (Platform.OS === 'web') {
    const webDb = await import('./database.web');
    return await webDb.executeQueryFirst<T>(query, params);
  } else {
    const nativeDb = await import('./database.native');
    return await nativeDb.executeQueryFirst<T>(query, params);
  }
};

export const executeUpdate = async (query: string, params: any[] = []) => {
  if (Platform.OS === 'web') {
    const webDb = await import('./database.web');
    return await webDb.executeUpdate(query, params);
  } else {
    const nativeDb = await import('./database.native');
    return await nativeDb.executeUpdate(query, params);
  }
};

export const executeTransaction = async <T>(operations: any): Promise<T> => {
  if (Platform.OS === 'web') {
    const webDb = await import('./database.web');
    return await webDb.executeTransaction<T>(operations);
  } else {
    const nativeDb = await import('./database.native');
    return await nativeDb.executeTransaction<T>(operations);
  }
};

export const seedDatabase = async () => {
  if (Platform.OS === 'web') {
    const webDb = await import('./database.web');
    return await webDb.seedDatabase();
  } else {
    const nativeDb = await import('./database.native');
    return await nativeDb.seedDatabase();
  }
};

export const clearDatabase = async () => {
  if (Platform.OS === 'web') {
    const webDb = await import('./database.web');
    return await webDb.clearDatabase();
  } else {
    const nativeDb = await import('./database.native');
    return await nativeDb.clearDatabase();
  }
};
