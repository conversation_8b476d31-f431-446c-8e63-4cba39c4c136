import {
  Product,
  Package,
  Batch,
  Inventory,
  Transaction,
  StockAlert,
  Shipment,
  ProductWithStock,
  PackageWithItems,
  TransactionWithDetails,
  InventoryDashboard,
  CreateProductForm,
  CreatePackageForm,
  CreateBatchForm,
  CreateTransactionForm,
  CreateShipmentForm,
  TransactionType,
  AlertType,
  BatchWithInventory
} from './types';
import { executeQuery, executeQueryFirst, executeUpdate, executeTransaction } from './database';

// Product Services
export const productService = {
  async getAll(): Promise<Product[]> {
    return executeQuery<Product>('SELECT * FROM products ORDER BY name');
  },

  async getById(id: number): Promise<Product | null> {
    return executeQueryFirst<Product>('SELECT * FROM products WHERE id = ?', [id]);
  },

  async getBySku(sku: string): Promise<Product | null> {
    return executeQueryFirst<Product>('SELECT * FROM products WHERE sku = ?', [sku]);
  },

  async getWithStock(): Promise<ProductWithStock[]> {
    const query = `
      SELECT 
        p.*,
        COALESCE(SUM(i.quantity), 0) as total_stock,
        COALESCE(SUM(i.quantity - i.reserved_quantity), 0) as available_stock
      FROM products p
      LEFT JOIN inventory i ON p.id = i.product_id
      GROUP BY p.id
      ORDER BY p.name
    `;

    const products = await executeQuery<ProductWithStock>(query);

    // Get batches and alerts for each product
    for (const product of products) {
      product.batches = await batchService.getByProductId(product.id);
      product.alerts = await stockAlertService.getByProductId(product.id);
    }

    return products;
  },

  async create(data: CreateProductForm): Promise<number> {
    const result = await executeUpdate(
      `INSERT INTO products (name, sku, description, unit, min_stock_level) 
       VALUES (?, ?, ?, ?, ?)`,
      [data.name, data.sku, data.description || null, data.unit, data.min_stock_level]
    );
    return result.lastInsertRowId!;
  },

  async update(id: number, data: Partial<CreateProductForm>): Promise<void> {
    const fields = Object.keys(data).map(key => `${key} = ?`).join(', ');
    const values = Object.values(data);

    await executeUpdate(
      `UPDATE products SET ${fields} WHERE id = ?`,
      [...values, id]
    );
  },

  async delete(id: number): Promise<void> {
    await executeUpdate('DELETE FROM products WHERE id = ?', [id]);
  }
};

// Package Services
export const packageService = {
  async getAll(): Promise<Package[]> {
    return executeQuery<Package>('SELECT * FROM packages ORDER BY name');
  },

  async getById(id: number): Promise<Package | null> {
    return executeQueryFirst<Package>('SELECT * FROM packages WHERE id = ?', [id]);
  },

  async getWithItems(): Promise<PackageWithItems[]> {
    const packages = await executeQuery<Package>('SELECT * FROM packages ORDER BY name');

    for (const pkg of packages) {
      const items = await executeQuery<any>(
        `SELECT pi.*, p.name as product_name, p.sku, p.unit, b.cost_per_unit
         FROM package_items pi
         JOIN products p ON pi.product_id = p.id
         LEFT JOIN batches b ON p.id = b.product_id
         WHERE pi.package_id = ?
         GROUP BY pi.id`,
        [pkg.id]
      );

      (pkg as PackageWithItems).items = items;
      (pkg as PackageWithItems).total_value = items.reduce(
        (sum, item) => sum + (item.quantity * (item.cost_per_unit || 0)),
        0
      );
    }

    return packages as PackageWithItems[];
  },

  async create(data: CreatePackageForm): Promise<number> {
    return executeTransaction(async (db) => {
      const packageResult = await db.runAsync(
        'INSERT INTO packages (name, description) VALUES (?, ?)',
        [data.name, data.description || null]
      );

      const packageId = packageResult.lastInsertRowId!;

      for (const item of data.items) {
        await db.runAsync(
          'INSERT INTO package_items (package_id, product_id, quantity) VALUES (?, ?, ?)',
          [packageId, item.product_id, item.quantity]
        );
      }

      return packageId;
    });
  },

  async update(id: number, data: Partial<CreatePackageForm>): Promise<void> {
    await executeTransaction(async (db) => {
      if (data.name || data.description) {
        await db.runAsync(
          'UPDATE packages SET name = COALESCE(?, name), description = COALESCE(?, description) WHERE id = ?',
          [data.name || null, data.description || null, id]
        );
      }

      if (data.items) {
        await db.runAsync('DELETE FROM package_items WHERE package_id = ?', [id]);

        for (const item of data.items) {
          await db.runAsync(
            'INSERT INTO package_items (package_id, product_id, quantity) VALUES (?, ?, ?)',
            [id, item.product_id, item.quantity]
          );
        }
      }
    });
  },

  async delete(id: number): Promise<void> {
    await executeUpdate('DELETE FROM packages WHERE id = ?', [id]);
  }
};

// Batch Services
export const batchService = {
  async getAll(): Promise<Batch[]> {
    return executeQuery<Batch>('SELECT * FROM batches ORDER BY created_at DESC');
  },

  async getById(id: number): Promise<Batch | null> {
    return executeQueryFirst<Batch>('SELECT * FROM batches WHERE id = ?', [id]);
  },

  async getByProductId(productId: number): Promise<BatchWithInventory[]> {
    const batches = await executeQuery<Batch>(
      'SELECT * FROM batches WHERE product_id = ? ORDER BY created_at DESC',
      [productId]
    );

    for (const batch of batches) {
      const inventory = await executeQuery<Inventory>(
        'SELECT * FROM inventory WHERE batch_id = ?',
        [batch.id]
      );

      (batch as BatchWithInventory).inventory = inventory;
      (batch as BatchWithInventory).total_quantity = inventory.reduce(
        (sum, inv) => sum + inv.quantity,
        0
      );
      (batch as BatchWithInventory).available_quantity = inventory.reduce(
        (sum, inv) => sum + (inv.quantity - inv.reserved_quantity),
        0
      );
    }

    return batches as BatchWithInventory[];
  },

  async create(data: CreateBatchForm): Promise<number> {
    return executeTransaction(async (db) => {
      const batchResult = await db.runAsync(
        `INSERT INTO batches (product_id, batch_number, supplier, cost_per_unit, expiry_date) 
         VALUES (?, ?, ?, ?, ?)`,
        [data.product_id, data.batch_number, data.supplier || null, data.cost_per_unit, data.expiry_date || null]
      );

      const batchId = batchResult.lastInsertRowId!;

      // Create initial inventory record
      await db.runAsync(
        'INSERT INTO inventory (product_id, batch_id, quantity, location) VALUES (?, ?, ?, ?)',
        [data.product_id, batchId, data.initial_quantity, data.location || null]
      );

      // Create initial inbound transaction
      await db.runAsync(
        `INSERT INTO transactions (type, product_id, batch_id, quantity, notes) 
         VALUES (?, ?, ?, ?, ?)`,
        ['INBOUND', data.product_id, batchId, data.initial_quantity, 'Initial batch creation']
      );

      return batchId;
    });
  },

  async update(id: number, data: Partial<CreateBatchForm>): Promise<void> {
    const fields = Object.keys(data)
      .filter(key => key !== 'initial_quantity')
      .map(key => `${key} = ?`)
      .join(', ');
    const values = Object.entries(data)
      .filter(([key]) => key !== 'initial_quantity')
      .map(([, value]) => value);

    if (fields) {
      await executeUpdate(
        `UPDATE batches SET ${fields} WHERE id = ?`,
        [...values, id]
      );
    }
  },

  async delete(id: number): Promise<void> {
    await executeUpdate('DELETE FROM batches WHERE id = ?', [id]);
  }
};

// Inventory Services
export const inventoryService = {
  async getAll(): Promise<Inventory[]> {
    return executeQuery<Inventory>('SELECT * FROM inventory');
  },

  async getByProductId(productId: number): Promise<Inventory[]> {
    return executeQuery<Inventory>(
      'SELECT * FROM inventory WHERE product_id = ?',
      [productId]
    );
  },

  async getByBatchId(batchId: number): Promise<Inventory[]> {
    return executeQuery<Inventory>(
      'SELECT * FROM inventory WHERE batch_id = ?',
      [batchId]
    );
  },

  async updateQuantity(productId: number, batchId: number, quantity: number, location?: string): Promise<void> {
    const existing = await executeQueryFirst<Inventory>(
      'SELECT * FROM inventory WHERE product_id = ? AND batch_id = ? AND (location = ? OR (location IS NULL AND ? IS NULL))',
      [productId, batchId, location || null, location || null]
    );

    if (existing) {
      await executeUpdate(
        'UPDATE inventory SET quantity = ? WHERE id = ?',
        [quantity, existing.id]
      );
    } else {
      await executeUpdate(
        'INSERT INTO inventory (product_id, batch_id, quantity, location) VALUES (?, ?, ?, ?)',
        [productId, batchId, quantity, location || null]
      );
    }
  },

  async adjustStock(productId: number, batchId: number, adjustment: number, location?: string): Promise<void> {
    const existing = await executeQueryFirst<Inventory>(
      'SELECT * FROM inventory WHERE product_id = ? AND batch_id = ? AND (location = ? OR (location IS NULL AND ? IS NULL))',
      [productId, batchId, location || null, location || null]
    );

    if (existing) {
      const newQuantity = Math.max(0, existing.quantity + adjustment);
      await executeUpdate(
        'UPDATE inventory SET quantity = ? WHERE id = ?',
        [newQuantity, existing.id]
      );
    } else if (adjustment > 0) {
      await executeUpdate(
        'INSERT INTO inventory (product_id, batch_id, quantity, location) VALUES (?, ?, ?, ?)',
        [productId, batchId, adjustment, location || null]
      );
    }
  }
};

// Transaction Services
export const transactionService = {
  async getAll(): Promise<TransactionWithDetails[]> {
    const query = `
      SELECT
        t.*,
        p.name as product_name, p.sku, p.unit,
        b.batch_number, b.supplier, b.cost_per_unit
      FROM transactions t
      JOIN products p ON t.product_id = p.id
      JOIN batches b ON t.batch_id = b.id
      ORDER BY t.created_at DESC
    `;

    return executeQuery<TransactionWithDetails>(query);
  },

  async getByProductId(productId: number): Promise<TransactionWithDetails[]> {
    const query = `
      SELECT
        t.*,
        p.name as product_name, p.sku, p.unit,
        b.batch_number, b.supplier, b.cost_per_unit
      FROM transactions t
      JOIN products p ON t.product_id = p.id
      JOIN batches b ON t.batch_id = b.id
      WHERE t.product_id = ?
      ORDER BY t.created_at DESC
    `;

    return executeQuery<TransactionWithDetails>(query, [productId]);
  },

  async getRecent(limit: number = 10): Promise<TransactionWithDetails[]> {
    const query = `
      SELECT
        t.*,
        p.name as product_name, p.sku, p.unit,
        b.batch_number, b.supplier, b.cost_per_unit
      FROM transactions t
      JOIN products p ON t.product_id = p.id
      JOIN batches b ON t.batch_id = b.id
      ORDER BY t.created_at DESC
      LIMIT ?
    `;

    return executeQuery<TransactionWithDetails>(query, [limit]);
  },

  async create(data: CreateTransactionForm): Promise<number> {
    return executeTransaction(async (db) => {
      // Create transaction record
      const transactionResult = await db.runAsync(
        `INSERT INTO transactions (type, product_id, batch_id, quantity, reference_number, notes)
         VALUES (?, ?, ?, ?, ?, ?)`,
        [data.type, data.product_id, data.batch_id, data.quantity, data.reference_number || null, data.notes || null]
      );

      // Update inventory based on transaction type
      const adjustment = data.type === TransactionType.INBOUND ? data.quantity : -data.quantity;

      const existing = await db.getFirstAsync(
        'SELECT * FROM inventory WHERE product_id = ? AND batch_id = ?',
        [data.product_id, data.batch_id]
      ) as Inventory | null;

      if (existing) {
        const newQuantity = Math.max(0, existing.quantity + adjustment);
        await db.runAsync(
          'UPDATE inventory SET quantity = ? WHERE id = ?',
          [newQuantity, existing.id]
        );
      } else if (adjustment > 0) {
        await db.runAsync(
          'INSERT INTO inventory (product_id, batch_id, quantity) VALUES (?, ?, ?)',
          [data.product_id, data.batch_id, adjustment]
        );
      }

      return transactionResult.lastInsertRowId!;
    });
  }
};

// Stock Alert Services
export const stockAlertService = {
  async getAll(): Promise<StockAlert[]> {
    return executeQuery<StockAlert>('SELECT * FROM stock_alerts WHERE is_active = 1 ORDER BY created_at DESC');
  },

  async getByProductId(productId: number): Promise<StockAlert[]> {
    return executeQuery<StockAlert>(
      'SELECT * FROM stock_alerts WHERE product_id = ? AND is_active = 1',
      [productId]
    );
  },

  async getActiveAlerts(): Promise<StockAlert[]> {
    const query = `
      SELECT sa.*, p.name as product_name, p.sku,
             COALESCE(SUM(i.quantity - i.reserved_quantity), 0) as current_stock
      FROM stock_alerts sa
      JOIN products p ON sa.product_id = p.id
      LEFT JOIN inventory i ON p.id = i.product_id
      WHERE sa.is_active = 1
      GROUP BY sa.id
      HAVING (sa.alert_type = 'LOW_STOCK' AND current_stock <= sa.threshold)
          OR (sa.alert_type = 'OVERSTOCK' AND current_stock >= sa.threshold)
      ORDER BY sa.created_at DESC
    `;

    return executeQuery<StockAlert>(query);
  },

  async create(productId: number, alertType: AlertType, threshold: number): Promise<number> {
    const result = await executeUpdate(
      'INSERT INTO stock_alerts (product_id, alert_type, threshold) VALUES (?, ?, ?)',
      [productId, alertType, threshold]
    );
    return result.lastInsertRowId!;
  },

  async update(id: number, threshold: number, isActive: boolean): Promise<void> {
    await executeUpdate(
      'UPDATE stock_alerts SET threshold = ?, is_active = ? WHERE id = ?',
      [threshold, isActive ? 1 : 0, id]
    );
  },

  async delete(id: number): Promise<void> {
    await executeUpdate('DELETE FROM stock_alerts WHERE id = ?', [id]);
  }
};

// Shipment Services
export const shipmentService = {
  async getAll(): Promise<Shipment[]> {
    return executeQuery<Shipment>('SELECT * FROM shipments ORDER BY date DESC');
  },

  async getById(id: number): Promise<Shipment | null> {
    return executeQueryFirst<Shipment>('SELECT * FROM shipments WHERE id = ?', [id]);
  },

  async getByReferenceNumber(referenceNumber: string): Promise<Shipment | null> {
    return executeQueryFirst<Shipment>(
      'SELECT * FROM shipments WHERE reference_number = ?',
      [referenceNumber]
    );
  },

  async create(data: CreateShipmentForm): Promise<number> {
    return executeTransaction(async (db) => {
      const shipmentResult = await db.runAsync(
        `INSERT INTO shipments (type, reference_number, status, date, notes)
         VALUES (?, ?, ?, ?, ?)`,
        [data.type, data.reference_number, 'PENDING', data.date, data.notes || null]
      );

      // Create transactions for each item in the shipment
      for (const item of data.items) {
        const transactionType = data.type === 'INCOMING' ? TransactionType.INBOUND : TransactionType.OUTBOUND;

        await db.runAsync(
          `INSERT INTO transactions (type, product_id, batch_id, quantity, reference_number, notes)
           VALUES (?, ?, ?, ?, ?, ?)`,
          [transactionType, item.product_id, item.batch_id, item.quantity, data.reference_number, `Shipment: ${data.reference_number}`]
        );

        // Update inventory
        const adjustment = transactionType === TransactionType.INBOUND ? item.quantity : -item.quantity;

        const existing = await db.getFirstAsync(
          'SELECT * FROM inventory WHERE product_id = ? AND batch_id = ?',
          [item.product_id, item.batch_id]
        ) as Inventory | null;

        if (existing) {
          const newQuantity = Math.max(0, existing.quantity + adjustment);
          await db.runAsync(
            'UPDATE inventory SET quantity = ? WHERE id = ?',
            [newQuantity, existing.id]
          );
        } else if (adjustment > 0) {
          await db.runAsync(
            'INSERT INTO inventory (product_id, batch_id, quantity) VALUES (?, ?, ?)',
            [item.product_id, item.batch_id, adjustment]
          );
        }
      }

      return shipmentResult.lastInsertRowId!;
    });
  },

  async updateStatus(id: number, status: string): Promise<void> {
    await executeUpdate(
      'UPDATE shipments SET status = ? WHERE id = ?',
      [status, id]
    );
  }
};

// Dashboard Services
export const dashboardService = {
  async getDashboardData(): Promise<InventoryDashboard> {
    const totalProducts = await executeQueryFirst<{ count: number }>(
      'SELECT COUNT(*) as count FROM products'
    );

    const lowStockProducts = await executeQueryFirst<{ count: number }>(
      `SELECT COUNT(DISTINCT p.id) as count
       FROM products p
       LEFT JOIN inventory i ON p.id = i.product_id
       GROUP BY p.id
       HAVING COALESCE(SUM(i.quantity - i.reserved_quantity), 0) <= p.min_stock_level`
    );

    const totalValue = await executeQueryFirst<{ value: number }>(
      `SELECT COALESCE(SUM(i.quantity * b.cost_per_unit), 0) as value
       FROM inventory i
       JOIN batches b ON i.batch_id = b.id`
    );

    const recentTransactions = await transactionService.getRecent(5);
    const activeAlerts = await stockAlertService.getActiveAlerts();

    return {
      total_products: totalProducts?.count || 0,
      low_stock_products: lowStockProducts?.count || 0,
      total_value: totalValue?.value || 0,
      recent_transactions: recentTransactions,
      active_alerts: activeAlerts
    };
  }
};
