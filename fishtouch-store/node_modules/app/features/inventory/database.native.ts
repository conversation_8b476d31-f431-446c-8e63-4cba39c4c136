// Native database implementation using expo-sqlite
// This file should only be imported on native platforms
let SQLite: any;

try {
  SQLite = require('expo-sqlite');
} catch (error) {
  console.warn('expo-sqlite not available, this file should only be used on native platforms');
  SQLite = null;
}

import { CREATE_TABLES_SQL } from './types';

// Database interface for cross-platform compatibility
interface DatabaseInterface {
  execAsync: (sql: string) => Promise<void>;
  getAllAsync: (sql: string, params?: any[]) => Promise<any[]>;
  getFirstAsync: (sql: string, params?: any[]) => Promise<any | null>;
  runAsync: (sql: string, params?: any[]) => Promise<{ lastInsertRowId?: number; changes: number }>;
  closeAsync: () => Promise<void>;
}

interface RunResult {
  lastInsertRowId?: number;
  changes: number;
}

let db: DatabaseInterface | null = null;

export const initializeDatabase = async (): Promise<DatabaseInterface> => {
  if (db) {
    return db;
  }

  try {
    const nativeDb = await SQLite.openDatabaseAsync('inventory.db');

    // Enable foreign keys
    await nativeDb.execAsync('PRAGMA foreign_keys = ON;');

    // Create tables
    await nativeDb.execAsync(CREATE_TABLES_SQL);

    // Create wrapper to match interface
    db = {
      execAsync: (sql: string) => nativeDb.execAsync(sql),
      getAllAsync: (sql: string, params: any[] = []) => nativeDb.getAllAsync(sql, params),
      getFirstAsync: (sql: string, params: any[] = []) => nativeDb.getFirstAsync(sql, params),
      runAsync: (sql: string, params: any[] = []) => nativeDb.runAsync(sql, params),
      closeAsync: () => nativeDb.closeAsync()
    };

    console.log('Native database initialized successfully');
    return db;
  } catch (error) {
    console.error('Failed to initialize native database:', error);
    throw error;
  }
};

export const getDatabase = async (): Promise<DatabaseInterface> => {
  if (!db) {
    return await initializeDatabase();
  }
  return db;
};

export const closeDatabase = async (): Promise<void> => {
  if (db) {
    await db.closeAsync();
    db = null;
  }
};

// Utility function to execute queries with error handling
export const executeQuery = async <T>(
  query: string,
  params: any[] = []
): Promise<T[]> => {
  try {
    const database = await getDatabase();
    const result = await database.getAllAsync(query, params);
    return result as T[];
  } catch (error) {
    console.error('Query execution failed:', error);
    console.error('Query:', query);
    console.error('Params:', params);
    throw error;
  }
};

// Utility function to execute single row queries
export const executeQueryFirst = async <T>(
  query: string,
  params: any[] = []
): Promise<T | null> => {
  try {
    const database = await getDatabase();
    const result = await database.getFirstAsync(query, params);
    return result as T | null;
  } catch (error) {
    console.error('Query execution failed:', error);
    console.error('Query:', query);
    console.error('Params:', params);
    throw error;
  }
};

// Utility function to execute insert/update/delete queries
export const executeUpdate = async (
  query: string,
  params: any[] = []
): Promise<RunResult> => {
  try {
    const database = await getDatabase();
    const result = await database.runAsync(query, params);
    return result;
  } catch (error) {
    console.error('Update execution failed:', error);
    console.error('Query:', query);
    console.error('Params:', params);
    throw error;
  }
};

// Transaction wrapper for multiple operations
export const executeTransaction = async <T>(
  operations: (db: DatabaseInterface) => Promise<T>
): Promise<T> => {
  const database = await getDatabase();

  try {
    const result = await operations(database);
    return result;
  } catch (error) {
    console.error('Transaction failed:', error);
    throw error;
  }
};

// Database seeding function for development/testing
export const seedDatabase = async (): Promise<void> => {
  try {
    console.log('Seeding native database with sample data...');

    // Clear existing data first
    await executeUpdate('DELETE FROM transactions');
    await executeUpdate('DELETE FROM inventory');
    await executeUpdate('DELETE FROM stock_alerts');
    await executeUpdate('DELETE FROM package_products');
    await executeUpdate('DELETE FROM packages');
    await executeUpdate('DELETE FROM batches');
    await executeUpdate('DELETE FROM shipments');
    await executeUpdate('DELETE FROM products');

    // Insert sample products
    const productIds = [];
    const products = [
      { name: 'Fish Food Premium', sku: 'FF001', description: 'High-quality fish food for tropical fish', unit: 'kg', min_stock_level: 10 },
      { name: 'Aquarium Filter', sku: 'AF001', description: 'Advanced filtration system', unit: 'piece', min_stock_level: 5 },
      { name: 'Water Conditioner', sku: 'WC001', description: 'Removes chlorine and heavy metals', unit: 'liter', min_stock_level: 20 },
      { name: 'LED Light Strip', sku: 'LS001', description: 'Energy-efficient aquarium lighting', unit: 'piece', min_stock_level: 3 },
      { name: 'Gravel Substrate', sku: 'GS001', description: 'Natural aquarium gravel', unit: 'kg', min_stock_level: 50 }
    ];

    for (const product of products) {
      const result = await executeUpdate(
        'INSERT INTO products (name, sku, description, unit, min_stock_level) VALUES (?, ?, ?, ?, ?)',
        [product.name, product.sku, product.description, product.unit, product.min_stock_level]
      );
      productIds.push(result.lastInsertRowId);
    }

    // Insert sample inventory records
    for (let i = 0; i < productIds.length; i++) {
      const productId = productIds[i];
      const stock = Math.floor(Math.random() * 100) + 10;
      const cost = Math.random() * 50 + 10;

      await executeUpdate(
        'INSERT INTO inventory (product_id, available_stock, reserved_stock, cost_per_unit) VALUES (?, ?, ?, ?)',
        [productId, stock, 0, cost.toFixed(2)]
      );
    }

    // Insert sample transactions
    for (let i = 0; i < 10; i++) {
      const productId = productIds[Math.floor(Math.random() * productIds.length)];
      const type = Math.random() > 0.5 ? 'INBOUND' : 'OUTBOUND';
      const quantity = Math.floor(Math.random() * 20) + 1;
      const unitPrice = Math.random() * 30 + 5;

      await executeUpdate(
        'INSERT INTO transactions (product_id, type, quantity, unit_price, reference_number) VALUES (?, ?, ?, ?, ?)',
        [productId, type, quantity, unitPrice.toFixed(2), `REF${Date.now()}-${i}`]
      );
    }

    console.log('Native database seeded successfully');
  } catch (error) {
    console.error('Failed to seed native database:', error);
    throw error;
  }
};

// Clear all data (for testing purposes)
export const clearDatabase = async (): Promise<void> => {
  try {
    await executeUpdate('DELETE FROM transactions');
    await executeUpdate('DELETE FROM inventory');
    await executeUpdate('DELETE FROM stock_alerts');
    await executeUpdate('DELETE FROM package_products');
    await executeUpdate('DELETE FROM packages');
    await executeUpdate('DELETE FROM batches');
    await executeUpdate('DELETE FROM shipments');
    await executeUpdate('DELETE FROM products');
    console.log('Native database cleared successfully');
  } catch (error) {
    console.error('Failed to clear native database:', error);
    throw error;
  }
};
