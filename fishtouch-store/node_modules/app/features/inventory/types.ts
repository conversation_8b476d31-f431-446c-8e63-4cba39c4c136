// Database Models and Types for Inventory Management System

export interface Product {
  id: number;
  name: string;
  sku: string;
  description?: string;
  unit: string; // e.g., 'piece', 'kg', 'liter'
  min_stock_level: number;
  created_at: string;
  updated_at: string;
}

export interface Package {
  id: number;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface PackageItem {
  id: number;
  package_id: number;
  product_id: number;
  quantity: number;
  created_at: string;
}

export interface Batch {
  id: number;
  product_id: number;
  batch_number: string;
  supplier?: string;
  cost_per_unit: number;
  expiry_date?: string;
  created_at: string;
}

export interface Inventory {
  id: number;
  product_id: number;
  batch_id: number;
  quantity: number;
  reserved_quantity: number;
  location?: string;
}

export interface Transaction {
  id: number;
  type: TransactionType;
  product_id: number;
  batch_id: number;
  quantity: number;
  reference_number?: string; // shipment/order number
  notes?: string;
  created_at: string;
}

export interface StockAlert {
  id: number;
  product_id: number;
  alert_type: AlertType;
  threshold: number;
  is_active: boolean;
  created_at: string;
}

export interface Shipment {
  id: number;
  type: ShipmentType;
  reference_number: string;
  status: ShipmentStatus;
  date: string;
  notes?: string;
  created_at: string;
}

// Enums
export enum TransactionType {
  INBOUND = 'INBOUND',
  OUTBOUND = 'OUTBOUND',
  ADJUSTMENT = 'ADJUSTMENT',
  TRANSFER = 'TRANSFER'
}

export enum AlertType {
  LOW_STOCK = 'LOW_STOCK',
  EXPIRY_WARNING = 'EXPIRY_WARNING',
  OVERSTOCK = 'OVERSTOCK'
}

export enum ShipmentType {
  INCOMING = 'INCOMING',
  OUTGOING = 'OUTGOING'
}

export enum ShipmentStatus {
  PENDING = 'PENDING',
  IN_TRANSIT = 'IN_TRANSIT',
  DELIVERED = 'DELIVERED',
  CANCELLED = 'CANCELLED'
}

// Extended types for UI and business logic
export interface ProductWithStock extends Product {
  total_stock: number;
  available_stock: number;
  batches: BatchWithInventory[];
  alerts: StockAlert[];
}

export interface BatchWithInventory extends Batch {
  inventory: Inventory[];
  total_quantity: number;
  available_quantity: number;
}

export interface PackageWithItems extends Package {
  items: (PackageItem & { product: Product })[];
  total_value: number;
}

export interface TransactionWithDetails extends Transaction {
  product: Product;
  batch: Batch;
}

export interface InventoryDashboard {
  total_products: number;
  low_stock_products: number;
  total_value: number;
  recent_transactions: TransactionWithDetails[];
  active_alerts: StockAlert[];
}

// Stock prediction types
export interface StockPrediction {
  product_id: number;
  predicted_stock_out_date?: string;
  recommended_reorder_quantity: number;
  confidence_level: number;
}

export interface StockMovement {
  date: string;
  inbound: number;
  outbound: number;
  net_change: number;
}

// Form types for creating/updating entities
export interface CreateProductForm {
  name: string;
  sku: string;
  description?: string;
  unit: string;
  min_stock_level: number;
}

export interface CreatePackageForm {
  name: string;
  description?: string;
  items: {
    product_id: number;
    quantity: number;
  }[];
}

export interface CreateBatchForm {
  product_id: number;
  batch_number: string;
  supplier?: string;
  cost_per_unit: number;
  expiry_date?: string;
  initial_quantity: number;
  location?: string;
}

export interface CreateTransactionForm {
  type: TransactionType;
  product_id: number;
  batch_id: number;
  quantity: number;
  reference_number?: string;
  notes?: string;
}

export interface CreateShipmentForm {
  type: ShipmentType;
  reference_number: string;
  date: string;
  notes?: string;
  items: {
    product_id: number;
    batch_id: number;
    quantity: number;
  }[];
}

// Database table creation SQL
export const CREATE_TABLES_SQL = `
  CREATE TABLE IF NOT EXISTS products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    sku TEXT UNIQUE NOT NULL,
    description TEXT,
    unit TEXT NOT NULL,
    min_stock_level INTEGER NOT NULL DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  );

  CREATE TABLE IF NOT EXISTS packages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  );

  CREATE TABLE IF NOT EXISTS package_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    package_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (package_id) REFERENCES packages (id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
  );

  CREATE TABLE IF NOT EXISTS batches (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id INTEGER NOT NULL,
    batch_number TEXT NOT NULL,
    supplier TEXT,
    cost_per_unit REAL NOT NULL,
    expiry_date DATE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE,
    UNIQUE(product_id, batch_number)
  );

  CREATE TABLE IF NOT EXISTS inventory (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id INTEGER NOT NULL,
    batch_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 0,
    reserved_quantity INTEGER NOT NULL DEFAULT 0,
    location TEXT,
    FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE,
    FOREIGN KEY (batch_id) REFERENCES batches (id) ON DELETE CASCADE,
    UNIQUE(product_id, batch_id, location)
  );

  CREATE TABLE IF NOT EXISTS transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    type TEXT NOT NULL CHECK (type IN ('INBOUND', 'OUTBOUND', 'ADJUSTMENT', 'TRANSFER')),
    product_id INTEGER NOT NULL,
    batch_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL,
    reference_number TEXT,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE,
    FOREIGN KEY (batch_id) REFERENCES batches (id) ON DELETE CASCADE
  );

  CREATE TABLE IF NOT EXISTS stock_alerts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id INTEGER NOT NULL,
    alert_type TEXT NOT NULL CHECK (alert_type IN ('LOW_STOCK', 'EXPIRY_WARNING', 'OVERSTOCK')),
    threshold INTEGER NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
  );

  CREATE TABLE IF NOT EXISTS shipments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    type TEXT NOT NULL CHECK (type IN ('INCOMING', 'OUTGOING')),
    reference_number TEXT UNIQUE NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('PENDING', 'IN_TRANSIT', 'DELIVERED', 'CANCELLED')),
    date DATE NOT NULL,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  );

  -- Indexes for better performance
  CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku);
  CREATE INDEX IF NOT EXISTS idx_batches_product_id ON batches(product_id);
  CREATE INDEX IF NOT EXISTS idx_inventory_product_id ON inventory(product_id);
  CREATE INDEX IF NOT EXISTS idx_inventory_batch_id ON inventory(batch_id);
  CREATE INDEX IF NOT EXISTS idx_transactions_product_id ON transactions(product_id);
  CREATE INDEX IF NOT EXISTS idx_transactions_batch_id ON transactions(batch_id);
  CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);
  CREATE INDEX IF NOT EXISTS idx_stock_alerts_product_id ON stock_alerts(product_id);
  CREATE INDEX IF NOT EXISTS idx_shipments_reference_number ON shipments(reference_number);

  -- Triggers for updating timestamps
  CREATE TRIGGER IF NOT EXISTS update_products_timestamp 
    AFTER UPDATE ON products
    BEGIN
      UPDATE products SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

  CREATE TRIGGER IF NOT EXISTS update_packages_timestamp 
    AFTER UPDATE ON packages
    BEGIN
      UPDATE packages SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
`;
