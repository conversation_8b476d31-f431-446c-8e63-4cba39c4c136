{"version": 3, "sources": ["/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/ui/src/CustomToast.tsx"], "mappings": ";AAAA,OAAOA,aAAaC,4BAA4B;AAChD,SAASC,eAAeC,aAAa;AAErC,IAAMC,SAASJ,UAAUK,yBAAyBJ,qBAAqBK,aAE1DC,cAAc,WAAA;AACzB,SAAIH,SACK,OAEF,qBAACD,OAAAA,CAAAA,CAAAA;AACV;", "names": ["Constants", "ExecutionEnvironment", "NativeToast", "Toast", "isExpo", "executionEnvironment", "StoreClient", "CustomToast"]}