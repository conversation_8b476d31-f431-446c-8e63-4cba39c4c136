import { jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from "react";
import { Button, useIsomorphicLayoutEffect } from "tamagui";
import { useThemeSetting, useRootTheme } from "@tamagui/next-theme";
var SwitchThemeButton = function() {
  var themeSetting = useThemeSetting(), [theme] = useRootTheme(), [clientTheme, setClientTheme] = useState("light");
  return useIsomorphicLayoutEffect(function() {
    setClientTheme(themeSetting.forcedTheme || themeSetting.current || theme);
  }, [
    themeSetting.current,
    themeSetting.resolvedTheme
  ]), /* @__PURE__ */ _jsxs(Button, {
    onPress: themeSetting.toggle,
    children: [
      "Change theme: ",
      clientTheme
    ]
  });
};
export {
  SwitchThemeButton
};
//# sourceMappingURL=SwitchThemeButton.js.map
