var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: !0 });
}, __copyProps = (to, from, except, desc) => {
  if (from && typeof from == "object" || typeof from == "function")
    for (let key of __getOwnPropNames(from))
      !__hasOwnProp.call(to, key) && key !== except && __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: !0 }), mod);
var SwitchThemeButton_exports = {};
__export(SwitchThemeButton_exports, {
  SwitchThemeButton: () => SwitchThemeButton
});
module.exports = __toCommonJS(SwitchThemeButton_exports);
var import_jsx_runtime = require("react/jsx-runtime"), import_react = require("react"), import_tamagui = require("tamagui"), import_next_theme = require("@tamagui/next-theme"), SwitchThemeButton = function() {
  var themeSetting = (0, import_next_theme.useThemeSetting)(), [theme] = (0, import_next_theme.useRootTheme)(), [clientTheme, setClientTheme] = (0, import_react.useState)("light");
  return (0, import_tamagui.useIsomorphicLayoutEffect)(function() {
    setClientTheme(themeSetting.forcedTheme || themeSetting.current || theme);
  }, [
    themeSetting.current,
    themeSetting.resolvedTheme
  ]), /* @__PURE__ */ (0, import_jsx_runtime.jsxs)(import_tamagui.Button, {
    onPress: themeSetting.toggle,
    children: [
      "Change theme: ",
      clientTheme
    ]
  });
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  SwitchThemeButton
});
//# sourceMappingURL=SwitchThemeButton.js.map
