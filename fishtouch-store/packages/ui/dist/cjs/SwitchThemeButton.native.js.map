{"version": 3, "sources": ["/Users/<USER>/Developer/FullStackProjects/FishTouchStore/fishtouch-store/packages/ui/src/SwitchThemeButton.tsx"], "mappings": ";;;;;;;;;;;;;;;;;;;uDAAA,eAAyB,kBACzB,iBAAkD,oBAClD,oBAA8C,gCAEjCA,oBAAoB,WAAA;AAC/B,MAAMC,mBAAeC,mCAAAA,GACf,CAACC,KAAAA,QAASC,gCAAAA,GAEV,CAACC,aAAaC,cAAAA,QAAkBC,uBAA6B,OAAA;AAEnEC,uDAA0B,WAAA;AACxBF,mBAAeL,aAAaQ,eAAeR,aAAaS,WAAWP,KAAAA;EACrE,GAAG;IAACF,aAAaS;IAAST,aAAaU;GAAc,GAE9C,uCAAAC,MAACC,uBAAAA;IAAOC,SAASb,aAAac;;MAAQ;MAAeV;;;AAC9D;", "names": ["SwitchThemeButton", "themeSetting", "useThemeSetting", "theme", "useRootTheme", "clientTheme", "setClientTheme", "useState", "useIsomorphicLayoutEffect", "forcedTheme", "current", "resolvedTheme", "_jsxs", "<PERSON><PERSON>", "onPress", "toggle"]}