var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
    for (var name in all) __defProp(target, name, {
      get: all[name],
      enumerable: !0
    });
  },
  __copyProps = (to, from, except, desc) => {
    if (from && typeof from == "object" || typeof from == "function") for (let key of __getOwnPropNames(from)) !__hasOwnProp.call(to, key) && key !== except && __defProp(to, key, {
      get: () => from[key],
      enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
    });
    return to;
  },
  __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
var __toCommonJS = mod => __copyProps(__defProp({}, "__esModule", {
  value: !0
}), mod);
var index_exports = {};
__export(index_exports, {
  config: () => import_config.config
});
module.exports = __toCommonJS(index_exports);
__reExport(index_exports, require("tamagui"), module.exports);
__reExport(index_exports, require("@tamagui/toast"), module.exports);
__reExport(index_exports, require("./MyComponent.cjs"), module.exports);
var import_config = require("@my/config");
__reExport(index_exports, require("./CustomToast.cjs"), module.exports);
__reExport(index_exports, require("./SwitchThemeButton.cjs"), module.exports);
__reExport(index_exports, require("./SwitchRouterButton.cjs"), module.exports);