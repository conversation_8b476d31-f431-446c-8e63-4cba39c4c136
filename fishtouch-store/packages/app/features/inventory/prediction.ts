import { StockPrediction, StockMovement, TransactionType } from './types';
import { executeQuery, executeQueryFirst } from './database';

// Stock prediction and analytics service
export const predictionService = {
  /**
   * Calculate stock prediction based on historical consumption patterns
   */
  async getStockPrediction(productId: number, daysToPredict: number = 30): Promise<StockPrediction> {
    // Get current stock level
    const currentStock = await executeQueryFirst<{ total_stock: number }>(
      `SELECT COALESCE(SUM(quantity - reserved_quantity), 0) as total_stock 
       FROM inventory WHERE product_id = ?`,
      [productId]
    );

    // Get historical outbound transactions for the last 90 days
    const historicalData = await executeQuery<{ date: string; quantity: number }>(
      `SELECT DATE(created_at) as date, SUM(quantity) as quantity
       FROM transactions 
       WHERE product_id = ? AND type = 'OUTBOUND' 
         AND created_at >= date('now', '-90 days')
       GROUP BY DATE(created_at)
       ORDER BY date`,
      [productId]
    );

    if (historicalData.length === 0) {
      return {
        product_id: productId,
        predicted_stock_out_date: undefined,
        recommended_reorder_quantity: 0,
        confidence_level: 0
      };
    }

    // Calculate daily consumption rate
    const totalConsumption = historicalData.reduce((sum, day) => sum + day.quantity, 0);
    const averageDailyConsumption = totalConsumption / historicalData.length;

    // Calculate trend (linear regression slope)
    const trend = this.calculateTrend(historicalData);
    
    // Adjust consumption rate based on trend
    const adjustedDailyConsumption = Math.max(0, averageDailyConsumption + (trend * daysToPredict / 2));

    // Calculate standard deviation for confidence level
    const variance = historicalData.reduce((sum, day) => {
      const diff = day.quantity - averageDailyConsumption;
      return sum + (diff * diff);
    }, 0) / historicalData.length;
    const standardDeviation = Math.sqrt(variance);

    // Confidence level based on data consistency (lower std dev = higher confidence)
    const confidenceLevel = Math.max(0, Math.min(1, 1 - (standardDeviation / averageDailyConsumption)));

    // Predict stock out date
    const currentStockLevel = currentStock?.total_stock || 0;
    let predictedStockOutDate: string | undefined;
    
    if (adjustedDailyConsumption > 0) {
      const daysUntilStockOut = currentStockLevel / adjustedDailyConsumption;
      if (daysUntilStockOut > 0) {
        const stockOutDate = new Date();
        stockOutDate.setDate(stockOutDate.getDate() + Math.floor(daysUntilStockOut));
        predictedStockOutDate = stockOutDate.toISOString().split('T')[0];
      }
    }

    // Calculate recommended reorder quantity
    // Safety stock = 1.5 * average daily consumption * lead time (assume 7 days)
    const leadTimeDays = 7;
    const safetyStock = 1.5 * adjustedDailyConsumption * leadTimeDays;
    const reorderQuantity = Math.ceil(adjustedDailyConsumption * daysToPredict + safetyStock);

    return {
      product_id: productId,
      predicted_stock_out_date: predictedStockOutDate,
      recommended_reorder_quantity: reorderQuantity,
      confidence_level: Math.round(confidenceLevel * 100) / 100
    };
  },

  /**
   * Calculate trend using simple linear regression
   */
  calculateTrend(data: { date: string; quantity: number }[]): number {
    if (data.length < 2) return 0;

    const n = data.length;
    let sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;

    data.forEach((point, index) => {
      const x = index; // Use index as x value
      const y = point.quantity;
      sumX += x;
      sumY += y;
      sumXY += x * y;
      sumXX += x * x;
    });

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    return isNaN(slope) ? 0 : slope;
  },

  /**
   * Get stock movement analysis for a product
   */
  async getStockMovement(productId: number, days: number = 30): Promise<StockMovement[]> {
    const query = `
      SELECT 
        DATE(created_at) as date,
        SUM(CASE WHEN type = 'INBOUND' THEN quantity ELSE 0 END) as inbound,
        SUM(CASE WHEN type = 'OUTBOUND' THEN quantity ELSE 0 END) as outbound,
        SUM(CASE WHEN type = 'INBOUND' THEN quantity ELSE -quantity END) as net_change
      FROM transactions 
      WHERE product_id = ? AND created_at >= date('now', '-${days} days')
      GROUP BY DATE(created_at)
      ORDER BY date
    `;

    return executeQuery<StockMovement>(query, [productId]);
  },

  /**
   * Get products that need reordering based on predictions
   */
  async getProductsNeedingReorder(daysAhead: number = 30): Promise<(StockPrediction & { product_name: string; sku: string })[]> {
    const products = await executeQuery<{ id: number; name: string; sku: string }>(
      'SELECT id, name, sku FROM products ORDER BY name'
    );

    const predictions = [];
    
    for (const product of products) {
      const prediction = await this.getStockPrediction(product.id, daysAhead);
      
      // Include products that will stock out within the prediction period
      if (prediction.predicted_stock_out_date) {
        const stockOutDate = new Date(prediction.predicted_stock_out_date);
        const today = new Date();
        const daysUntilStockOut = Math.ceil((stockOutDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        
        if (daysUntilStockOut <= daysAhead) {
          predictions.push({
            ...prediction,
            product_name: product.name,
            sku: product.sku
          });
        }
      }
    }

    return predictions.sort((a, b) => {
      if (!a.predicted_stock_out_date) return 1;
      if (!b.predicted_stock_out_date) return -1;
      return a.predicted_stock_out_date.localeCompare(b.predicted_stock_out_date);
    });
  },

  /**
   * Get inventory turnover rate for products
   */
  async getInventoryTurnover(productId?: number, days: number = 90): Promise<{ product_id: number; product_name: string; turnover_rate: number; days_of_supply: number }[]> {
    const whereClause = productId ? 'WHERE p.id = ?' : '';
    const params = productId ? [productId, days] : [days];

    const query = `
      SELECT 
        p.id as product_id,
        p.name as product_name,
        COALESCE(SUM(CASE WHEN t.type = 'OUTBOUND' THEN t.quantity ELSE 0 END), 0) as total_sold,
        COALESCE(AVG(i.quantity), 0) as avg_inventory,
        CASE 
          WHEN AVG(i.quantity) > 0 THEN 
            (SUM(CASE WHEN t.type = 'OUTBOUND' THEN t.quantity ELSE 0 END) * 365.0 / ?) / AVG(i.quantity)
          ELSE 0 
        END as turnover_rate,
        CASE 
          WHEN SUM(CASE WHEN t.type = 'OUTBOUND' THEN t.quantity ELSE 0 END) > 0 THEN 
            (AVG(i.quantity) * ?) / SUM(CASE WHEN t.type = 'OUTBOUND' THEN t.quantity ELSE 0 END)
          ELSE 999 
        END as days_of_supply
      FROM products p
      LEFT JOIN transactions t ON p.id = t.product_id AND t.created_at >= date('now', '-${days} days')
      LEFT JOIN inventory i ON p.id = i.product_id
      ${whereClause}
      GROUP BY p.id, p.name
      ORDER BY turnover_rate DESC
    `;

    return executeQuery(query, params);
  },

  /**
   * Generate automated stock alerts based on predictions
   */
  async generatePredictiveAlerts(): Promise<{ product_id: number; message: string; urgency: 'low' | 'medium' | 'high' }[]> {
    const alerts = [];
    const products = await executeQuery<{ id: number; name: string; min_stock_level: number }>(
      'SELECT id, name, min_stock_level FROM products'
    );

    for (const product of products) {
      const prediction = await this.getStockPrediction(product.id);
      
      if (prediction.predicted_stock_out_date) {
        const stockOutDate = new Date(prediction.predicted_stock_out_date);
        const today = new Date();
        const daysUntilStockOut = Math.ceil((stockOutDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        
        let urgency: 'low' | 'medium' | 'high' = 'low';
        let message = '';
        
        if (daysUntilStockOut <= 3) {
          urgency = 'high';
          message = `Critical: ${product.name} will stock out in ${daysUntilStockOut} days`;
        } else if (daysUntilStockOut <= 7) {
          urgency = 'high';
          message = `Urgent: ${product.name} will stock out in ${daysUntilStockOut} days`;
        } else if (daysUntilStockOut <= 14) {
          urgency = 'medium';
          message = `Warning: ${product.name} will stock out in ${daysUntilStockOut} days`;
        } else if (daysUntilStockOut <= 30) {
          urgency = 'low';
          message = `Notice: ${product.name} will stock out in ${daysUntilStockOut} days`;
        }
        
        if (message) {
          alerts.push({
            product_id: product.id,
            message,
            urgency
          });
        }
      }
    }

    return alerts.sort((a, b) => {
      const urgencyOrder = { high: 3, medium: 2, low: 1 };
      return urgencyOrder[b.urgency] - urgencyOrder[a.urgency];
    });
  }
};
