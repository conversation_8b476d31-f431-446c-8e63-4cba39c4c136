import React, { useState, useEffect } from 'react';
import {
  YStack,
  XStack,
  Text,
  Card,
  Button,
  ScrollView,
  Separator,
  H2,
  H3,
  H4,
  Paragraph,
  Sheet,
  Input,
  Label,
  Select,
  Adapt,
  AlertDialog,
  Spinner,
  TextArea
} from '@my/ui';
import { RefreshCw, Package, AlertTriangle, TrendingUp, Plus, Search } from '@tamagui/lucide-icons';
import { dashboardService, productService, transactionService, stockAlertService } from './services';
import { initializeDatabase, seedDatabase } from './database';
import { predictionService } from './prediction';
import { InventoryDashboard, ProductWithStock, TransactionWithDetails, StockAlert, CreateProductForm } from './types';

export function InventoryScreen() {
  const [dashboardData, setDashboardData] = useState<InventoryDashboard | null>(null);
  const [products, setProducts] = useState<ProductWithStock[]>([]);
  const [recentTransactions, setRecentTransactions] = useState<TransactionWithDetails[]>([]);
  const [activeAlerts, setActiveAlerts] = useState<StockAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showProductSheet, setShowProductSheet] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [productForm, setProductForm] = useState<CreateProductForm>({
    name: '',
    sku: '',
    description: '',
    unit: 'piece',
    min_stock_level: 0
  });
  const [formErrors, setFormErrors] = useState<Partial<CreateProductForm>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      setLoading(true);
      await initializeDatabase();

      // Check if database has data, if not seed it
      const existingProducts = await productService.getAll();
      if (existingProducts.length === 0) {
        await seedDatabase();
      }

      await loadDashboardData();
    } catch (error) {
      console.error('Failed to initialize app:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadDashboardData = async () => {
    try {
      const [dashboard, productsData, transactions, alerts] = await Promise.all([
        dashboardService.getDashboardData(),
        productService.getWithStock(),
        transactionService.getRecent(10),
        stockAlertService.getActiveAlerts()
      ]);

      setDashboardData(dashboard);
      setProducts(productsData);
      setRecentTransactions(transactions);
      setActiveAlerts(alerts);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const validateProductForm = (): boolean => {
    const errors: Partial<CreateProductForm> = {};

    if (!productForm.name.trim()) {
      errors.name = 'Product name is required';
    }

    if (!productForm.sku.trim()) {
      errors.sku = 'SKU is required';
    }

    if (!productForm.unit.trim()) {
      errors.unit = 'Unit is required';
    }

    if (productForm.min_stock_level < 0) {
      errors.min_stock_level = 'Minimum stock level must be 0 or greater';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleCreateProduct = async () => {
    if (!validateProductForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await productService.create(productForm);

      // Reset form
      setProductForm({
        name: '',
        sku: '',
        description: '',
        unit: 'piece',
        min_stock_level: 0
      });
      setFormErrors({});
      setShowProductSheet(false);

      // Refresh data
      await loadDashboardData();
    } catch (error) {
      console.error('Failed to create product:', error);
      // You could add a toast notification here
    } finally {
      setIsSubmitting(false);
    }
  };

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.sku.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <YStack flex={1} jc="center" ai="center" p="$4">
        <Spinner size="large" />
        <Text mt="$4">Initializing Inventory System...</Text>
      </YStack>
    );
  }

  return (
    <ScrollView flex={1} background="$background">
      <YStack paddingHorizontal="$4" paddingVertical="$4" space="$4">
        {/* Header */}
        <XStack jc="space-between" ai="center">
          <H2>Inventory Management</H2>
          <Button
            icon={RefreshCw}
            onPress={handleRefresh}
            disabled={refreshing}
            variant="outlined"
            size="$3"
          >
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
        </XStack>

        {/* Dashboard Cards */}
        {dashboardData && (
          <XStack space="$3" flexWrap="wrap">
            <Card flex={1} minWidth={150} padding="$3">
              <YStack space="$2">
                <XStack ai="center" space="$2">
                  <Package size={20} color="$blue10" />
                  <Text fontSize="$3" color="$color11">Total Products</Text>
                </XStack>
                <Text fontSize="$6" fontWeight="bold">{dashboardData.total_products}</Text>
              </YStack>
            </Card>

            <Card flex={1} minWidth={150} padding="$3">
              <YStack space="$2">
                <XStack ai="center" space="$2">
                  <AlertTriangle size={20} color="$red10" />
                  <Text fontSize="$3" color="$color11">Low Stock</Text>
                </XStack>
                <Text fontSize="$6" fontWeight="bold" color="$red10">
                  {dashboardData.low_stock_products}
                </Text>
              </YStack>
            </Card>

            <Card flex={1} minWidth={150} padding="$3">
              <YStack space="$2">
                <XStack ai="center" space="$2">
                  <TrendingUp size={20} color="$green10" />
                  <Text fontSize="$3" color="$color11">Total Value</Text>
                </XStack>
                <Text fontSize="$6" fontWeight="bold" color="$green10">
                  ${dashboardData.total_value.toFixed(2)}
                </Text>
              </YStack>
            </Card>
          </XStack>
        )}

        {/* Active Alerts */}
        {activeAlerts.length > 0 && (
          <Card padding="$4">
            <H3 mb="$3">Active Alerts</H3>
            <YStack space="$2">
              {activeAlerts.slice(0, 3).map((alert) => (
                <XStack key={alert.id} ai="center" space="$3" p="$2" bg="$red2" br="$3">
                  <AlertTriangle size={16} color="$red10" />
                  <YStack flex={1}>
                    <Text fontSize="$3" fontWeight="600">Low Stock Alert</Text>
                    <Text fontSize="$2" color="$color11">
                      Product ID: {alert.product_id} - Threshold: {alert.threshold}
                    </Text>
                  </YStack>
                  <Text
                    bg="$red5"
                    color="$red11"
                    px="$2"
                    py="$1"
                    br="$2"
                    fontSize="$1"
                  >
                    {alert.alert_type}
                  </Text>
                </XStack>
              ))}
              {activeAlerts.length > 3 && (
                <Text fontSize="$2" color="$color11" ta="center">
                  +{activeAlerts.length - 3} more alerts
                </Text>
              )}
            </YStack>
          </Card>
        )}

        {/* Products Section */}
        <Card padding="$4">
          <XStack jc="space-between" ai="center" mb="$3">
            <H3>Products</H3>
            <Button
              icon={Plus}
              onPress={() => setShowProductSheet(true)}
              size="$3"
            >
              Add Product
            </Button>
          </XStack>

          {/* Search */}
          <XStack space="$2" mb="$3">
            <Input
              flex={1}
              placeholder="Search products..."
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            <Button icon={Search} variant="outlined" />
          </XStack>

          <YStack space="$2">
            {filteredProducts.slice(0, 5).map((product) => (
              <Card key={product.id} p="$3" bg="$color2">
                <XStack jc="space-between" ai="center">
                  <YStack flex={1}>
                    <Text fontSize="$4" fontWeight="600">{product.name}</Text>
                    <Text fontSize="$3" color="$color11">SKU: {product.sku}</Text>
                    <Text fontSize="$2" color="$color10">
                      Stock: {product.available_stock} {product.unit}
                    </Text>
                  </YStack>
                  <YStack ai="flex-end" space="$1">
                    <Text
                      bg={product.available_stock <= product.min_stock_level ? "$red5" : "$green5"}
                      color={product.available_stock <= product.min_stock_level ? "$red11" : "$green11"}
                      px="$2"
                      py="$1"
                      br="$2"
                      fontSize="$1"
                    >
                      {product.available_stock <= product.min_stock_level ? 'Low Stock' : 'In Stock'}
                    </Text>
                    <Text fontSize="$2" color="$color11">
                      Min: {product.min_stock_level}
                    </Text>
                  </YStack>
                </XStack>
              </Card>
            ))}
            {filteredProducts.length > 5 && (
              <Text fontSize="$2" color="$color11" ta="center">
                +{filteredProducts.length - 5} more products
              </Text>
            )}
          </YStack>
        </Card>

        {/* Recent Transactions */}
        <Card padding="$4">
          <H3 mb="$3">Recent Transactions</H3>
          <YStack space="$2">
            {recentTransactions.slice(0, 5).map((transaction) => (
              <XStack key={transaction.id} jc="space-between" ai="center" p="$2">
                <YStack flex={1}>
                  <Text fontSize="$3" fontWeight="600">{transaction.product.name}</Text>
                  <Text fontSize="$2" color="$color11">
                    {transaction.type} - {transaction.quantity} {transaction.product.unit}
                  </Text>
                  <Text fontSize="$1" color="$color10">
                    {new Date(transaction.created_at).toLocaleDateString()}
                  </Text>
                </YStack>
                <Text
                  bg={transaction.type === 'INBOUND' ? "$green5" : "$red5"}
                  color={transaction.type === 'INBOUND' ? "$green11" : "$red11"}
                  px="$2"
                  py="$1"
                  br="$2"
                  fontSize="$1"
                >
                  {transaction.type}
                </Text>
              </XStack>
            ))}
          </YStack>
        </Card>
      </YStack>

      {/* Add Product Sheet */}
      <Sheet
        modal
        open={showProductSheet}
        onOpenChange={setShowProductSheet}
        snapPoints={[85]}
        dismissOnSnapToBottom
      >
        <Sheet.Overlay />
        <Sheet.Handle />
        <Sheet.Frame p="$4">
          <ScrollView>
            <YStack space="$4">
              <H3>Add New Product</H3>

              {/* Product Name */}
              <YStack space="$2">
                <Label htmlFor="product-name">Product Name *</Label>
                <Input
                  id="product-name"
                  placeholder="Enter product name"
                  value={productForm.name}
                  onChangeText={(text) => setProductForm(prev => ({ ...prev, name: text }))}
                  borderColor={formErrors.name ? '$red10' : '$borderColor'}
                />
                {formErrors.name && <Text color="$red10" fontSize="$2">{formErrors.name}</Text>}
              </YStack>

              {/* SKU */}
              <YStack space="$2">
                <Label htmlFor="product-sku">SKU *</Label>
                <Input
                  id="product-sku"
                  placeholder="Enter SKU (e.g., FF001)"
                  value={productForm.sku}
                  onChangeText={(text) => setProductForm(prev => ({ ...prev, sku: text }))}
                  borderColor={formErrors.sku ? '$red10' : '$borderColor'}
                />
                {formErrors.sku && <Text color="$red10" fontSize="$2">{formErrors.sku}</Text>}
              </YStack>

              {/* Unit */}
              <YStack space="$2">
                <Label htmlFor="product-unit">Unit *</Label>
                <Select
                  value={productForm.unit}
                  onValueChange={(value) => setProductForm(prev => ({ ...prev, unit: value }))}
                >
                  <Select.Trigger>
                    <Select.Value placeholder="Select unit" />
                  </Select.Trigger>
                  <Adapt when="sm" platform="touch">
                    <Sheet modal dismissOnSnapToBottom>
                      <Sheet.Frame>
                        <Sheet.ScrollView>
                          <Adapt.Contents />
                        </Sheet.ScrollView>
                      </Sheet.Frame>
                      <Sheet.Overlay />
                    </Sheet>
                  </Adapt>
                  <Select.Content zIndex={200000}>
                    <Select.ScrollUpButton />
                    <Select.Viewport>
                      <Select.Group>
                        <Select.Item index={0} value="piece">
                          <Select.ItemText>Piece</Select.ItemText>
                        </Select.Item>
                        <Select.Item index={1} value="kg">
                          <Select.ItemText>Kilogram (kg)</Select.ItemText>
                        </Select.Item>
                        <Select.Item index={2} value="liter">
                          <Select.ItemText>Liter</Select.ItemText>
                        </Select.Item>
                        <Select.Item index={3} value="box">
                          <Select.ItemText>Box</Select.ItemText>
                        </Select.Item>
                        <Select.Item index={4} value="pack">
                          <Select.ItemText>Pack</Select.ItemText>
                        </Select.Item>
                      </Select.Group>
                    </Select.Viewport>
                    <Select.ScrollDownButton />
                  </Select.Content>
                </Select>
                {formErrors.unit && <Text color="$red10" fontSize="$2">{formErrors.unit}</Text>}
              </YStack>

              {/* Minimum Stock Level */}
              <YStack space="$2">
                <Label htmlFor="min-stock">Minimum Stock Level *</Label>
                <Input
                  id="min-stock"
                  placeholder="Enter minimum stock level"
                  value={productForm.min_stock_level.toString()}
                  onChangeText={(text) => {
                    const value = parseInt(text) || 0;
                    setProductForm(prev => ({ ...prev, min_stock_level: value }));
                  }}
                  keyboardType="numeric"
                  borderColor={formErrors.min_stock_level ? '$red10' : '$borderColor'}
                />
                {formErrors.min_stock_level && <Text color="$red10" fontSize="$2">{formErrors.min_stock_level}</Text>}
              </YStack>

              {/* Description */}
              <YStack space="$2">
                <Label htmlFor="product-description">Description</Label>
                <TextArea
                  id="product-description"
                  placeholder="Enter product description (optional)"
                  value={productForm.description || ''}
                  onChangeText={(text) => setProductForm(prev => ({ ...prev, description: text }))}
                  numberOfLines={3}
                />
              </YStack>

              {/* Action Buttons */}
              <XStack space="$3" jc="flex-end" mt="$4">
                <Button
                  variant="outlined"
                  onPress={() => setShowProductSheet(false)}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  onPress={handleCreateProduct}
                  disabled={isSubmitting}
                  icon={isSubmitting ? undefined : Plus}
                >
                  {isSubmitting ? 'Creating...' : 'Create Product'}
                </Button>
              </XStack>
            </YStack>
          </ScrollView>
        </Sheet.Frame>
      </Sheet>
    </ScrollView>
  );
}
