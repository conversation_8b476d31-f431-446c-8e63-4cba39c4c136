import React, { useState, useEffect } from 'react';
import {
  YStack,
  XStack,
  Text,
  Card,
  Button,
  ScrollView,
  Separator,
  H2,
  H3,
  H4,
  Paragraph,

  Sheet,
  Input,
  Label,
  Select,
  Adapt,
  AlertDialog,
  Spinner
} from '@my/ui';
import { RefreshCw, Package, AlertTriangle, TrendingUp, Plus, Search } from '@tamagui/lucide-icons';
import { dashboardService, productService, transactionService, stockAlertService } from './services';
import { initializeDatabase, seedDatabase } from './database';
import { predictionService } from './prediction';
import { InventoryDashboard, ProductWithStock, TransactionWithDetails, StockAlert } from './types';

export function InventoryScreen() {
  const [dashboardData, setDashboardData] = useState<InventoryDashboard | null>(null);
  const [products, setProducts] = useState<ProductWithStock[]>([]);
  const [recentTransactions, setRecentTransactions] = useState<TransactionWithDetails[]>([]);
  const [activeAlerts, setActiveAlerts] = useState<StockAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showProductSheet, setShowProductSheet] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      setLoading(true);
      await initializeDatabase();

      // Check if database has data, if not seed it
      const existingProducts = await productService.getAll();
      if (existingProducts.length === 0) {
        await seedDatabase();
      }

      await loadDashboardData();
    } catch (error) {
      console.error('Failed to initialize app:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadDashboardData = async () => {
    try {
      const [dashboard, productsData, transactions, alerts] = await Promise.all([
        dashboardService.getDashboardData(),
        productService.getWithStock(),
        transactionService.getRecent(10),
        stockAlertService.getActiveAlerts()
      ]);

      setDashboardData(dashboard);
      setProducts(productsData);
      setRecentTransactions(transactions);
      setActiveAlerts(alerts);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.sku.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <YStack flex={1} justifyContent="center" alignItems="center" padding="$4">
        <Spinner size="large" />
        <Text marginTop="$4">Initializing Inventory System...</Text>
      </YStack>
    );
  }

  return (
    <ScrollView flex={1} backgroundColor="$background">
      <YStack padding="$4" space="$4">
        {/* Header */}
        <XStack justifyContent="space-between" alignItems="center">
          <H2>Inventory Management</H2>
          <Button
            icon={RefreshCw}
            onPress={handleRefresh}
            disabled={refreshing}
            variant="outlined"
            size="$3"
          >
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
        </XStack>

        {/* Dashboard Cards */}
        {dashboardData && (
          <XStack space="$3" flexWrap="wrap">
            <Card flex={1} minWidth={150} padding="$3">
              <YStack space="$2">
                <XStack alignItems="center" space="$2">
                  <Package size={20} color="$blue10" />
                  <Text fontSize="$3" color="$gray11">Total Products</Text>
                </XStack>
                <Text fontSize="$6" fontWeight="bold">{dashboardData.total_products}</Text>
              </YStack>
            </Card>

            <Card flex={1} minWidth={150} padding="$3">
              <YStack space="$2">
                <XStack alignItems="center" space="$2">
                  <AlertTriangle size={20} color="$red10" />
                  <Text fontSize="$3" color="$gray11">Low Stock</Text>
                </XStack>
                <Text fontSize="$6" fontWeight="bold" color="$red10">
                  {dashboardData.low_stock_products}
                </Text>
              </YStack>
            </Card>

            <Card flex={1} minWidth={150} padding="$3">
              <YStack space="$2">
                <XStack alignItems="center" space="$2">
                  <TrendingUp size={20} color="$green10" />
                  <Text fontSize="$3" color="$gray11">Total Value</Text>
                </XStack>
                <Text fontSize="$6" fontWeight="bold" color="$green10">
                  ${dashboardData.total_value.toFixed(2)}
                </Text>
              </YStack>
            </Card>
          </XStack>
        )}

        {/* Active Alerts */}
        {activeAlerts.length > 0 && (
          <Card padding="$4">
            <H3 marginBottom="$3">Active Alerts</H3>
            <YStack space="$2">
              {activeAlerts.slice(0, 3).map((alert) => (
                <XStack key={alert.id} alignItems="center" space="$3" padding="$2" backgroundColor="$red2" borderRadius="$3">
                  <AlertTriangle size={16} color="$red10" />
                  <YStack flex={1}>
                    <Text fontSize="$3" fontWeight="600">Low Stock Alert</Text>
                    <Text fontSize="$2" color="$gray11">
                      Product ID: {alert.product_id} - Threshold: {alert.threshold}
                    </Text>
                  </YStack>
                  <Text
                    backgroundColor="$red5"
                    color="$red11"
                    paddingHorizontal="$2"
                    paddingVertical="$1"
                    borderRadius="$2"
                    fontSize="$1"
                  >
                    {alert.alert_type}
                  </Text>
                </XStack>
              ))}
              {activeAlerts.length > 3 && (
                <Text fontSize="$2" color="$gray11" textAlign="center">
                  +{activeAlerts.length - 3} more alerts
                </Text>
              )}
            </YStack>
          </Card>
        )}

        {/* Products Section */}
        <Card padding="$4">
          <XStack justifyContent="space-between" alignItems="center" marginBottom="$3">
            <H3>Products</H3>
            <Button
              icon={Plus}
              onPress={() => setShowProductSheet(true)}
              size="$3"
            >
              Add Product
            </Button>
          </XStack>

          {/* Search */}
          <XStack space="$2" marginBottom="$3">
            <Input
              flex={1}
              placeholder="Search products..."
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            <Button icon={Search} variant="outlined" />
          </XStack>

          <YStack space="$2">
            {filteredProducts.slice(0, 5).map((product) => (
              <Card key={product.id} padding="$3" backgroundColor="$gray2">
                <XStack justifyContent="space-between" alignItems="center">
                  <YStack flex={1}>
                    <Text fontSize="$4" fontWeight="600">{product.name}</Text>
                    <Text fontSize="$3" color="$gray11">SKU: {product.sku}</Text>
                    <Text fontSize="$2" color="$gray10">
                      Stock: {product.available_stock} {product.unit}
                    </Text>
                  </YStack>
                  <YStack alignItems="flex-end" space="$1">
                    <Text
                      backgroundColor={product.available_stock <= product.min_stock_level ? "$red5" : "$green5"}
                      color={product.available_stock <= product.min_stock_level ? "$red11" : "$green11"}
                      paddingHorizontal="$2"
                      paddingVertical="$1"
                      borderRadius="$2"
                      fontSize="$1"
                    >
                      {product.available_stock <= product.min_stock_level ? 'Low Stock' : 'In Stock'}
                    </Text>
                    <Text fontSize="$2" color="$gray11">
                      Min: {product.min_stock_level}
                    </Text>
                  </YStack>
                </XStack>
              </Card>
            ))}
            {filteredProducts.length > 5 && (
              <Text fontSize="$2" color="$gray11" textAlign="center">
                +{filteredProducts.length - 5} more products
              </Text>
            )}
          </YStack>
        </Card>

        {/* Recent Transactions */}
        <Card padding="$4">
          <H3 marginBottom="$3">Recent Transactions</H3>
          <YStack space="$2">
            {recentTransactions.slice(0, 5).map((transaction) => (
              <XStack key={transaction.id} justifyContent="space-between" alignItems="center" padding="$2">
                <YStack flex={1}>
                  <Text fontSize="$3" fontWeight="600">{transaction.product_name}</Text>
                  <Text fontSize="$2" color="$gray11">
                    {transaction.type} - {transaction.quantity} {transaction.unit}
                  </Text>
                  <Text fontSize="$1" color="$gray10">
                    {new Date(transaction.created_at).toLocaleDateString()}
                  </Text>
                </YStack>
                <Text
                  backgroundColor={transaction.type === 'INBOUND' ? "$green5" : "$red5"}
                  color={transaction.type === 'INBOUND' ? "$green11" : "$red11"}
                  paddingHorizontal="$2"
                  paddingVertical="$1"
                  borderRadius="$2"
                  fontSize="$1"
                >
                  {transaction.type}
                </Text>
              </XStack>
            ))}
          </YStack>
        </Card>
      </YStack>

      {/* Add Product Sheet */}
      <Sheet
        modal
        open={showProductSheet}
        onOpenChange={setShowProductSheet}
        snapPoints={[85]}
        dismissOnSnapToBottom
      >
        <Sheet.Overlay />
        <Sheet.Handle />
        <Sheet.Frame padding="$4">
          <YStack space="$4">
            <H3>Add New Product</H3>
            <Text color="$gray11">Feature coming soon...</Text>
            <Button onPress={() => setShowProductSheet(false)}>
              Close
            </Button>
          </YStack>
        </Sheet.Frame>
      </Sheet>
    </ScrollView>
  );
}
