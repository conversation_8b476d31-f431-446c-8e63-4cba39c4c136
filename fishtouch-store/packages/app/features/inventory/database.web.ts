// Web-compatible database implementation using localStorage for demo purposes
// In a real app, you'd use IndexedDB or connect to a backend API

import { CREATE_TABLES_SQL } from './types';

// Mock SQLite interface for web
interface WebSQLiteDatabase {
  execAsync: (sql: string) => Promise<void>;
  getAllAsync: (sql: string, params?: any[]) => Promise<any[]>;
  getFirstAsync: (sql: string, params?: any[]) => Promise<any | null>;
  runAsync: (sql: string, params?: any[]) => Promise<{ lastInsertRowId?: number; changes: number }>;
  closeAsync: () => Promise<void>;
}

interface WebSQLiteRunResult {
  lastInsertRowId?: number;
  changes: number;
}

// Simple in-memory database for web demo
class WebDatabase implements WebSQLiteDatabase {
  private data: { [tableName: string]: any[] } = {};
  private nextId: { [tableName: string]: number } = {};

  async execAsync(sql: string): Promise<void> {
    // Handle CREATE TABLE statements
    if (sql.includes('CREATE TABLE')) {
      const tableMatch = sql.match(/CREATE TABLE (?:IF NOT EXISTS )?(\w+)/);
      if (tableMatch) {
        const tableName = tableMatch[1];
        if (!this.data[tableName]) {
          this.data[tableName] = [];
          this.nextId[tableName] = 1;
        }
      }
    }
    // Handle other SQL statements as needed
  }

  async getAllAsync(sql: string, params: any[] = []): Promise<any[]> {
    // Simple query parsing for demo
    if (sql.includes('SELECT * FROM products')) {
      return this.data.products || [];
    }
    if (sql.includes('SELECT * FROM inventory')) {
      return this.data.inventory || [];
    }
    if (sql.includes('SELECT * FROM transactions')) {
      return this.data.transactions || [];
    }
    if (sql.includes('SELECT * FROM stock_alerts')) {
      return this.data.stock_alerts || [];
    }
    if (sql.includes('SELECT * FROM packages')) {
      return this.data.packages || [];
    }
    if (sql.includes('SELECT * FROM batches')) {
      return this.data.batches || [];
    }
    if (sql.includes('SELECT * FROM shipments')) {
      return this.data.shipments || [];
    }
    
    // Handle COUNT queries
    if (sql.includes('COUNT(*)')) {
      if (sql.includes('FROM products')) {
        return [{ count: (this.data.products || []).length }];
      }
    }
    
    return [];
  }

  async getFirstAsync(sql: string, params: any[] = []): Promise<any | null> {
    const results = await this.getAllAsync(sql, params);
    return results.length > 0 ? results[0] : null;
  }

  async runAsync(sql: string, params: any[] = []): Promise<WebSQLiteRunResult> {
    // Handle INSERT statements
    if (sql.includes('INSERT INTO')) {
      const tableMatch = sql.match(/INSERT INTO (\w+)/);
      if (tableMatch) {
        const tableName = tableMatch[1];
        if (!this.data[tableName]) {
          this.data[tableName] = [];
          this.nextId[tableName] = 1;
        }
        
        const id = this.nextId[tableName]++;
        const newRecord = { id, ...this.parseInsertValues(sql, params) };
        this.data[tableName].push(newRecord);
        
        return { lastInsertRowId: id, changes: 1 };
      }
    }
    
    // Handle UPDATE statements
    if (sql.includes('UPDATE')) {
      return { changes: 1 };
    }
    
    // Handle DELETE statements
    if (sql.includes('DELETE')) {
      return { changes: 1 };
    }
    
    return { changes: 0 };
  }

  async closeAsync(): Promise<void> {
    // Nothing to close for in-memory database
  }

  private parseInsertValues(sql: string, params: any[]): any {
    // Simple parsing for demo - in real implementation you'd parse SQL properly
    const record: any = {};
    
    // For demo purposes, create some sample data based on table
    if (sql.includes('products')) {
      record.name = params[0] || 'Sample Product';
      record.sku = params[1] || 'SKU001';
      record.description = params[2] || 'Sample description';
      record.unit = params[3] || 'piece';
      record.min_stock_level = params[4] || 10;
      record.created_at = new Date().toISOString();
    }
    
    return record;
  }
}

let db: WebDatabase | null = null;

export const initializeDatabase = async (): Promise<WebDatabase> => {
  if (db) {
    return db;
  }

  try {
    db = new WebDatabase();
    
    // Create tables
    await db.execAsync(CREATE_TABLES_SQL);
    
    console.log('Web database initialized successfully');
    return db;
  } catch (error) {
    console.error('Failed to initialize web database:', error);
    throw error;
  }
};

export const getDatabase = async (): Promise<WebDatabase> => {
  if (!db) {
    return await initializeDatabase();
  }
  return db;
};

export const closeDatabase = async (): Promise<void> => {
  if (db) {
    await db.closeAsync();
    db = null;
  }
};

// Utility function to execute queries with error handling
export const executeQuery = async <T>(
  query: string,
  params: any[] = []
): Promise<T[]> => {
  try {
    const database = await getDatabase();
    const result = await database.getAllAsync(query, params);
    return result as T[];
  } catch (error) {
    console.error('Query execution failed:', error);
    console.error('Query:', query);
    console.error('Params:', params);
    throw error;
  }
};

// Utility function to execute single row queries
export const executeQueryFirst = async <T>(
  query: string,
  params: any[] = []
): Promise<T | null> => {
  try {
    const database = await getDatabase();
    const result = await database.getFirstAsync(query, params);
    return result as T | null;
  } catch (error) {
    console.error('Query execution failed:', error);
    console.error('Query:', query);
    console.error('Params:', params);
    throw error;
  }
};

// Utility function to execute insert/update/delete queries
export const executeUpdate = async (
  query: string,
  params: any[] = []
): Promise<WebSQLiteRunResult> => {
  try {
    const database = await getDatabase();
    const result = await database.runAsync(query, params);
    return result;
  } catch (error) {
    console.error('Update execution failed:', error);
    console.error('Query:', query);
    console.error('Params:', params);
    throw error;
  }
};

// Transaction wrapper for multiple operations
export const executeTransaction = async <T>(
  operations: (db: WebDatabase) => Promise<T>
): Promise<T> => {
  const database = await getDatabase();
  
  try {
    const result = await operations(database);
    return result;
  } catch (error) {
    console.error('Transaction failed:', error);
    throw error;
  }
};

// Database seeding function for development/testing
export const seedDatabase = async (): Promise<void> => {
  try {
    console.log('Seeding web database with sample data...');
    
    // Create sample data directly in the web database
    const database = await getDatabase();
    
    // Sample products
    const sampleProducts = [
      { id: 1, name: 'Fish Food Premium', sku: 'FF001', unit: 'kg', min_stock_level: 10, created_at: new Date().toISOString() },
      { id: 2, name: 'Aquarium Filter', sku: 'AF001', unit: 'piece', min_stock_level: 5, created_at: new Date().toISOString() },
      { id: 3, name: 'Water Conditioner', sku: 'WC001', unit: 'liter', min_stock_level: 20, created_at: new Date().toISOString() },
      { id: 4, name: 'LED Light Strip', sku: 'LS001', unit: 'piece', min_stock_level: 3, created_at: new Date().toISOString() },
      { id: 5, name: 'Gravel Substrate', sku: 'GS001', unit: 'kg', min_stock_level: 50, created_at: new Date().toISOString() }
    ];

    // Directly populate the data for web demo
    (database as any).data.products = sampleProducts;
    (database as any).nextId.products = 6;

    console.log('Web database seeded successfully');
  } catch (error) {
    console.error('Failed to seed web database:', error);
    throw error;
  }
};

// Clear all data (for testing purposes)
export const clearDatabase = async (): Promise<void> => {
  try {
    const database = await getDatabase();
    (database as any).data = {};
    (database as any).nextId = {};
    console.log('Web database cleared successfully');
  } catch (error) {
    console.error('Failed to clear web database:', error);
    throw error;
  }
};
