import { Platform } from 'react-native';
import { CREATE_TABLES_SQL } from './types';

// Database interface for cross-platform compatibility
interface DatabaseInterface {
  execAsync: (sql: string) => Promise<void>;
  getAllAsync: (sql: string, params?: any[]) => Promise<any[]>;
  getFirstAsync: (sql: string, params?: any[]) => Promise<any | null>;
  runAsync: (sql: string, params?: any[]) => Promise<{ lastInsertRowId?: number; changes: number }>;
  closeAsync: () => Promise<void>;
}

interface RunResult {
  lastInsertRowId?: number;
  changes: number;
}

let db: DatabaseInterface | null = null;

export const initializeDatabase = async (): Promise<DatabaseInterface> => {
  if (db) {
    return db;
  }

  try {
    if (Platform.OS === 'web') {
      // Use web-compatible database
      const { initializeDatabase: initWebDb } = await import('./database.web');
      db = await initWebDb();
    } else {
      // Use expo-sqlite for native
      const SQLite = await import('expo-sqlite');
      const nativeDb = await SQLite.openDatabaseAsync('inventory.db');

      // Enable foreign keys
      await nativeDb.execAsync('PRAGMA foreign_keys = ON;');

      // Create tables
      await nativeDb.execAsync(CREATE_TABLES_SQL);

      // Create wrapper to match interface
      db = {
        execAsync: (sql: string) => nativeDb.execAsync(sql),
        getAllAsync: (sql: string, params: any[] = []) => nativeDb.getAllAsync(sql, params),
        getFirstAsync: (sql: string, params: any[] = []) => nativeDb.getFirstAsync(sql, params),
        runAsync: (sql: string, params: any[] = []) => nativeDb.runAsync(sql, params),
        closeAsync: () => nativeDb.closeAsync()
      };
    }

    console.log('Database initialized successfully');
    return db!;
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
};

export const getDatabase = async (): Promise<DatabaseInterface> => {
  if (!db) {
    return await initializeDatabase();
  }
  return db;
};

export const closeDatabase = async (): Promise<void> => {
  if (db) {
    await db.closeAsync();
    db = null;
  }
};

// Utility function to execute queries with error handling
export const executeQuery = async <T>(
  query: string,
  params: any[] = []
): Promise<T[]> => {
  try {
    const database = await getDatabase();
    const result = await database.getAllAsync(query, params);
    return result as T[];
  } catch (error) {
    console.error('Query execution failed:', error);
    console.error('Query:', query);
    console.error('Params:', params);
    throw error;
  }
};

// Utility function to execute single row queries
export const executeQueryFirst = async <T>(
  query: string,
  params: any[] = []
): Promise<T | null> => {
  try {
    const database = await getDatabase();
    const result = await database.getFirstAsync(query, params);
    return result as T | null;
  } catch (error) {
    console.error('Query execution failed:', error);
    console.error('Query:', query);
    console.error('Params:', params);
    throw error;
  }
};

// Utility function to execute insert/update/delete queries
export const executeUpdate = async (
  query: string,
  params: any[] = []
): Promise<RunResult> => {
  try {
    const database = await getDatabase();
    const result = await database.runAsync(query, params);
    return result;
  } catch (error) {
    console.error('Update execution failed:', error);
    console.error('Query:', query);
    console.error('Params:', params);
    throw error;
  }
};

// Transaction wrapper for multiple operations
export const executeTransaction = async <T>(
  operations: (db: DatabaseInterface) => Promise<T>
): Promise<T> => {
  const database = await getDatabase();

  try {
    await database.execAsync('BEGIN TRANSACTION;');
    const result = await operations(database);
    await database.execAsync('COMMIT;');
    return result;
  } catch (error) {
    await database.execAsync('ROLLBACK;');
    console.error('Transaction failed:', error);
    throw error;
  }
};

// Database seeding function for development/testing
export const seedDatabase = async (): Promise<void> => {
  try {
    console.log('Seeding database with sample data...');

    // Sample products
    const sampleProducts = [
      { name: 'Fish Food Premium', sku: 'FF001', unit: 'kg', min_stock_level: 10 },
      { name: 'Aquarium Filter', sku: 'AF001', unit: 'piece', min_stock_level: 5 },
      { name: 'Water Conditioner', sku: 'WC001', unit: 'liter', min_stock_level: 20 },
      { name: 'LED Light Strip', sku: 'LS001', unit: 'piece', min_stock_level: 3 },
      { name: 'Gravel Substrate', sku: 'GS001', unit: 'kg', min_stock_level: 50 }
    ];

    for (const product of sampleProducts) {
      await executeUpdate(
        `INSERT OR IGNORE INTO products (name, sku, unit, min_stock_level) 
         VALUES (?, ?, ?, ?)`,
        [product.name, product.sku, product.unit, product.min_stock_level]
      );
    }

    // Sample packages
    const starterPackage = await executeUpdate(
      `INSERT OR IGNORE INTO packages (name, description) 
       VALUES (?, ?)`,
      ['Aquarium Starter Kit', 'Complete kit for new aquarium setup']
    );

    if (starterPackage.lastInsertRowId) {
      // Add items to the starter package
      const packageItems = [
        { product_id: 1, quantity: 2 }, // Fish Food
        { product_id: 2, quantity: 1 }, // Filter
        { product_id: 3, quantity: 1 }, // Water Conditioner
        { product_id: 4, quantity: 1 }, // LED Light
        { product_id: 5, quantity: 5 }  // Gravel
      ];

      for (const item of packageItems) {
        await executeUpdate(
          `INSERT OR IGNORE INTO package_items (package_id, product_id, quantity) 
           VALUES (?, ?, ?)`,
          [starterPackage.lastInsertRowId, item.product_id, item.quantity]
        );
      }
    }

    // Sample batches and inventory
    const sampleBatches = [
      { product_id: 1, batch_number: 'FF001-2024-01', supplier: 'AquaSupply Co', cost_per_unit: 25.50, quantity: 100 },
      { product_id: 2, batch_number: 'AF001-2024-01', supplier: 'FilterTech Ltd', cost_per_unit: 45.00, quantity: 20 },
      { product_id: 3, batch_number: 'WC001-2024-01', supplier: 'ChemAqua Inc', cost_per_unit: 12.75, quantity: 50 },
      { product_id: 4, batch_number: 'LS001-2024-01', supplier: 'LightCorp', cost_per_unit: 89.99, quantity: 15 },
      { product_id: 5, batch_number: 'GS001-2024-01', supplier: 'SubstratePro', cost_per_unit: 8.50, quantity: 200 }
    ];

    for (const batch of sampleBatches) {
      const batchResult = await executeUpdate(
        `INSERT OR IGNORE INTO batches (product_id, batch_number, supplier, cost_per_unit) 
         VALUES (?, ?, ?, ?)`,
        [batch.product_id, batch.batch_number, batch.supplier, batch.cost_per_unit]
      );

      if (batchResult.lastInsertRowId) {
        // Add initial inventory
        await executeUpdate(
          `INSERT OR IGNORE INTO inventory (product_id, batch_id, quantity, location) 
           VALUES (?, ?, ?, ?)`,
          [batch.product_id, batchResult.lastInsertRowId, batch.quantity, 'Main Warehouse']
        );

        // Add initial inbound transaction
        await executeUpdate(
          `INSERT INTO transactions (type, product_id, batch_id, quantity, reference_number, notes) 
           VALUES (?, ?, ?, ?, ?, ?)`,
          ['INBOUND', batch.product_id, batchResult.lastInsertRowId, batch.quantity, `PO-2024-${batch.product_id.toString().padStart(3, '0')}`, 'Initial stock']
        );
      }
    }

    // Sample stock alerts
    const stockAlerts = [
      { product_id: 1, alert_type: 'LOW_STOCK', threshold: 10 },
      { product_id: 2, alert_type: 'LOW_STOCK', threshold: 5 },
      { product_id: 3, alert_type: 'LOW_STOCK', threshold: 20 },
      { product_id: 4, alert_type: 'LOW_STOCK', threshold: 3 },
      { product_id: 5, alert_type: 'LOW_STOCK', threshold: 50 }
    ];

    for (const alert of stockAlerts) {
      await executeUpdate(
        `INSERT OR IGNORE INTO stock_alerts (product_id, alert_type, threshold) 
         VALUES (?, ?, ?)`,
        [alert.product_id, alert.alert_type, alert.threshold]
      );
    }

    console.log('Database seeded successfully');
  } catch (error) {
    console.error('Failed to seed database:', error);
    throw error;
  }
};

// Clear all data (for testing purposes)
export const clearDatabase = async (): Promise<void> => {
  try {
    const tables = [
      'transactions',
      'stock_alerts',
      'inventory',
      'batches',
      'package_items',
      'packages',
      'products',
      'shipments'
    ];

    for (const table of tables) {
      await executeUpdate(`DELETE FROM ${table}`);
    }

    console.log('Database cleared successfully');
  } catch (error) {
    console.error('Failed to clear database:', error);
    throw error;
  }
};
